FROM crpi-6hsqzgqhxpg54bm7.cn-beijing.personal.cr.aliyuncs.com/nkp/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

USER root
# 安装 fontconfig 以便使用 fc-cache
RUN apt-get update && apt-get install -y fontconfig curl

ENV ASPNETCORE_URLS=http://+:80

FROM --platform=$BUILDPLATFORM crpi-6hsqzgqhxpg54bm7.cn-beijing.personal.cr.aliyuncs.com/nkp/sdk:8.0 AS build
ARG configuration=Release

WORKDIR /src
COPY ["Settlement.WebApi/Settlement.WebApi.csproj", "Settlement.WebApi/"]
COPY ["nuget.config", "Settlement.WebApi/"]
COPY ["Settlement.Infrastructure/Settlement.Infrastructure.csproj", "Settlement.Infrastructure/"]
COPY ["Settlement.Domain/Settlement.Domain.csproj", "Settlement.Domain/"]
COPY ["Settlement.Application/Settlement.Application.csproj", "Settlement.Application/"]
COPY ["Public/Config/Config.csproj", "Public/Config/"]
COPY ["Public/Entity/Entity.csproj", "Public/Entity/"]
COPY ["Public/Infrastructure/Infrastructure.csproj", "Public/Infrastructure/"]

RUN dotnet restore "Settlement.WebApi/Settlement.WebApi.csproj" --configfile "Settlement.WebApi/nuget.config"
    
COPY . .

WORKDIR "/src/Settlement.WebApi"
RUN dotnet build "Settlement.WebApi.csproj" -c Release -o /app/build --no-restore

FROM build AS publish
RUN dotnet publish "Settlement.WebApi.csproj" -c Release -o /app/publish --no-restore

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

 
ENTRYPOINT ["dotnet", "Settlement.WebApi.dll"]
