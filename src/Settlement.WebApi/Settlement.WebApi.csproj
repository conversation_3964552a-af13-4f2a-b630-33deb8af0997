<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <PropertyGroup>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\net8.0\Settlement.WebApi.xml</DocumentationFile>
    <NoWarn>1701;1702;1591;NU1803;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspectCore.Extensions.DependencyInjection" Version="2.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.11" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Settlement.Application\Settlement.Application.csproj" />
    <ProjectReference Include="..\Settlement.Infrastructure\Settlement.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Service\" />
  </ItemGroup>

</Project>
