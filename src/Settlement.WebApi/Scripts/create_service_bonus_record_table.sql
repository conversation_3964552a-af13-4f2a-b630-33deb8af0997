-- 创建服务奖金发放记录表（在nkp数据库中）
-- 请在nkp数据库中执行此脚本
CREATE TABLE IF NOT EXISTS `settlement_service_bonus_record` (
    `Id` varchar(255) NOT NULL COMMENT '主键ID',
    `OrderNumber` varchar(64) NOT NULL COMMENT '订单号(来自转账请求)',
    `ContractCode` varchar(64) NOT NULL COMMENT '合同编号',
    `AgentHrNo` varchar(32) NOT NULL COMMENT '经办人钉钉工号',
    `BeneficiaryHrNo` varchar(32) NOT NULL COMMENT '受益人钉钉工号',
    `IssuedAmount` bigint NOT NULL COMMENT '发放金额(单位:分)',
    `Remark` varchar(512) NULL COMMENT '说明备注',
    `Status` int NOT NULL DEFAULT 0 COMMENT '发起状态:0=待发起,1=发起成功,2=发起失败,3=处理中,4=已完成,5=已取消',
    `ResponseCode` int NULL COMMENT '第三方系统响应码',
    `ResponseMessage` varchar(512) NULL COMMENT '第三方系统响应消息',
    `ResponseData` text NULL COMMENT '第三方系统返回的数据',
    `InitiatedTime` datetime(6) NULL COMMENT '发起时间',
    `CompletedTime` datetime(6) NULL COMMENT '完成时间',
    `RetryCount` int NOT NULL DEFAULT 0 COMMENT '重试次数',
    `LastErrorMessage` varchar(1024) NULL COMMENT '最后错误信息',
    `IsDeleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    `CreatedBy` varchar(64) NULL COMMENT '创建人',
    `UpdatedBy` varchar(64) NULL COMMENT '更新人',
    PRIMARY KEY (`Id`),
    UNIQUE INDEX `UK_service_bonus_record_order_number` (`OrderNumber`),
    INDEX `IX_service_bonus_record_contract_code` (`ContractCode`),
    INDEX `IX_service_bonus_record_agent_hr_no` (`AgentHrNo`),
    INDEX `IX_service_bonus_record_beneficiary_hr_no` (`BeneficiaryHrNo`),
    INDEX `IX_service_bonus_record_status` (`Status`),
    INDEX `IX_service_bonus_record_created_time` (`CreatedTime`),
    INDEX `IX_service_bonus_record_is_deleted` (`IsDeleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务奖金发放记录表';


ALTER TABLE `staffing`.`settlement_service_bonus_record`
    ADD COLUMN `MerchantId` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户id ' AFTER `ContractCode`;