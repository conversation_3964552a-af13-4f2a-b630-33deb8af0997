-- 创建本地消息表（在nkp数据库中）
-- 请在nkp数据库中执行此脚本
CREATE TABLE IF NOT EXISTS `outbox_message` (
    `Id` varchar(255) NOT NULL,
    `MessageType` varchar(255) NOT NULL COMMENT '消息类型',
    `Content` longtext NOT NULL COMMENT '消息内容（JSON格式）',
    `ApiType` varchar(255) NOT NULL COMMENT '目标API类型',
    `Status` int NOT NULL DEFAULT 0 COMMENT '消息状态：0=待处理，1=处理中，2=已完成，3=失败',
    `RetryCount` int NOT NULL DEFAULT 0 COMMENT '重试次数',
    `MaxRetryCount` int NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    `NextRetryTime` datetime(6) NULL COMMENT '下次重试时间',
    `ErrorMessage` longtext NULL COMMENT '错误信息',
    `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    `ProcessedTime` datetime(6) NULL COMMENT '处理时间',
    PRIMARY KEY (`Id`),
    INDEX `IX_outbox_message_status` (`Status`),
    INDEX `IX_outbox_message_created_time` (`CreatedTime`),
    INDEX `IX_outbox_message_next_retry_time` (`NextRetryTime`),
    INDEX `IX_outbox_message_api_type` (`ApiType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='本地消息表';
