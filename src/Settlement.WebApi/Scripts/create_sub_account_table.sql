-- 创建子账户表（在nkp数据库中）
-- 请在nkp数据库中执行此脚本
CREATE TABLE IF NOT EXISTS `sub_account` (
    `Id` varchar(255) NOT NULL,
    `ChannelNo` varchar(64) NOT NULL COMMENT '渠道号',
    `MerchantNo` varchar(36) NOT NULL COMMENT '平台级商户号',
    `ExternalUserNo` varchar(64) NOT NULL COMMENT '外部用户号(项目id)',
    `AppSerialNumber` varchar(64) NOT NULL COMMENT '外部流水号',
    `Mobile` varchar(512) NULL COMMENT '手机号(SM4 ECB加密)',
    `UserType` varchar(2) NOT NULL COMMENT '用户类型(01:自然人,02:个体工商户,03:企业商户,04:实体卡)',
    `SubAccountType` varchar(2) NULL COMMENT '子账户类型',
    `SubProductCode` varchar(20) NULL COMMENT '产品类型(二级子产品类型)',
    `ParentAccountNo` varchar(64) NULL COMMENT '上级账户号',
    `MerchantName` varchar(512) NULL COMMENT '商户名称(SM4 ECB加密)',
    `CertificateValidDateBegin` varchar(8) NULL COMMENT '证件有效期起始日期(yyyyMMdd)',
    `CertificateValidDateEnd` varchar(8) NULL COMMENT '证件有效期截至日期(yyyyMMdd,永久填********)',
    `CorporateCertificateType` varchar(2) NULL COMMENT '企业证件类型',
    `CorporateCertificateNo` varchar(512) NULL COMMENT '企业证件号码(SM4 ECB加密)',
    `MerchantCertificateValidDate` varchar(8) NULL COMMENT '企业证件有效期(yyyyMMdd)',
    `RiskInfoEncrypted` varchar(2048) NULL COMMENT '风险信息(SM4 ECB加密)',
    `Key` varchar(512) NULL COMMENT '加密key(使用账户公钥SM2加密)',
    `MerchantReserved` varchar(512) NULL COMMENT '商户保留域',
    `AccountNumber` varchar(64) NULL COMMENT '账户号(客户身份识别码)',
    `OpenStatus` int NOT NULL DEFAULT 0 COMMENT '开户状态:0=审核中,1=成功,2=失败',
    `FailReason` varchar(256) NULL COMMENT '失败原因',
    `ErrorCode` varchar(32) NULL COMMENT '错误码',
    `ErrorInfo` varchar(512) NULL COMMENT '错误码描述',
    `CreatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `UpdatedTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
    `CreatedBy` varchar(64) NULL COMMENT '创建人',
    `UpdatedBy` varchar(64) NULL COMMENT '更新人',
    `Remark` varchar(512) NULL COMMENT '备注',
    `IsDeleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`Id`),
    UNIQUE INDEX `UK_sub_account_external_user_no` (`ExternalUserNo`),
    UNIQUE INDEX `UK_sub_account_app_serial_number` (`AppSerialNumber`),
    INDEX `IX_sub_account_channel_no` (`ChannelNo`),
    INDEX `IX_sub_account_merchant_no` (`MerchantNo`),
    INDEX `IX_sub_account_user_type` (`UserType`),
    INDEX `IX_sub_account_open_status` (`OpenStatus`),
    INDEX `IX_sub_account_account_number` (`AccountNumber`),
    INDEX `IX_sub_account_created_time` (`CreatedTime`),
    INDEX `IX_sub_account_is_deleted` (`IsDeleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='子账户表';
