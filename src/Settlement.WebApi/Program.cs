using System.Reflection;
using System.Text;
using System.Web;
using AspectCore.Extensions.DependencyInjection;
using Config;
using Entity;
using Flurl.Http;
using IGeekFan.AspNetCore.Knife4jUI;
using Infrastructure.Common;
using Infrastructure.DataHandling;
using Infrastructure.Extend;
using Infrastructure.Middleware;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Events;
using Settlement.Application.Service;
using Settlement.Infrastructure.Common;
using Settlement.Infrastructure.Proxy;
using Settlement.WebApi.Api.admin;
using Settlement.WebApi.Api.@internal;
using Settlement.WebApi.Api.settlement;
using Settlement.WebApi.Hosting;
using JsonOptions = Microsoft.AspNetCore.Http.Json.JsonOptions;

var builder = WebApplication.CreateBuilder(args);


builder.Services
    .Configure<KestrelServerOptions>(x => x.AllowSynchronousIO = true)
    .Configure<IISServerOptions>(x => x.AllowSynchronousIO = true);

builder.Services.AddControllers();

//aop支持
builder.Host.UseServiceProviderFactory(new DynamicProxyServiceProviderFactory());

builder.Services.AddMemoryCache();

//配置
builder.Services.Configure<ConfigManager>(builder.Configuration.GetSection("Settings"));

//json格式化
builder.Services.Configure<JsonOptions>(options =>
{
    options.SerializerOptions.Converters.Add(new DateTimeConverterForYYYYMMDD());
    options.SerializerOptions.Converters.Add(new DateTimeConverterForYYYYMMDD2());
});

var connectionString = builder.Configuration.GetConnectionString("sqlCon");
builder.Services.AddDbContextPool<MainContext>(
    options => options.UseMySql(connectionString, new MySqlServerVersion(Constants.MySqlVersion))
        .ConfigureWarnings(builder =>
            builder.Ignore(CoreEventId.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning)));

builder.Services.AddDbContextFactory<MainContext>(
    options => options.UseMySql(connectionString, new MySqlServerVersion(Constants.MySqlVersion))
        .ConfigureWarnings(builder =>
            builder.Ignore(CoreEventId.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning)));


//诺快聘
var nkpCon = builder.Configuration.GetConnectionString("nkp");
builder.Services.AddDbContextPool<NkpContext>(
    options => options.UseMySql(nkpCon, new MySqlServerVersion(Constants.MySqlVersion), x => x.UseNetTopologySuite())
        .ConfigureWarnings(builder =>
            builder.Ignore(CoreEventId.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning)));

builder.Services.AddDbContextFactory<NkpContext>(
    options => options.UseMySql(nkpCon, new MySqlServerVersion(Constants.MySqlVersion), x => x.UseNetTopologySuite())
        .ConfigureWarnings(builder =>
            builder.Ignore(CoreEventId.PossibleIncorrectRequiredNavigationWithQueryFilterInteractionWarning)));

var cfg = builder.Configuration.GetSection("Settings").Get<ConfigManager>();

builder.Services.AddScoped<RequestContext>();

//自动注入
builder.Services.AutoDependencyInjection();

// 注册NovaPinApi服务
builder.Services.AddHttpClient<NovaPinApi>();

MyRedis.Client = new FreeRedis.RedisClient(cfg!.RedisAddress);
MyRedis.Client.Serialize = obj => ServiceStack.Text.JsonSerializer.SerializeToString(obj);
MyRedis.Client.Deserialize = (json, type) => ServiceStack.Text.JsonSerializer.DeserializeFromString(json, type);

var esUrl = cfg.ElasticSearch!.Address!;
var esUrlWithCredentials =
    $"http://{cfg.ElasticSearch.UserName}:{HttpUtility.UrlEncode(cfg.ElasticSearch.Password)}@{new Uri(esUrl).Host}:{new Uri(esUrl).Port}";

builder.Logging.ClearProviders();
Log.Logger = new LoggerConfiguration()
    .Enrich.FromLogContext() // 增加上下文信息
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)      
    .WriteTo.Console() // 控制台输出
    .WriteTo.Elasticsearch(new Serilog.Sinks.Elasticsearch.ElasticsearchSinkOptions(new Uri(esUrlWithCredentials))
    {
        AutoRegisterTemplate = true, // 自动注册索引模板
        IndexFormat = "nkplogs-{0:yyyy.MM.dd}", // 日志索引格式
    }).CreateLogger();

builder.Host.UseSerilog();

builder.Services.AddCors(options =>
{
    options.AddPolicy("cors",
        builder => builder.AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod()
    );
});

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("admin", new OpenApiInfo() { Title = "平台端", Version = "1.0" });
    c.SwaggerDoc("settlement", new OpenApiInfo() { Title = "结算", Version = "1.0" });
    c.SwaggerDoc("internal", new OpenApiInfo() { Title = "结算", Version = "1.0" });

    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    var xmlPathInfra = Path.Combine(AppContext.BaseDirectory, "Settlement.Infrastructure.xml");
    var xmlPathDomain = Path.Combine(AppContext.BaseDirectory, "Settlement.Domain.xml");
    var xmlPathConfig = Path.Combine(AppContext.BaseDirectory, "Config.xml");

    c.IncludeXmlComments(xmlPath, true);
    if (File.Exists(xmlPathInfra))
    {
        c.IncludeXmlComments(xmlPathInfra, true);
    }

    if (File.Exists(xmlPathDomain))
    {
        c.IncludeXmlComments(xmlPathDomain, true);
    }

    if (File.Exists(xmlPathConfig))
    {
        c.IncludeXmlComments(xmlPathConfig, true);
    }

    // 为枚举添加描述（可选）
    c.SchemaFilter<EnumSchemaFilter>();
});

//雪花算法worker注册
await Tools.SnowflakeWorkerInit(builder.Environment.IsProduction());

Tools.NextId();

// 注册本地消息表后台服务
//builder.Services.AddHostedService<OutboxBackgroundService>();
//builder.Services.AddHostedService<DailyWorker>();
builder.Services.AddHostedService<DailyWorker>();

var app = builder.Build();


// app.UseRouting();
app.UseCors("cors");
app.UseStaticFiles();

app.UseAuthentication();

app.UseSerilogRequestLogging();

if (!app.Environment.IsProduction())
{
    // Configure the HTTP request pipeline.
    app.UseSwagger();
    app.UseKnife4UI(c =>
    {
        c.RoutePrefix = "swagger";
        c.SwaggerEndpoint("/admin/swagger.json", "管理端");
        c.SwaggerEndpoint("/settlement/swagger.json", "结算服务");
        c.SwaggerEndpoint("/internal/swagger.json", "内部服务");
    });
}   

app.MapControllers();

app.UseHttpsRedirection();
app.UseMiddleware<RequRespLog>();
app.UseMiddleware<AuthorizationMiddleware>();
app.UseMiddleware<ExceptionHandler>();




var mainApi = app.MapGroup(string.Empty);


//内部api
var internalApi = mainApi.MapGroup(Constants.ApiGroup.InternalApi)
    .WithGroupName("internal")
    .WithMetadata(new ApiMeta { AuthType = ApiAuthType.Internal });

internalApi.InternalApi();
internalApi.InternalNdnApi();

//settlement api
var settlementApi = mainApi.MapGroup(Constants.ApiGroup.SettlementApi)
    .WithGroupName("settlement")
    .WithMetadata(new ApiMeta { AuthType = ApiAuthType.Nkp });

settlementApi.MapNoahApi();
settlementApi.MapInvoiceApi();
settlementApi.MapVoucherApi();

//管理端api
var adminApi = mainApi.MapGroup(Constants.ApiGroup.AdminApi)
    .WithGroupName("admin")
    .WithMetadata(new ApiMeta { AuthType = ApiAuthType.Admin });

adminApi.MapOutboxManagementApi();
// adminApi.MapAdminApi();
// adminApi.MapNkpApi();
// adminApi.MapYbgApi();
// adminApi.MapNoahAdminApi();
// adminApi.MapDemoApi();

Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
app.Run();