﻿namespace Settlement.WebApi.Hosting
{
    public class DailyWorker : HostingBase
    {
        private readonly Settlement.Application.Service.IInvoiceService _invoiceService;

        public DailyWorker(IHostApplicationLifetime lifetime,
            Application.Service.IInvoiceService invoiceService) : base(lifetime)
        {
            _invoiceService = invoiceService;
        }
        public override async Task StartAsync(CancellationToken cancel)
        {
            _bgTasks.Add(await Task.Factory.StartNew(() => Worker30Seconds(_cancellationToken), cancel, TaskCreationOptions.LongRunning, TaskScheduler.Default));
        }

        private async Task Worker30Seconds(CancellationToken cancel)
        {
            using var timer = new PeriodicTimer(TimeSpan.FromSeconds(30));
            try
            {
                while (await timer.WaitForNextTickAsync(cancel))
                {
                    // 发起凭证开票流程
                    await _invoiceService.CreateInvoiceForVoucher(cancel);
                }
            }
            catch (OperationCanceledException) { }
        }
    }
}
