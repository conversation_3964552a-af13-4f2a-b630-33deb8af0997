{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "sqlCon": "Server=**************;port=3306;Database=noah_open;User=root;Password=**************;max pool size=100;connect timeout = 30",
    "logSqlCon": "Server=**************;port=3306;Database=staffing_log;User=root;Password=**************;max pool size=100;connect timeout = 30",
    "nkp": "Server=**************;port=3306;Database=staffing;User=root;Password=**************;max pool size=100;connect timeout = 30"
  },
  "Settings": {
    "RedisAddress": "**************,password=k8s_20220315:4vA0KjQFDzB1VhUn,defaultDatabase=8",
    "ServiceKeys": [
      "A137FD89F7A74212A87E641C7D8B99E7",
      "B2842AACB09F4A3986314929CA99B553",
      "8F842AACB09F4A3986314929CA99B553",
      "C1B7FDE9F7A742D2A87E641C7D8B99NC", // 诺快聘订单服务
      "TJKQ9XWCDJZ0ZKJJG4W7GEDGG7D9731" //数字诺亚
    ],
    "StaffingServer": "https://test-nkpapi.nuopin.cn",
    "DataRdsServer": "https://test-openapi.nuopin.cn",
    "ChinaumsUrl": "https://selfapply-test.chinaums.com/self-contract-nmrs-uat/interface/autoReg",
    "ChinaumsKey": "udik876ehjde32dU61edsxsf",
    "ChinaumsAccesserId": "89813017361710F",
    "OpenChinaumsChannelNo": "302",
    "OpenChinaumsAppId": "10037e6f97111707019743c16b420111",
    "OpenChinaumsAppKey": "ee2e3e77a449487da2977d777e7e54db",
    "OpenChinaumsBaseUrl": "https://test-api-open.chinaums.com",
    "OpenChinaumsMchntNo": "898460300278086",
    "OpenChinaumsSM2PublicKey": "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEPUE5W9fRgSP2SMEkztig1gsvZj+vaDq5Tyo1T5sfdJ/4olg/o3SjET3sfpX4yS5aJprh5RhybTJXNUNzUJIXKQ==",
    "OpenChinaumsSM2PublicKeyHex": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d0101022100fffffffeffffffffffffffffffffffffffffffff00000000ffffffffffffffff30440420fffffffeffffffffffffffffffffffffffffffff00000000fffffffffffffffc042028e9fa9e9d9f5e344d5a9e4bcf6509a7f39789f515ab8f92ddbcbd414d940e9304410432c4ae2c1f1981195f9904466a39c9948fe30bbff2660be1715a4589334c74c7bc3736a2f4f6779c59bdcee36b692153d0a9877cc62a474002df32e52139f0a0022100fffffffeffffffffffffffffffffffff7203df6b21c6052b53bbf40939d5412302010103420004b344aaf3d9564cd51d5b87aa57c7c99a498d92c8896e48744c9f00f7c9eaa1071aca500771b67acc1dd7d9ebae4fad3981ce23a1a8a5c00d0768768599f692a5",
    "OpenChinaumsSubProdCode":"232-0011-0001-0001",
    "NuoPinAccountNo": "1130003020000000181",
    "BankName": "银联商务支付股份有限公司",
    "BranchCode": "************",
    "Currency": "CNY",
    "ElasticSearch": {
      "Address": "http://**************:9200",
      "UserName": "elastic",
      "Password": "elastic@9128"
    },
    "NovaPin": {
      "OAuthUrl": "http://*************:3000/oauth/token",
      "ApiBaseUrl": "http://task.royeinfo.com:9999/project/novapin",
      "InitiateServiceBonusPath": "initiateServiceBonus",
      "InitiateInvoicePath": "initiateInvoice"
    },
    "ServiceBonusTax": {
      "TaxDeductionRate": 0.06,
      "EnableTaxDeduction": true,
      "MinTaxDeductionAmount": 0,
      "MaxTaxDeductionAmount": 0
    },
    "StaffingToken": "C1B7FDE9F7A742D2A87E641C7D8B99NC"
  }
}
