using Microsoft.AspNetCore.Mvc;
using Settlement.Application.Service;
using Settlement.Domain.OutboxService;

namespace Settlement.WebApi.Api.admin;

/// <summary>
/// 本地消息表管理接口
/// </summary>
public static class OutboxManagement
{
    public static RouteGroupBuilder MapOutboxManagementApi(this RouteGroupBuilder group)
    {
        var gp = group.MapGroup("/outbox").WithTags("本地消息表管理");

        // 获取消息统计信息
        gp.MapGet("/stats", async (IOutboxService outboxService) =>
        {
            var stats = await outboxService.GetStatsAsync();
            return Results.Ok(stats);
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "获取消息统计信息",
            Description = "获取本地消息表中各状态消息的统计信息"
        });

        // 获取待处理消息列表
        gp.MapGet("/pending", async (IOutboxService outboxService, [FromQuery] int pageSize = 10) =>
        {
            var messages = await outboxService.GetPendingMessagesAsync(pageSize);
            return Results.Ok(messages);
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "获取待处理消息",
            Description = "获取状态为待处理的消息列表"
        });

        // 获取需要重试的消息列表
        gp.MapGet("/retry", async (IOutboxService outboxService, [FromQuery] int pageSize = 10) =>
        {
            var messages = await outboxService.GetRetryMessagesAsync(pageSize);
            return Results.Ok(messages);
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "获取重试消息",
            Description = "获取需要重试的消息列表"
        });

        // 手动处理待发送消息
        gp.MapPost("/process", async (IMessageProcessingService processingService) =>
        {
            await processingService.ProcessPendingMessagesAsync();
            return Results.Ok(new { message = "Processing completed" });
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "手动处理消息",
            Description = "手动触发待发送消息的处理"
        });

        // 手动处理重试消息
        gp.MapPost("/process-retry", async (IMessageProcessingService processingService) =>
        {
            await processingService.ProcessRetryMessagesAsync();
            return Results.Ok(new { message = "Retry processing completed" });
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "手动处理重试消息",
            Description = "手动触发重试消息的处理"
        });

        // 清理已完成的消息
        gp.MapDelete("/cleanup", async (IMessageProcessingService processingService, [FromQuery] int olderThanDays = 7) =>
        {
            var cleanedCount = await processingService.CleanupCompletedMessagesAsync(olderThanDays);
            return Results.Ok(new { cleanedCount, message = $"Cleaned up {cleanedCount} completed messages" });
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "清理已完成消息",
            Description = "清理指定天数前已完成的消息"
        });

        // 标记消息为已完成（用于手动干预）
        gp.MapPut("/{messageId}/complete", async (string messageId, IOutboxService outboxService) =>
        {
            await outboxService.MarkAsCompletedAsync(messageId);
            return Results.Ok(new { message = "Message marked as completed" });
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "标记消息为已完成",
            Description = "手动将指定消息标记为已完成状态"
        });

        // 标记消息为失败（用于手动干预）
        gp.MapPut("/{messageId}/fail", async (string messageId, [FromBody] FailMessageRequest request, IOutboxService outboxService) =>
        {
            await outboxService.MarkAsFailedAsync(messageId, request.ErrorMessage);
            return Results.Ok(new { message = "Message marked as failed" });
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "标记消息为失败",
            Description = "手动将指定消息标记为失败状态"
        });

        return gp;
    }
}

/// <summary>
/// 标记消息失败请求
/// </summary>
public class FailMessageRequest
{
    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = default!;
}
