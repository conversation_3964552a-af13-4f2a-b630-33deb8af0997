﻿using Infrastructure.Exceptions;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using Org.BouncyCastle.Ocsp;
using Settlement.Application.Service;
using Settlement.Domain.Invoice;
using Settlement.Domain.Voucher;

namespace Settlement.WebApi.Api.settlement
{
    public static class Invoice
    {
        const string invoUseScene_Pro = "【项目发票】";
        const string invoUseScene_Ums = "【服务奖金】";
        const string invoUseScene_Ums_Ent = "【服务奖金--公司资产】";
        const string invoUseScene_Ums_Consumption = "【服务奖金--消费情况】";
        public static RouteGroupBuilder MapInvoiceApi(this RouteGroupBuilder group)
        {
            var gp = group.MapGroup("/invoice").WithTags("发票");

            //发起开票
            gp.MapPost("/create/pro", async (InitiateInvoiceForProReq req, IInvoiceService invoiceService) =>
            {
                await invoiceService.CreateInvoiceForPro(req);
                return Results.Ok();
            })
            .WithOpenApi(operation => new(operation)
            {
                Summary = invoUseScene_Pro + "发起开票",
                Description = invoUseScene_Pro + "发起开票"
            });
            gp.MapPost("/create/ums", async (InitiateInvoiceForUmsReq req, IInvoiceService invoiceService) => await invoiceService.CreateInvoiceForUms(req))
            .WithOpenApi(operation => new(operation)
            {
                Summary = "【仅供手动处理时使用】" + invoUseScene_Ums + "发起开票",
                Description = "【仅供手动处理时使用】" + invoUseScene_Ums + "发起开票"
            });

            //获取发票记录
            gp.MapPost("/getlast/pro", (GetProLastInvoiceReq req, IInvoiceService invoiceService) => invoiceService.GetLastInvoiceForPro(req))
            .WithOpenApi(operation => new(operation)
            {
                Summary = invoUseScene_Pro + "获取上次开发票填写信息",
                Description = invoUseScene_Pro + "获取上次开发票填写信息"
            });
            gp.MapPost("/getlist/pro", (GetProInvoicesReq req, IInvoiceService invoiceService) => invoiceService.GetInvoicesForPro(req))
            .WithOpenApi(operation => new(operation)
            {
                Summary = invoUseScene_Pro + "获取发票列表",
                Description = invoUseScene_Pro + "获取发票列表"
            });
            gp.MapPost("/getlist/ums/in", (GetUmsInvoicesReq req, IInvoiceService invoiceService) => invoiceService.GetInvoicesForUms(req, "IN"))
            .WithOpenApi(operation => new(operation)
            {
                Summary = invoUseScene_Ums_Ent + "获取发票列表",
                Description = invoUseScene_Ums_Ent + "获取发票列表"
            });
            gp.MapGet("/downloadlist/ums/in", ([AsParameters] DownLoadUmsInvoicesReq req, IInvoiceService invoiceService) => downloadUmslist(req, "IN", invoiceService))
            .WithOpenApi(operation => new(operation)
            {
                Summary = invoUseScene_Ums_Ent + "下载发票列表",
                Description = invoUseScene_Ums_Ent + "下载发票列表"
            });
            gp.MapPost("/getlist/ums/out", (GetUmsInvoicesReq req, IInvoiceService invoiceService) => invoiceService.GetInvoicesForUms(req, "OUT"))
            .WithOpenApi(operation => new(operation)
            {
                Summary = invoUseScene_Ums_Consumption + "获取发票列表",
                Description = invoUseScene_Ums_Consumption + "获取发票列表"
            });
            gp.MapGet("/downloadlist/ums/out", ([AsParameters] DownLoadUmsInvoicesReq req, IInvoiceService invoiceService) => downloadUmslist(req, "OUT", invoiceService))
            .WithOpenApi(operation => new(operation)
            {
                Summary = invoUseScene_Ums_Consumption + "下载发票列表",
                Description = invoUseScene_Ums_Consumption + "下载发票列表"
            });
            //gp.MapPost("/getdetail", (GetInvoicesDetailReq req, IInvoiceService invoiceService) => invoiceService.GetInvoicesDetail(req))
            //.WithOpenApi(operation => new(operation)
            //{
            //    Summary = "获取发票详情",
            //    Description = "获取发票详情"
            //});

            ////获取动账记录
            //gp.MapPost("/gettrans/pro", (GetTransForProReq req, IInvoiceService invoiceService) => invoiceService.GetTransForPro(req))
            //.WithOpenApi(operation => new(operation)
            //{
            //    Summary = invoUseScene_Pro + "获取动账记录",
            //    Description = invoUseScene_Pro + "获取动账记录"
            //});
            //gp.MapPost("/gettrans/ums", (GetTransForUmsReq req, IInvoiceService invoiceService) => invoiceService.GetTransForUms(req))
            //.WithOpenApi(operation => new(operation)
            //{
            //    Summary = invoUseScene_Ums + "获取动账记录",
            //    Description = invoUseScene_Ums + "获取动账记录"
            //});

            //获取发票统计
            gp.MapPost("/getstatistics/pro", (GetStatisticsForProReq req, IInvoiceService invoiceService) => invoiceService.GetInvoiceStatisticsForPro(req))
            .WithOpenApi(operation => new(operation)
            {
                Summary = invoUseScene_Pro + "获取发票统计",
                Description = invoUseScene_Pro + "获取发票统计"
            });
            //gp.MapPost("/getstatistics/ums", (GetStatisticsForUmsReq req, IInvoiceService invoiceService) => invoiceService.GetInvoiceStatisticsForUms(req))
            //.WithOpenApi(operation => new(operation)
            //{
            //    Summary = invoUseScene_Ums + "获取发票统计",
            //    Description = invoUseScene_Ums + "获取发票统计"
            //});

            gp.MapGet("/download", async ([FromQuery] string InvoiceUrl) =>
            {
                //验证参数
                if (string.IsNullOrEmpty(InvoiceUrl))
                    throw new BadRequestException($"发票PDF文件链接不能为空");

                using var client = new HttpClient();
                var response = await client.GetAsync(InvoiceUrl);

                var stream = await response.Content.ReadAsStreamAsync();

                return Results.File(stream, "application/octet-stream", "downloaded_invoice.pdf");
            })
            .WithOpenApi(operation => new(operation)
            {
                Summary = "下载发票",
                Description = "下载发票"
            });
            return gp;
        }

        /// <summary>
        /// 下载【服务奖金】发票
        /// </summary>
        /// <param name="req"></param>
        /// <param name="MerchantType"></param>
        /// <param name="invoiceService"></param>
        /// <returns></returns>
        private static IResult downloadUmslist(DownLoadUmsInvoicesReq req, string MerchantType, IInvoiceService invoiceService)
        {
            var data = invoiceService.GetInvoicesForUms(new GetUmsInvoicesReq(req), MerchantType).Rows.Select(x => new DownloaUmsInvoicesResp(x)).ToList();

            var memoryStream = new MemoryStream();
            memoryStream.SaveAs(data);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
              $"服务奖金发票.xlsx");
        }
    }
}
