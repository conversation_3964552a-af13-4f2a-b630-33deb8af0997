using Config;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Settlement.Application.Service;
using Settlement.Domain.Messages;

namespace Settlement.WebApi.Api.settlement;

/// <summary>
/// 消息测试接口
/// </summary>
public static class MessageTest
{
    public static RouteGroupBuilder MapMessageTestApi(this RouteGroupBuilder group)
    {
        var gp = group.MapGroup("/message-test").WithTags("消息测试");

        // 测试发布服务奖金消息
        gp.MapPost("/service-bonus", async ([FromBody] TestServiceBonusRequest request, IMessagePublishService messagePublishService) =>
        {
            try
            {
                var message = new ServiceBonusInitiatedMessage
                {
                    BusinessId = request.OrderNumber,
                    ContractCode = request.ContractCode,
                    AgentHrNo = request.AgentHrNo,
                    OrderNumber = request.OrderNumber,
                    IssueList = request.IssueList.Select(x => new global::Settlement.Domain.Messages.IssueItem
                    {
                        PersonId = x.PersonId,
                        Amount = x.Amount,
                        Type = x.Type
                    }).ToList()
                };

                var messageId = await messagePublishService.PublishAsync(message, ApiTypes.NovaPin,"");

                return Results.Ok(new
                {
                    Success = true,
                    MessageId = messageId,
                    Message = "Service bonus message published successfully"
                });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Error publishing message: {ex.Message}");
            }
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "测试发布服务奖金消息",
            Description = "测试发布服务奖金消息到本地消息表和Redis队列"
        });

        // 测试银联商务消息
        gp.MapPost("/chinaums", async ([FromBody] TestChinaumsRequest request, IMessagePublishService messagePublishService) =>
        {
            try
            {
                var message = new ChinaumsRegistrationMessage
                {
                    BusinessId = request.UmsRegId,
                    UmsRegId = request.UmsRegId,
                    Status = request.Status,
                    UserInfo = request.UserInfo
                };

                var messageId = await messagePublishService.PublishAsync(message, ApiTypes.Chinaums,"");

                return Results.Ok(new
                {
                    Success = true,
                    MessageId = messageId,
                    Message = "Chinaums registration message published successfully"
                });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Error publishing Chinaums message: {ex.Message}");
            }
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "测试发布银联商务消息",
            Description = "测试发布银联商务进件消息到本地消息表"
        });


        gp.MapPost("/Sm2Utils/Verify", (IOptions<ConfigManager> config) =>
        {
            if (Infrastructure.Common.Sm2Utils.VerifyHex(config.Value.OpenChinaumsSM2PublicKeyHex,
                "{\"channelNo\":\"302\",\"mchntNo\":\"898460300278086\",\"noticeType\":\"subAcctOpen\",\"msg\":{\"externalUserNo\":\"333822681946286726\",\"acctNo\":\"1132003020000000534\",\"userType\":\"03\",\"openStatus\":\"1\"}}",
                "3046022100f464518726a255af6d3a035ba89cb0727aa230353f8a6ae36d0ba7bd0d2721b60221009254542a17325b339b494fbe0d26cafe7b1a16ade443658732537b5a7e024af2"))
                return Results.Ok("验签成功"); 
            else
                return Results.BadRequest("签名验证失败");
        })
       .WithOpenApi(operation => new(operation)
       {
           Summary = "测试签名验证",
           Description = "测试签名验证"
       })
       .WithMetadata(new ApiMeta());
        return gp;
    }
}

/// <summary>
/// 测试服务奖金请求
/// </summary>
public class TestServiceBonusRequest
{
    public string ContractCode { get; set; } = default!;
    public string AgentHrNo { get; set; } = default!;
    public string OrderNumber { get; set; } = default!;
    public List<TestIssueItem> IssueList { get; set; } = new();
}

/// <summary>
/// 测试发放项目
/// </summary>
public class TestIssueItem
{
    public string PersonId { get; set; } = default!;
    public decimal Amount { get; set; }
    public string Type { get; set; } = default!;
}

/// <summary>
/// 测试开票请求
/// </summary>
public class TestInvoiceRequest
{
    public string ContractCode { get; set; } = default!;
    public string OrderNumber { get; set; } = default!;
    public decimal Amount { get; set; }
    public string InvoiceType { get; set; } = "Standard";
    public string? Remark { get; set; }
}

/// <summary>
/// 测试银联商务请求
/// </summary>
public class TestChinaumsRequest
{
    public string UmsRegId { get; set; } = default!;
    public string Status { get; set; } = default!;
    public string UserInfo { get; set; } = default!;
}
