﻿using Infrastructure.Exceptions;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using Org.BouncyCastle.Ocsp;
using ServiceStack.Text;
using Settlement.Application.Service;
using Settlement.Domain.Invoice;
using Settlement.Domain.Voucher;
using System.IO;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Settlement.WebApi.Api.settlement;

public static class Voucher
{
    const string Scene_In = "【公司资产】";
    const string Scene_Out = "【消费情况】";
    public static RouteGroupBuilder MapVoucherApi(this RouteGroupBuilder group)
    {
        var gp = group.MapGroup("/voucher").WithTags("凭证");

        gp.MapPost("/getlist/in", (VoucherGetList req, IVoucherService voucherService) => voucherService.GetList(req, "IN"))
        .WithOpenApi(operation => new(operation)
        {
            Summary = Scene_In + "分页获取凭证记录",
            Description = Scene_In + "分页获取凭证记录"
        });

        gp.MapGet("/download/in", ([AsParameters] VoucherDownloadList req, IVoucherService voucherService) => DownloadMerchant(req, "IN", voucherService))
        .WithOpenApi(operation => new(operation)
        {
            Summary = Scene_In + "下载凭证",
            Description = Scene_In + "下载凭证"
        });

        gp.MapPost("/getlist/out", (VoucherGetList req, IVoucherService voucherService) => voucherService.GetList(req, "OUT"))
        .WithOpenApi(operation => new(operation)
        {
            Summary = Scene_Out + "分页获取凭证记录",
            Description = Scene_Out + "分页获取凭证记录"
        });

        gp.MapGet("/download/out", ([AsParameters] VoucherDownloadList req, IVoucherService voucherService) => DownloadMerchant(req, "OUT", voucherService))
        .WithOpenApi(operation => new(operation)
        {
            Summary = Scene_Out + "下载凭证",
            Description = Scene_Out + "下载凭证"
        });

        gp.MapGet("/download", async ([FromQuery] string VoucherUrl) =>
        {
            //验证参数
            if (string.IsNullOrEmpty(VoucherUrl))
                throw new BadRequestException($"合同PDF文件链接不能为空");

            using var client = new HttpClient();
            var response = await client.GetAsync(VoucherUrl);

            var stream = await response.Content.ReadAsStreamAsync();

            return Results.File(stream, "application/octet-stream", "downloaded_voucher.pdf");
        })
        .WithOpenApi(operation => new(operation)
        {
            Summary = "下载凭证",
            Description = "下载凭证"
        });
        return gp;
    }

    private static IResult DownloadMerchant(VoucherDownloadList req, string MerchantType, IVoucherService voucherService)
    {
        var data = voucherService.GetList(new VoucherGetList(req), MerchantType).Rows.Select(x => new VoucherListExportResp(x)).ToList();

        var memoryStream = new MemoryStream();
        memoryStream.SaveAs(data);
        memoryStream.Seek(0, SeekOrigin.Begin);

        return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          $"凭证.xlsx");
    }
}
