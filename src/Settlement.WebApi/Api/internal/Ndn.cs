﻿using Infrastructure.Extend;
using Settlement.Application.Service;
using Settlement.Domain.Transaction;
using Settlement.Infrastructure.Proxy;

namespace Settlement.WebApi.Api.@internal
{
    [Service(ServiceLifetime.Transient)]
    public static class Ndn
    {
        public static RouteGroupBuilder InternalNdnApi(this RouteGroupBuilder group)
        {
            var gp = group.MapGroup("/ndn").WithTags("内部接口");
            gp.MapPost("/notify", (NdnNotify req, INdnService ndnService) =>
            {
                var result = ndnService.NdnNotify(req);
                return result;
            })
            .WithOpenApi(operation => new(operation)
            {
                Summary = "数字诺亚回调处理",
                Description = "数字诺亚回调处理"
            });

            return gp;
        }
    }
}
