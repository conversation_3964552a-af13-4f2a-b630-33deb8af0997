
using Settlement.Application.Service;
using Settlement.Domain.Transaction;

namespace Settlement.WebApi.Api.@internal;

/// <summary>
/// 内部服务
/// </summary>
public static class Internal
{
    public static RouteGroupBuilder InternalApi(this RouteGroupBuilder group)
    {
        var gp = group.MapGroup(string.Empty).WithTags("内部接口");
        gp.MapPost("/transaction", async (TransactionReq req, ISubAccountService subAccountService) =>
            {
                var result = await subAccountService.Transaction(req);
                return result;
            })
            .WithOpenApi(operation => new(operation)
            {
                Summary = "转账支付接口",
                Description = "转账支付接口"
            });
      

        return gp;
    }
}
