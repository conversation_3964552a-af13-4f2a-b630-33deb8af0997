﻿using Config.Enums.Nkp;
using Entity.Nkp;
using Nest;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;

namespace Settlement.Domain.Invoice
{
    /// <summary>
    /// 发起开票请求参数
    /// </summary>
    public class InitiateInvoiceReqBase
    {
        /// <summary>
        /// 开票金额(单位：元)
        /// </summary>
        [JsonPropertyName("amount")]
        [Range(0.01, double.MaxValue, ErrorMessage = "开票金额不能小于0.01")]
        public decimal Amount { get; set; }

        /// <summary>
        /// 开票类型
        /// </summary>
        [JsonPropertyName("invoiceType")]
        public NovaPinInvoiceType InvoiceType { get; set; }

        /// <summary>
        /// 纳税人识别号(购方税号)
        /// </summary>
        [JsonPropertyName("buyertaxnum")]
        [Required(ErrorMessage = "纳税人识别号不能为空")]
        public string? BuyerTaxnum { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        [JsonPropertyName("buyeraccountname")]
        //[Required(ErrorMessage = "开户银行不能为空")]
        public string? BuyerAccountName { get; set; }

        /// <summary>
        /// 开户行账号
        /// </summary>
        [JsonPropertyName("buyeraccount")]
        //[Required(ErrorMessage = "银行账号不能为空")]
        public string? BuyerAccount { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [JsonPropertyName("buyeraddress")]
        //[Required(ErrorMessage = "开票地址不能为空")]
        public string? BuyerAddress { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [JsonPropertyName("buyertel")]
        //[Required(ErrorMessage = "开票电话不能为空")]
        public string? BuyerTel { get; set; }

        /// <summary>
        /// 推送方式
        /// </summary>
        [JsonPropertyName("pushmode")]
        public NovaPinInvoicePushMode PushMode { get; set; }

        /// <summary>
        /// 推送手机
        /// </summary>
        [JsonPropertyName("buyerphone")]
        public string? BuyerPhone { get; set; }

        /// <summary>
        /// 推送邮箱
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// 发票备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string? Remark { get; set; }

        /// <summary>
        /// 规格型号(非必填，页面无此参数)
        /// </summary>
        [JsonPropertyName("spectype")]
        public string? SpecType { get; set; }
    }

    /// <summary>
    /// 发起开票请求参数
    /// </summary>
    public class InitiateInvoiceReq : InitiateInvoiceReqBase
    {
        public InitiateInvoiceReq() { }
        public InitiateInvoiceReq(InitiateInvoiceForProReq req, string ContractCode, string BuyerName, Invoice_Product product)
        {
            this.ContractCode = ContractCode;

            this.InvoiceName = product.ProductName;
            this.GoodsCode = product.TaxTypeCode;
            this.VatRate = product.TaxRate;
            this.WithTaxFlag = ((int)product.IsIncludedTax).ToString();
            this.FavouredpolicyFlag = product.IsEnjoyPreferential == InvoiceProductIsEnjoyPreferential.不享受 ? "0" : product.PreferentialCode;

            this.Amount = req.Amount;
            this.InvoiceType = req.InvoiceType;
            this.BuyerTaxnum = req.BuyerTaxnum;
            this.BuyerName = BuyerName;
            this.BuyerAddress = req.BuyerAddress ?? string.Empty;
            this.BuyerTel = req.BuyerTel ?? string.Empty;
            this.BuyerAccountName = req.BuyerAccountName ?? string.Empty;
            this.BuyerAccount = req.BuyerAccount ?? string.Empty;
            this.SpecType = req.SpecType;
            this.PushMode = req.PushMode;
            this.BuyerPhone = req.BuyerPhone;
            this.Email = req.Email;
            this.Remark = req.Remark;


            if (string.IsNullOrEmpty(req.BuyerAccountName) && string.IsNullOrEmpty(req.BuyerAccount))
                this.ShowBankAccountType = NovaPinInvoiceShowBankAccountType.都不显示;
            else
                this.ShowBankAccountType = NovaPinInvoiceShowBankAccountType.备注仅显示购方开户行及账号;

            if (string.IsNullOrEmpty(req.BuyerAddress) && string.IsNullOrEmpty(req.BuyerTel))
                this.ShowAddressTelType = NovaPinInvoiceShowAddressTelType.都不显示;
            else
                this.ShowAddressTelType = NovaPinInvoiceShowAddressTelType.备注仅显示购方地址及电话;
        }
        public InitiateInvoiceReq(Entity.Nkp.Voucher req, string ContractCode, NdnInvoiceInfo BuyerInvoiceInfo, Invoice_Product product)
        {
            this.ContractCode = ContractCode;

            this.InvoiceName = product.ProductName;
            this.GoodsCode = product.TaxTypeCode;
            this.VatRate = product.TaxRate;
            this.WithTaxFlag = ((int)product.IsIncludedTax).ToString();
            this.FavouredpolicyFlag = product.IsEnjoyPreferential == InvoiceProductIsEnjoyPreferential.不享受 ? "0" : product.PreferentialCode;

            this.Amount = req.Amount;
            this.InvoiceType = BuyerInvoiceInfo.InvoiceType ?? NovaPinInvoiceType.专票;
            this.BuyerTaxnum = BuyerInvoiceInfo.TaxNumber;
            this.BuyerName = BuyerInvoiceInfo.InvoiceTitle;
            this.BuyerAddress = BuyerInvoiceInfo.Address ?? string.Empty;
            this.BuyerTel = BuyerInvoiceInfo.Phone ?? string.Empty;
            this.BuyerAccountName = BuyerInvoiceInfo.Bank ?? string.Empty;
            this.BuyerAccount = BuyerInvoiceInfo.BankAccount ?? string.Empty;
            this.SpecType = string.Empty;
            this.PushMode = BuyerInvoiceInfo.PushMode ?? NovaPinInvoicePushMode.不推送;
            this.BuyerPhone = BuyerInvoiceInfo.PushMode == NovaPinInvoicePushMode.手机 ? BuyerInvoiceInfo.PushAddress : string.Empty;
            this.Email = BuyerInvoiceInfo.PushMode == NovaPinInvoicePushMode.邮箱 ? BuyerInvoiceInfo.PushAddress : string.Empty;
            this.Remark = req.Remark;

            this.ShowBankAccountType = NovaPinInvoiceShowBankAccountType.都不显示;
            this.ShowAddressTelType = NovaPinInvoiceShowAddressTelType.都不显示;
        }

        #region 固定字段
        /// <summary>
        /// 开票金额(单位：元)
        /// </summary>
        [JsonPropertyName("amount")]
        [Range(0.01, double.MaxValue, ErrorMessage = "开票金额不能小于0.01")]
        public new decimal Amount { get; set; }
        /// <summary>
        /// 单位(是指商品的单位)
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit { get; set; } = "套";

        /// <summary>
        /// 是否展示收款和复核人
        /// </summary>
        [JsonPropertyName("showcheckertype")]
        public NovaPinInvoiceShowCheckerType ShowCheckerType { get; set; } = NovaPinInvoiceShowCheckerType.不显示;
        #endregion

        /// <summary>
        /// 是否显示开户行及账号
        /// </summary>
        [JsonPropertyName("showbankaccounttype")]
        public NovaPinInvoiceShowBankAccountType ShowBankAccountType { get; set; } = NovaPinInvoiceShowBankAccountType.备注仅显示购方开户行及账号;
        /// <summary>
        /// 是否显示地址及电话
        /// </summary>
        [JsonPropertyName("showaddressteltype")]
        public NovaPinInvoiceShowAddressTelType ShowAddressTelType { get; set; } = NovaPinInvoiceShowAddressTelType.备注仅显示购方地址及电话;

        /// <summary>
        /// 动账记录ID列表
        /// </summary>
        //[Required(ErrorMessage = "交易记录不能为空")]
        public List<string>? TransIDs { get; set; }

        /// <summary>
        /// 合同编码
        /// </summary>
        [Required(ErrorMessage = "合同编码不能为空")]
        [JsonPropertyName("contractCode")]
        public string ContractCode { get; set; } = default!;

        /// <summary>
        /// 抬头(购方名称)
        /// </summary>
        [JsonPropertyName("buyername")]
        public string? BuyerName { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        [JsonPropertyName("invoicename")]
        public string? InvoiceName { get; set; }

        /// <summary>
        /// 商品税收分类编码
        /// </summary>
        [JsonPropertyName("goodscode")]
        public string? GoodsCode { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [JsonPropertyName("vatrate")]
        public decimal? VatRate { get; set; }

        /// <summary>
        /// 含税标志
        /// </summary>
        [JsonPropertyName("withtaxflag")]
        public string? WithTaxFlag { get; set; }

        /// <summary>
        /// 优惠政策标识
        /// </summary>
        [JsonPropertyName("favouredpolicyflag")]
        public string? FavouredpolicyFlag { get; set; }
    }

    /// <summary>
    /// 发起开票请求参数（项目发票应用场景）
    /// </summary>
    public class InitiateInvoiceForProReq : InitiateInvoiceReqBase
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [Required(ErrorMessage = "项目ID不能为空")]
        public string ProjectId { get; set; } = default!;
    }

    /// <summary>
    /// 发起开票请求参数（服务奖金应用场景）
    /// </summary>
    public class InitiateInvoiceForUmsReq
    {
        /// <summary>
        /// 凭证记录id 
        /// </summary>
        [Required(ErrorMessage = "凭证记录id 不能为空")]
        public string? VoucherId { get; set; }
    }

    /// <summary>
    /// 根据项目获取充值/退款动账记录
    /// </summary>
    public class GetTransForProReq : ReqPageBase
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [Required(ErrorMessage = "项目编码不能为空")]
        public string? ProjectId { get; set; }
    }

    /// <summary>
    /// 根据商户获取服务奖金相关的 充值/退款动账记录
    /// </summary>
    public class GetTransForUmsReq : ReqPageBase
    {
        /// <summary>
        /// 开票商户id 
        /// </summary>
        [Required(ErrorMessage = "开票商户id 不能为空")]
        public string? MerchantId { get; set; }

        /// <summary>
        /// 购方商户id 
        /// </summary>
        [Required(ErrorMessage = "购方商户id 不能为空")]
        public string? BuyerMerchantId { get; set; }
    }

    /// <summary>
    /// 获取【项目】上次开发票填写信息接口开发
    /// </summary>
    public class GetProLastInvoiceReq
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        [Required(ErrorMessage = "项目编码不能为空")]
        public string? ProjectId { get; set; }
    }
    public class GetProLastInvoiceResp
    {
        public GetProLastInvoiceResp(string? ContractNo, string? Participant, string? Initiator, Invoice_Product Product)
        {
            IssuingCompany = Participant ?? string.Empty;
            BuyerName = Initiator ?? string.Empty;
            VatRate = Product.TaxRate.ToString();
        }
        public GetProLastInvoiceResp(Invoice_Record Model)
        {
            UserId = Model.UserId;
            AgentHrName = Model.AgentHrName;
            ProjectId = Model.ProjectId;
            MerchantId = Model.MerchantId;
            BuyerMerchantId = Model.BuyerMerchantId;
            ContractCode = Model.ContractCode;
            AgentHrNo = Model.AgentHrNo;
            Amount = Model.Amount;
            Unit = Model.Unit;
            InvoiceType = Model.InvoiceType;
            BuyerTaxnum = Model.BuyerTaxnum;
            BuyerName = Model.BuyerName;
            BuyerAddress = Model.BuyerAddress;
            BuyerTel = Model.BuyerTel;
            BuyerAccountName = Model.BuyerAccountName;
            BuyerAccount = Model.BuyerAccount;
            InvoiceName = Model.InvoiceName;
            GoodsCode = Model.GoodsCode;
            VatRate = Model.VatRate;
            WithTaxFlag = Model.WithTaxFlag;
            FavouredpolicyFlag = Model.FavouredpolicyFlag;
            SpecType = Model.SpecType;
            PushMode = Model.PushMode;
            BuyerPhone = Model.BuyerPhone;
            Email = Model.Email;
            ShowBankAccountType = Model.ShowBankAccountType;
            ShowAddressTelType = Model.ShowAddressTelType;
            ShowCheckerType = Model.ShowCheckerType;
            Remark = Model.Remark;
            IssuingCompany = Model.IssuingCompany;
        }

        /// <summary>
        /// 用户id（提交人）
        /// </summary>
        public string UserId { get; set; } = default!;

        /// <summary>
        /// 提交人的姓名
        /// </summary>
        public string AgentHrName { get; set; } = default!;

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; } = default!;
        /// <summary>
        /// 开票商户id 
        /// </summary>
        public string? MerchantId { get; set; }

        /// <summary>
        /// 购方商户id 
        /// </summary>
        public string? BuyerMerchantId { get; set; }
        /// <summary>
        /// 合同编码
        /// </summary>
        public string ContractCode { get; set; } = default!;

        /// <summary>
        /// 经办人
        /// </summary>
        public string AgentHrNo { get; set; } = default!;

        /// <summary>
        /// 开票金(单位：元)
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = default!;

        /// <summary>
        /// 开票类型
        /// </summary>
        public NovaPinInvoiceType InvoiceType { get; set; }

        /// <summary>
        /// 购方税号
        /// </summary>
        public string? BuyerTaxnum { get; set; }

        /// <summary>
        /// 购方名称
        /// </summary>
        public string? BuyerName { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string? BuyerAddress { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string? BuyerTel { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        public string? BuyerAccountName { get; set; }

        /// <summary>
        /// 开户行账号
        /// </summary>
        public string? BuyerAccount { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string? InvoiceName { get; set; }

        /// <summary>
        /// 商品税收分类编码
        /// </summary>
        public string? GoodsCode { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public string? VatRate { get; set; }

        /// <summary>
        /// 含税标志
        /// </summary>
        public string? WithTaxFlag { get; set; }

        /// <summary>
        /// 优惠政策标识
        /// </summary>
        public string? FavouredpolicyFlag { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? SpecType { get; set; }

        /// <summary>
        /// 推送方式
        /// </summary>
        public NovaPinInvoicePushMode PushMode { get; set; }

        /// <summary>
        /// 推送手机
        /// </summary>
        public string? BuyerPhone { get; set; }

        /// <summary>
        /// 推送邮箱
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// 是否显示开户行及账号
        /// </summary>
        public NovaPinInvoiceShowBankAccountType ShowBankAccountType { get; set; }
        /// <summary>
        /// 是否显示地址及电话
        /// </summary>
        public NovaPinInvoiceShowAddressTelType ShowAddressTelType { get; set; }
        /// <summary>
        /// 是否展示收款和复核人
        /// </summary>
        public NovaPinInvoiceShowCheckerType ShowCheckerType { get; set; }

        /// <summary>
        /// 发票备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 本方公司名称(开票企业)
        /// </summary>
        public string IssuingCompany { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取【项目】发票列表接口开发
    /// </summary>
    public class GetProInvoicesReq : ReqPageBase
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public string? ProjectId { get; set; }
    }

    /// <summary>
    /// 获取【项目】发票列表接口开发
    /// </summary>
    public class GetProInvoicesResp
    {
        public GetProInvoicesResp() { }
        public GetProInvoicesResp(Invoice_Record Model)
        {
            this.Id = Model.Id;
            this.UserId = Model.UserId;
            this.ProjectId = Model.ProjectId;
            this.AgentHr = $"{Model.AgentHrName}({Model.AgentHrNo})";
            this.Amount = Model.Amount;
            this.InvoiceType = Model.InvoiceType;
            this.BuyerName = Model.BuyerName;
            this.Remark = Model.Remark;
            this.CreateTime = Model.CreateTime;
            this.Status = Model.Status;
            this.InvoicePDFUrl = string.IsNullOrEmpty(Model.InvoicePDFUrl) || Model.InvoicePDFUrl == "[]" ? string.Empty : Newtonsoft.Json.JsonConvert.DeserializeObject<string[]>(Model.InvoicePDFUrl)?.FirstOrDefault() ?? string.Empty;
            this.CallBackRemark = Model.Status == InvoiceRecordStatus.处理失败 ? Model.CallBackRemark : "--";
            this.IssuingCompany = Model.IssuingCompany;
        }
        public string Id { get; set; } = default!;

        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 用户id（提交人）
        /// </summary>
        public string UserId { get; set; } = default!;
        /// <summary>
        /// 申请人(提交人，取：姓名+工号)
        /// </summary>
        public string AgentHr { get; set; } = default!;

        /// <summary>
        /// 本方公司名称(开票企业)
        /// </summary>
        public string IssuingCompany { get; set; } = string.Empty;
        /// <summary>
        /// 购方名称(开票抬头)
        /// </summary>
        public string? BuyerName { get; set; }

        /// <summary>
        /// 开票金额(单位：元)
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 开票类型(开票介质)
        /// </summary>
        public NovaPinInvoiceType InvoiceType { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public InvoiceRecordStatus Status { get; set; } = InvoiceRecordStatus.已申请;

        /// <summary>
        /// 拒绝原因(数字诺亚审核后回调时传的Remark)
        /// </summary>
        public string CallBackRemark { get; set; } = string.Empty;

        /// <summary>
        /// 发票备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; } = default!;

        /// <summary>
        /// 开具发票后，返回的发票PDF文件链接
        /// </summary>
        public string InvoicePDFUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取【服务奖金】发票列表接口开发
    /// </summary>
    public class GetUmsInvoicesReq : ReqPageBase
    {
        public GetUmsInvoicesReq() { }
        public GetUmsInvoicesReq(DownLoadUmsInvoicesReq req, int PageIndex = 1, int PageSize = 99999)
        {
            this.MerchantId = req.MerchantId;
            this.BeginTime = req.BeginTime;
            this.EndTime = req.EndTime;
            this.Search = req.Search;

            this.PageIndex = PageIndex;
            this.PageSize = PageSize;
        }
        /// <summary>
        /// 指定商户id (用于获取他开的和开给他的发票)
        /// </summary>
        [Required(ErrorMessage = "指定商户id不能为空")]
        public string? MerchantId { get; set; }

        /// <summary>
        /// 时间筛选--开始时间 
        /// </summary>
        public DateTime? BeginTime { get; set; }

        /// <summary>
        /// 时间筛选--截止时间 
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 模糊检索字段
        /// </summary>
        public string? Search { get; set; }
    }
    /// <summary>
    /// 获取【服务奖金】发票列表接口开发
    /// </summary>
    public class DownLoadUmsInvoicesReq
    {
        /// <summary>
        /// 指定商户id (用于获取他开的和开给他的发票)
        /// </summary>
        [Required(ErrorMessage = "指定商户id不能为空")]
        public string? MerchantId { get; set; }

        /// <summary>
        /// 时间筛选--开始时间 
        /// </summary>
        public DateTime? BeginTime { get; set; }

        /// <summary>
        /// 时间筛选--截止时间 
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 模糊检索字段
        /// </summary>
        public string? Search { get; set; }
    }

    /// <summary>
    /// 获取【项目】发票列表接口开发
    /// </summary>
    public class GetUmsInvoicesResp
    {
        public GetUmsInvoicesResp() { }
        public GetUmsInvoicesResp(Invoice_Record? Model, Entity.Nkp.Voucher Voucher)
        {
            this.Id = Voucher.Id;
            this.InvoicesTime = Voucher.VoucherTime;
            this.InvoicesName = $"{Voucher.VoucherTime:yyyy年MM月}发票";
            if (Model != null)
            {
                this.Amount = Model.Amount;
                this.InvoiceType = Model.InvoiceType;
                this.BuyerName = Model.BuyerName;
                this.CreateTime = Model.CreateTime;
                this.Status = Model.Status;
                this.InvoicePDFUrl = string.IsNullOrEmpty(Model.InvoicePDFUrl) || Model.InvoicePDFUrl == "[]" ? string.Empty : Newtonsoft.Json.JsonConvert.DeserializeObject<string[]>(Model.InvoicePDFUrl)?.FirstOrDefault() ?? string.Empty;
                this.CallBackRemark = Model.Status == InvoiceRecordStatus.处理失败 ? Model.CallBackRemark : string.Empty;
                //备注交换
                string temp = this.Remark ?? string.Empty; this.Remark = this.CallBackRemark; this.CallBackRemark = temp;
                this.IssuingCompany = Model.IssuingCompany;
            }
            else
            {
                this.Amount = Voucher.Amount;
                this.InvoiceType = Voucher.OutMerchant.InvoiceInfo?.InvoiceType ?? NovaPinInvoiceType.专票;
                this.BuyerName = Voucher.OutMerchantName;
                this.CreateTime = Voucher.CreatedTime;
                this.Status = (Voucher.InvoiceStatus == VoucherInvoiceStatus.尚未开票 || Voucher.InvoiceStatus == VoucherInvoiceStatus.申请开票失败) ? InvoiceRecordStatus.尚未申请 :
                        (Voucher.InvoiceStatus == VoucherInvoiceStatus.出入金同账户不开票 ? InvoiceRecordStatus.处理失败 :
                        (Voucher.InvoiceStatus == VoucherInvoiceStatus.已申请开票 ? InvoiceRecordStatus.已申请 :
                        (Voucher.InvoiceStatus == VoucherInvoiceStatus.开票成功 ? InvoiceRecordStatus.处理完成 : InvoiceRecordStatus.处理失败)));
                this.InvoicePDFUrl = string.Empty;
                this.CallBackRemark = Voucher.InvoiceStatus == VoucherInvoiceStatus.尚未开票 ? "尚未开票" : (Voucher.InvoiceRemark ?? Voucher.InvoiceStatus.ToString());
                //备注交换
                string temp = this.Remark ?? string.Empty; this.Remark = this.CallBackRemark; this.CallBackRemark = temp;
                this.IssuingCompany = Voucher.InMerchantName;
            }
        }
        public GetUmsInvoicesResp(Invoice_Record Model, DateTime VoucherTime)
        {
            this.Id = Model.Id;
            this.InvoicesTime = VoucherTime;
            this.InvoicesName = $"{VoucherTime:yyyy年MM月}发票";
            this.Amount = Model.Amount;
            this.InvoiceType = Model.InvoiceType;
            this.BuyerName = Model.BuyerName;
            this.Remark = Model.Remark;
            this.CreateTime = Model.CreateTime;
            this.Status = Model.Status;
            this.InvoicePDFUrl = Model.InvoicePDFUrl;
            this.CallBackRemark = Model.Status == InvoiceRecordStatus.处理失败 ? Model.CallBackRemark : string.Empty;
            this.IssuingCompany = Model.IssuingCompany;
        }
        public string Id { get; set; } = default!;

        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 发票月份
        /// </summary>
        public DateTime InvoicesTime { get; set; }
        /// <summary>
        /// 发票名称
        /// </summary>
        public string InvoicesName { get; set; }

        /// <summary>
        /// 开票主体
        /// </summary>
        public string IssuingCompany { get; set; } = string.Empty;
        /// <summary>
        /// 发票抬头
        /// </summary>
        public string? BuyerName { get; set; }

        /// <summary>
        /// 开票金额(单位：元)
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 开票类型(开票介质)
        /// </summary>
        public NovaPinInvoiceType InvoiceType { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public InvoiceRecordStatus Status { get; set; } = InvoiceRecordStatus.已申请;

        /// <summary>
        /// 拒绝原因(数字诺亚审核后回调时传的Remark)
        /// </summary>
        public string CallBackRemark { get; set; } = string.Empty;

        /// <summary>
        /// 发票备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 开具发票后，返回的发票PDF文件链接
        /// </summary>
        public string InvoicePDFUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// 导出【项目】发票列表接口开发
    /// </summary>
    public class DownloaUmsInvoicesResp
    {
        public DownloaUmsInvoicesResp()
        {
            this.发票月份 =
                this.发票名称 =
                this.开票主体 =
                this.发票抬头 =
                this.开票介质 =
                this.状态 =
                this.发票链接 =
                this.备注 = string.Empty;
            this.开票金额 = 0;
        }
        public DownloaUmsInvoicesResp(GetUmsInvoicesResp Model)
        {
            this.发票月份 = $"{Model.InvoicesTime:yyyy年MM月}";
            this.发票名称 = $"{Model.InvoicesTime:yyyy年MM月}发票";
            this.开票主体 = Model.IssuingCompany;
            this.发票抬头 = Model.BuyerName;
            this.开票金额 = Model.Amount;
            this.开票介质 = Model.InvoiceType.ToString();
            this.状态 = Model.Status.ToString();
            this.发票链接 = Model.InvoicePDFUrl;
            this.备注 = Model.Remark + (Model.Status == InvoiceRecordStatus.处理失败 ? Model.CallBackRemark : string.Empty);
        }

        public string 发票月份 { get; set; }
        public string 发票名称 { get; set; }

        public string 开票主体 { get; set; }
        public string? 发票抬头 { get; set; }
        public decimal 开票金额 { get; set; }
        public string 开票介质 { get; set; }

        public string 状态 { get; set; }
        public string? 备注 { get; set; }
        public string 发票链接 { get; set; } = string.Empty;
    }

    /// <summary>
    /// 获取发票详情接口开发
    /// </summary>
    public class GetInvoicesDetailReq
    {
        /// <summary>
        /// 发票记录Id
        /// </summary>
        [Required(ErrorMessage = "发票记录Id不能为空")]
        public string? RecordId { get; set; }
    }

    /// <summary>
    /// 获取【项目】可开票金额
    /// </summary>
    public class GetStatisticsForProReq
    {
        public GetStatisticsForProReq() { }
        public GetStatisticsForProReq(GetTransForProReq req)
        {
            this.ProjectId = req.ProjectId;
        }
        /// <summary>
        /// 项目ID
        /// </summary>
        [Required(ErrorMessage = "项目编码不能为空")]
        public string? ProjectId { get; set; }
    }

    /// <summary>
    /// 获取【服务奖金】 充值/退款动账记录
    /// </summary>
    public class GetStatisticsForUmsReq
    {
        public GetStatisticsForUmsReq() { }
        public GetStatisticsForUmsReq(GetTransForUmsReq req)
        {
            this.MerchantId = req.MerchantId;
            this.BuyerMerchantId = req.BuyerMerchantId;
        }
        /// <summary>
        /// 开票商户id 
        /// </summary>
        [Required(ErrorMessage = "开票商户id 不能为空")]
        public string? MerchantId { get; set; }

        /// <summary>
        /// 购方商户id 
        /// </summary>
        [Required(ErrorMessage = "购方商户id 不能为空")]
        public string? BuyerMerchantId { get; set; }
    }

    /// <summary>
    /// 获取可开票金额
    /// </summary>
    public class GetInvoiceStatisticsResp
    {
        /// <summary>
        /// 充值总额(充值金额-退款金额)
        /// *单位：元
        /// </summary>
        public decimal TransAmt { get; set; }

        /// <summary>
        /// 发票总额
        /// </summary>
        public decimal InvoicesAmt { get; set; }

        /// <summary>
        /// 可开票金额
        /// </summary>
        public decimal Invoicable { get; set; }

        /// <summary>
        /// 欠票金额
        /// </summary>
        public decimal OutInvoice { get; set; }
    }
}
