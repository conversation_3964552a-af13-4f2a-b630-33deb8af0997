using Entity.Nkp;

namespace Settlement.Domain.OutboxService;

/// <summary>
/// 本地消息表服务接口
/// </summary>
public interface IOutboxService
{
    /// <summary>
    /// 添加消息到本地消息表
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="content">消息内容</param>
    /// <param name="apiType">API类型</param>
    /// <param name="maxRetryCount">最大重试次数</param>
    /// <returns>消息ID</returns>
    Task<string> AddMessageAsync(string messageType, string content, string apiType,string orderNo,int maxRetryCount = 3);

    /// <summary>
    /// 获取待处理的消息
    /// </summary>
    /// <param name="batchSize">批次大小</param>
    /// <returns>待处理消息列表</returns>
    Task<List<OutboxMessage>> GetPendingMessagesAsync(int batchSize = 10);

    /// <summary>
    /// 标记消息为处理中
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns></returns>
    Task MarkAsProcessingAsync(string messageId);

    /// <summary>
    /// 标记消息为已完成
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns></returns>
    Task MarkAsCompletedAsync(string messageId);

    /// <summary>
    /// 标记消息为失败并增加重试次数
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="errorMessage">错误信息</param>
    /// <returns></returns>
    Task MarkAsFailedAsync(string messageId, string errorMessage);

    /// <summary>
    /// 获取需要重试的消息
    /// </summary>
    /// <param name="batchSize">批次大小</param>
    /// <returns>需要重试的消息列表</returns>
    Task<List<OutboxMessage>> GetRetryMessagesAsync(int batchSize = 10);

    /// <summary>
    /// 清理已完成的旧消息
    /// </summary>
    /// <param name="olderThanDays">保留天数</param>
    /// <returns>清理的消息数量</returns>
    Task<int> CleanupCompletedMessagesAsync(int olderThanDays = 100000);

    /// <summary>
    /// 获取消息统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<OutboxMessageStats> GetStatsAsync();
}

/// <summary>
/// 消息统计信息
/// </summary>
public class OutboxMessageStats
{
    /// <summary>
    /// 待处理消息数量
    /// </summary>
    public int PendingCount { get; set; }

    /// <summary>
    /// 处理中消息数量
    /// </summary>
    public int ProcessingCount { get; set; }

    /// <summary>
    /// 已完成消息数量
    /// </summary>
    public int CompletedCount { get; set; }

    /// <summary>
    /// 失败消息数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 总消息数量
    /// </summary>
    public int TotalCount { get; set; }
}
