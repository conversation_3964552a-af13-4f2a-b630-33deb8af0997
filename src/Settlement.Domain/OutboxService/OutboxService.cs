using Entity;
using Entity.Nkp;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Settlement.Domain.OutboxService;

/// <summary>
/// 本地消息表服务实现
/// </summary>
[Service(ServiceLifetime.Scoped)]
public class OutboxService : IOutboxService
{
    private readonly IDbContextFactory<NkpContext> _contextFactory;

    public OutboxService(IDbContextFactory<NkpContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    /// <summary>
    /// 添加消息到本地消息表
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="content">消息内容</param>
    /// <param name="apiType">API类型</param>
    /// <param name="maxRetryCount">最大重试次数</param>
    /// <returns>消息ID</returns>
    public async Task<string> AddMessageAsync(string messageType, string content, string apiType,string orderNo,int maxRetryCount = 3)
    {
        using var context = _contextFactory.CreateDbContext();

        var message = new OutboxMessage
        {
            MessageType = messageType,
            Content = content,
            ApiType = apiType,
            MaxRetryCount = maxRetryCount,
            Status = OutboxMessageStatus.Pending,
            OrderNo = orderNo,
        };

        context.OutboxMessage.Add(message);
        await context.SaveChangesAsync();

        return message.Id;
    }

    /// <summary>
    /// 获取待处理的消息
    /// </summary>
    /// <param name="batchSize">批次大小</param>
    /// <returns>待处理消息列表</returns>
    public async Task<List<OutboxMessage>> GetPendingMessagesAsync(int batchSize = 10)
    {
        using var context = _contextFactory.CreateDbContext();

        return await context.OutboxMessage
            .Where(m => m.Status == OutboxMessageStatus.Pending)
            .OrderBy(m => m.CreatedTime)
            .Take(batchSize)
            .ToListAsync();
    }

    /// <summary>
    /// 标记消息为处理中
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns></returns>
    public async Task MarkAsProcessingAsync(string messageId)
    {
        using var context = _contextFactory.CreateDbContext();

        var message = await context.OutboxMessage.FindAsync(messageId);
        if (message != null)
        {
            message.Status = OutboxMessageStatus.Processing;
            message.UpdatedTime = DateTime.Now;
            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// 标记消息为已完成
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns></returns>
    public async Task MarkAsCompletedAsync(string messageId)
    {
        using var context = _contextFactory.CreateDbContext();

        var message = await context.OutboxMessage.FindAsync(messageId);
        if (message != null)
        {
            message.Status = OutboxMessageStatus.Completed;
            message.ProcessedTime = DateTime.Now;
            message.UpdatedTime = DateTime.Now;
            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// 标记消息为失败并增加重试次数
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="errorMessage">错误信息</param>
    /// <returns></returns>
    public async Task MarkAsFailedAsync(string messageId, string errorMessage)
    {
        using var context = _contextFactory.CreateDbContext();

        var message = await context.OutboxMessage.FindAsync(messageId);
        if (message != null)
        {
            message.RetryCount++;
            message.ErrorMessage = errorMessage;
            message.UpdatedTime = DateTime.Now;

            if (message.RetryCount >= message.MaxRetryCount)
            {
                message.Status = OutboxMessageStatus.Failed;
            }
            else
            {
                message.Status = OutboxMessageStatus.Pending;
                // 设置下次重试时间（指数退避）
                var delayMinutes = Math.Pow(2, message.RetryCount);
                message.NextRetryTime = DateTime.Now.AddMinutes(delayMinutes);
            }

            await context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// 获取需要重试的消息
    /// </summary>
    /// <param name="batchSize">批次大小</param>
    /// <returns>需要重试的消息列表</returns>
    public async Task<List<OutboxMessage>> GetRetryMessagesAsync(int batchSize = 10)
    {
        using var context = _contextFactory.CreateDbContext();

        var now = DateTime.Now;
        return await context.OutboxMessage
            .Where(m => m.Status == OutboxMessageStatus.Pending
                       && m.RetryCount > 0
                       && (m.NextRetryTime == null || m.NextRetryTime <= now))
            .OrderBy(m => m.NextRetryTime ?? m.CreatedTime)
            .Take(batchSize)
            .ToListAsync();
    }

    /// <summary>
    /// 清理已完成的旧消息
    /// </summary>
    /// <param name="olderThanDays">保留天数</param>
    /// <returns>清理的消息数量</returns>
    public async Task<int> CleanupCompletedMessagesAsync(int olderThanDays = 100000)
    {
        using var context = _contextFactory.CreateDbContext();

        var cutoffDate = DateTime.Now.AddDays(-olderThanDays);
        var messagesToDelete = await context.OutboxMessage
            .Where(m => m.Status == OutboxMessageStatus.Completed
                       && m.ProcessedTime < cutoffDate)
            .ToListAsync();

        if (messagesToDelete.Any())
        {
            context.OutboxMessage.RemoveRange(messagesToDelete);
            await context.SaveChangesAsync();
        }

        return messagesToDelete.Count;
    }

    /// <summary>
    /// 获取消息统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public async Task<OutboxMessageStats> GetStatsAsync()
    {
        using var context = _contextFactory.CreateDbContext();

        var stats = await context.OutboxMessage
            .GroupBy(m => m.Status)
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToListAsync();

        var result = new OutboxMessageStats();
        foreach (var stat in stats)
        {
            switch (stat.Status)
            {
                case OutboxMessageStatus.Pending:
                    result.PendingCount = stat.Count;
                    break;
                case OutboxMessageStatus.Processing:
                    result.ProcessingCount = stat.Count;
                    break;
                case OutboxMessageStatus.Completed:
                    result.CompletedCount = stat.Count;
                    break;
                case OutboxMessageStatus.Failed:
                    result.FailedCount = stat.Count;
                    break;
            }
        }

        result.TotalCount = stats.Sum(s => s.Count);
        return result;
    }
}
