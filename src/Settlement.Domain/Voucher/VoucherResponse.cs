﻿using Config.Enums.Nkp;
using Entity.Nkp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Settlement.Domain.Voucher
{
    public class VoucherListResp
    {
        public VoucherListResp() { }
        public VoucherListResp(Entity.Nkp.Voucher Model)
        {
            this.Id = Model.Id;
            this.MerchantId = Model.InMerchantId;
            this.BuyerMerchantId = Model.OutMerchantId;
            this.VoucherTime = Model.VoucherTime;
            this.VoucherName = Model.Subject;
            this.Provider = Model.InMerchantName;
            this.Subject = Model.OutMerchantName;
            this.Amount = Model.Amount;
            this.VoucherType = Model.VoucherType;
            this.VoucherStatus = Model.VoucherStatus;
            this.VoucherUrl = Model.VoucherUrl ?? string.Empty;
            this.Remark = Model.Remark;
            this.CreateTime = Model.CreatedTime;
        }
        /// <summary>
        /// 凭证ID
        /// </summary>
        [Key]
        public string Id { get; set; } = Entity.EntityTools.SnowflakeId();
        /// <summary>
        /// 商户ID
        /// </summary>
        public string MerchantId { get; set; } = default!;
        /// <summary>
        /// 购方商户ID
        /// </summary>
        public string BuyerMerchantId { get; set; } = default!;
        /// <summary>
        /// 凭证时间
        /// </summary>
        public DateTime VoucherTime { get; set; } = DateTime.Now.Date;
        /// <summary>
        /// 凭证名称
        /// </summary>
        public string VoucherName { get; set; } = default!;

        /// <summary>
        /// 服务提供方
        /// </summary>
        public string Provider { get; set; } = default!;
        /// <summary>
        /// 凭证主体
        /// </summary>
        public string Subject { get; set; } = default!;
        /// <summary>
        /// 凭证总额
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 凭证类型:1=订单合同
        /// </summary>
        public VoucherType VoucherType { get; set; } = VoucherType.订单转账;
        /// <summary>
        /// 凭证状态:1=生效中
        /// </summary>
        public VoucherStatus VoucherStatus { get; set; } = VoucherStatus.生效中;
        /// <summary>
        /// 凭证地址
        /// </summary>
        public string? VoucherUrl { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = default!;
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;
    }

    public class VoucherListExportResp
    {
        public VoucherListExportResp(VoucherListResp Model)
        {
            this.合同月份 = Model.VoucherTime.ToString("yyyy年MM月");
            this.合同名称 = Model.VoucherName;
            this.服务提供方 = Model.Provider;
            this.合同主体 = Model.Subject;
            this.合同总额 = Model.Amount;
            this.合同类型 = Model.VoucherType.ToString();
            this.合同状态 = Model.VoucherStatus.ToString();
            this.合同地址 = Model.VoucherUrl ?? string.Empty;
            this.备注 = Model.Remark ?? string.Empty;
        }
        public VoucherListExportResp(Entity.Nkp.Voucher Model)
        {
            this.合同月份 = Model.VoucherTime.ToString("yyyy年MM月");
            this.合同名称 = Model.Subject;
            this.服务提供方 = Model.InMerchantName;
            this.合同主体 = Model.OutMerchantName;
            this.合同总额 = Model.Amount;
            this.合同类型 = Model.VoucherType.ToString();
            this.合同状态 = Model.VoucherStatus.ToString();
            this.合同地址 = Model.VoucherUrl ?? string.Empty;
            this.备注 = Model.Remark ?? string.Empty;
        }
        public string 合同月份 { get; set; }
        public string 合同名称 { get; set; }
        public string 服务提供方 { get; set; }
        public string 合同主体 { get; set; }
        public decimal 合同总额 { get; set; }
        public string 合同类型 { get; set; }
        public string 合同状态 { get; set; }
        public string 合同地址 { get; set; }
        public string 备注 { get; set; }
    }
}
