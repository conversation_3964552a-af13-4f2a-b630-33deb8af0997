﻿using Config.Enums.Nkp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Settlement.Domain.Voucher
{
    /// <summary>
    /// 凭证记录
    /// </summary>
    public class SaveVoucherReq
    {
        /// <summary>
        /// 商户ID
        /// </summary>
        [Required(ErrorMessage = "商户ID不能为空")]
        public string MerchantId { get; set; } = default!;
        /// <summary>
        /// 购方商户ID
        /// </summary>
        [Required(ErrorMessage = "购方商户ID不能为空")]
        public string BuyerMerchantId { get; set; } = default!;
        /// <summary>
        /// 凭证时间
        /// </summary>
        public DateTime VoucherTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 凭证总额
        /// </summary>
        [Required(ErrorMessage = "凭证总额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "凭证总额不能小于0.01")]
        public decimal Amount { get; set; }
        /// <summary>
        /// 凭证类型:1=订单合同
        /// </summary>
        public VoucherType VoucherType { get; set; } = VoucherType.订单转账;
        /// <summary>
        /// 凭证状态:1=生效中
        /// </summary>
        public VoucherStatus VoucherStatus { get; set; } = VoucherStatus.生效中;
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = default!;
    }

    public class VoucherGetList : ReqPageBase
    {
        public VoucherGetList() { }
        public VoucherGetList(VoucherDownloadList req, int PageIndex = 1, int PageSize = 99999)
        {
            this.MerchantId = req.MerchantId;
            this.BeginTime = req.BeginTime;
            this.EndTime = req.EndTime;
            this.Search = req.Search;

            this.PageIndex = PageIndex;
            this.PageSize = PageSize;
        }
        /// <summary>
        /// 商户ID
        /// </summary>
        [Required(ErrorMessage = "商户ID不能为空")]
        public string MerchantId { get; set; } = default!;
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }

        /// <summary>
        /// 截止时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 模糊检索
        /// </summary>
        public string? Search { get; set; }
    }

    public class VoucherDownloadList
    {
        /// <summary>
        /// 商户ID
        /// </summary>
        [Required(ErrorMessage = "商户ID不能为空")]
        public string MerchantId { get; set; } = default!;
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }

        /// <summary>
        /// 截止时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 模糊检索
        /// </summary>
        public string? Search { get; set; }
    }
}
