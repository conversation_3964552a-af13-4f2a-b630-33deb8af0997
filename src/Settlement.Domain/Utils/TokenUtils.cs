using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Settlement.Domain.Ums;
using Settlement.Infrastructure.Common.Http;

namespace Settlement.Domain.Utils;

public class TokenUtils
{
    public static string GenerateSignature(string appId, string timestamp,
        string nonce, string appKey)
    {
        String plaintext = appId + timestamp + nonce + appKey;
        try
        {
            var strRes = Encoding.Default.GetBytes(plaintext);
            HashAlgorithm iSha = new SHA1CryptoServiceProvider();
            strRes = iSha.ComputeHash(strRes);
            var signature = new StringBuilder();
            foreach (byte iByte in strRes)
            {
                signature.AppendFormat("{0:x2}", iByte);
            }

            return signature.ToString();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message);
        }

        return null;
    }

    public static TokenResponse GetToken(OpenApiContext context)
    {
        int retry = 0;
        ConfigBean configBean = context.ConfigBean;
        TokenResponse acquiredToken = null;
        if (TokenValid(context.CurrentToken) && !TokenTimeout(context.CurrentToken))
        {
            return context.CurrentToken;
        }

        while (retry++ < configBean.TokenAcquireReties)
        {
            acquiredToken = AcquireAccessToken(context);
            if (null != acquiredToken)
            {
                break;
            }
            else if (retry < configBean.TokenAcquireReties)
            {
                try
                {
                    Thread.Sleep(200);
                }
                catch (Exception e)
                {
                    throw new Exception(" 重试token申请出错:" + e.Message);
                }
            }
            else
            {
                throw new Exception(" 申请token超过重试次数 bye-bye");
                break;
            }
        }

        return acquiredToken;
    }

    private static TokenResponse AcquireAccessToken(OpenApiContext context)
    {
        string appId = context.AppId;
        string appKey = context.AppKey;
        ConfigBean configBean = context.ConfigBean;
        TokenRequest tokenRequest = new TokenRequest(appId, appKey);
        string request = JsonSerializer.Serialize(tokenRequest);
        string url = context.OpenServUrl + "" + configBean.Version + "" + configBean.TokenServiceCode;
        string response = HttpTransport.Instance.DoPost(configBean.IsProd, url, null, request);
        if (string.IsNullOrEmpty(response))
        {
            throw new Exception(" 获取token失败");
        }
        TokenResponse? tokenResponse  = JsonSerializer.Deserialize<TokenResponse>(response);
        if (tokenResponse != null)
        {
            tokenResponse.effectTime = DateTime.Now;
            tokenResponse.timeout = tokenResponse.expiresIn;
            tokenResponse.aheadInterval = configBean.TokenAcquireAheadInterval;
            return tokenResponse;
        }
        return new TokenResponse();
    }

    private static bool TokenValid(TokenResponse tokenBean)
    {
        return (null != tokenBean
                && !string.IsNullOrEmpty(tokenBean.accessToken)
                && null != tokenBean.effectTime
                && tokenBean.timeout > 0
                && tokenBean.aheadInterval > 0 && tokenBean.timeout > tokenBean
                    .aheadInterval);
    }

    private static bool TokenTimeout(TokenResponse tokenBean)
    {
        TimeSpan span = DateTime.Now - tokenBean.effectTime;
        int elapseInterval = span.Minutes;
        int maxEffectiveInterval = tokenBean.timeout - tokenBean.aheadInterval;
        bool isTimeout = (elapseInterval > maxEffectiveInterval);
        if (isTimeout)
        {
            throw new Exception("exception:token过期了");
        }

        return isTimeout;
    }

    private static byte[] ConvertHexStringToByteArray(string hexString)
    {
        if (hexString.Length % 2 != 0)
        {
            throw new ArgumentException(String.Format(CultureInfo.InvariantCulture,
                "The binary key cannot have an odd number of digits: {0}", hexString));
        }

        byte[] HexAsBytes = new byte[hexString.Length / 2];
        for (int index = 0; index < HexAsBytes.Length; index++)
        {
            string byteValue = hexString.Substring(index * 2, 2);
            HexAsBytes[index] = byte.Parse(byteValue, NumberStyles.HexNumber, CultureInfo.InvariantCulture);
        }

        return HexAsBytes;
    }
}