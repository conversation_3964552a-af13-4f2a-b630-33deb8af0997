using System.Net;
using System.Text;
using Newtonsoft.Json;
using Settlement.Domain.Ums;

namespace Settlement.Infrastructure.Common.Http;

public sealed class HttpTransport: IHttpTransport
{
    
    private static volatile HttpTransport instance;
    private static readonly object obj = new object();
    private HttpTransport() { }
    public static HttpTransport Instance
    {
        get
        {
            if (null == instance)
            {
                lock (obj)
                {
                    if (null == instance)
                    {
                        instance = new HttpTransport();
                    }
                }

            }
            return instance;
        }
    }

    public string DoPost(bool isDebug, string url, string token, string request)
    {
        try
        {
            if (!isDebug)
            {
                // _logger.LogInformation("url:" + url);
                // _logger.LogInformation("requst params:" + request);
            }
            WebClient webClient = new WebClient();
            Encoding  encoding = Encoding.UTF8;
            string result = string.Empty;
            webClient.Headers.Add("Content-Type", "application/json");
            request = request.Replace("'", "\"");
            byte[] postData = encoding.GetBytes(request);
            if (!string.IsNullOrEmpty(token))
            {
                webClient.Headers.Add("Authorization", "OPEN-ACCESS-TOKEN AccessToken="+token);
            }
            byte[] responseData = webClient.UploadData(url, "POST", postData);
            string responseContent = encoding.GetString(responseData);
            //_logger.LogInformation("response msg:" + responseContent);
            return responseContent;
        }
        catch (Exception e)
        {
            //_logger.LogError("HttpTransport.DoPost",e);
        }
        return null;
    }

    public string DoPost(OpenApiContext context, string request)
    {
        string url = context.ApiServiceUrl;
        ConfigBean configBean = context.ConfigBean;
        context.Request = request;
        if (!configBean.IsProd) {
            //_logger.LogInformation("process context:" + JsonConvert.SerializeObject(context));
            //_logger.LogInformation("requst params:" + request);
        }
        WebClient webClient = new WebClient();
        Encoding encoding = Encoding.UTF8;
        string result = string.Empty;
        webClient.Headers.Add("Content-Type", "application/json");
        TokenResponse currentToken = context.CurrentToken;
        webClient.Headers.Add("Authorization", "OPEN-ACCESS-TOKEN AccessToken=" + currentToken.accessToken);
        byte[] postData = encoding.GetBytes(request);
        byte[] responseData = webClient.UploadData(url, "POST", postData);
        string responseContent = encoding.GetString(responseData);
        //_logger.LogInformation("response msg:" + responseContent);
        context.Response = responseContent;
        return responseContent;
    }
}