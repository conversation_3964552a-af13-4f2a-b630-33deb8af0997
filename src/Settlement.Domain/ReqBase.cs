﻿using System.ComponentModel.DataAnnotations;

namespace Settlement.Domain
{
    public class ReqPageBase
    {
        /// <summary>
        /// 页码，从1开始
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "页码不能小于1")]
        public int PageIndex { get; set; } = 1;
        /// <summary>
        /// 每页显示记录数
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "每页显示记录数不能小于1")]
        public int PageSize { get; set; } = 1;
    }

    public class RespPageBase<T>
    {
        public RespPageBase(List<T> Rows, int Total, int PageIndex, int PageSize)
        {
            this.Total = Total;
            this.Rows = Rows;
            this.IsLast = PageIndex * PageSize >= Total;
        }
        /// <summary>
        /// 总记录数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 是否最后一页
        /// </summary>
        public bool IsLast { get; set; } = true;

        /// <summary>
        /// 最后一条记录的时间戳
        /// </summary>
        public long? LastTimeStamp { get; set; }

        public List<T> Rows { get; set; } = default!;
    }
}
