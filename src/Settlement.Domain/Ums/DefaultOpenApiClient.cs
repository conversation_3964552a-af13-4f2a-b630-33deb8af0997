using System.Text;
using Microsoft.OpenApi.Exceptions;
using Settlement.Infrastructure.Common.Http;

namespace Settlement.Domain.Ums;

public class DefaultOpenApiClient<T> : OpenApiclient<T> where T : OpenApiResponse
{
    /*
        * 开放平台URL
        */
        private string serverUrl;
        public string ServerUrl
        {
            set { serverUrl = value; }
            get { return serverUrl; }
        }
        /**
         * appId
         */
        private string appId;
        public string AppId
        {
            set { appId = value; }
            get { return appId; }
        }
        /**
         * appKey
         */
        private string appKey;
        public string AppKey
        {
            set { appKey = value; }
            get { return appKey; }
        }
        /**
         * 字符集格式
         */
        private string encodeCharSet;
        public string EncodeCharSet
        {
            set { encodeCharSet = value; }
            get { return encodeCharSet; }
        }
        public DefaultOpenApiClient(string serverUrl, string appId, string appKey)
        {
            this.serverUrl = serverUrl;
            this.appId = appId;
            this.appKey = appKey;
        }
        public DefaultOpenApiClient(string serverUrl, string appId, string appKey, string encodeCharSet)
        {
            this.serverUrl = serverUrl;
            this.appId = appId;
            this.appKey = appKey;
            this.encodeCharSet = encodeCharSet;
        }
        public T execute(OpenApiRequest<T> openApiRequest)
        {
            OpenApiParser<T> openApiParser = new ObjectJsonParser<T>();
            return execute_(openApiRequest, openApiParser, null, null);
        }
        public T execute(OpenApiRequest<T> openApiRequest, String token)
        {
            return execute(openApiRequest, token, null);
        }

        public T execute(OpenApiRequest<T> openApiRequest, String token, String encodeCharSet)
        {
            OpenApiParser<T> openApiParser = new ObjectJsonParser<T>();
            return execute_(openApiRequest, openApiParser, token, encodeCharSet);
        }

        public T execute_(OpenApiRequest<T> openApiRequest, OpenApiParser<T> openApiParser,
            string token, string encodeCharSet)
        {
            ConfigBean configBean = new ConfigBean();
            OpenApiContext context = new OpenApiContext();
            T? trsp = null;
            string response = "";
            try
            {
                if (string.IsNullOrEmpty(serverUrl)) throw new Exception("通讯地址未设置！");
                if (string.IsNullOrEmpty(appId)) throw new Exception("开发者appId未设置！");
                if (string.IsNullOrEmpty(appKey)) throw new Exception("开发者appKey未设置！");
                //请求检查
                String request = openApiParser.ValidRequest(openApiRequest);
                context.StartTime = DateTime.Now.ToFileTime();
                context.RequestId = Guid.NewGuid().ToString("N");
                context.OpenServUrl = serverUrl;
                string url = ServerUrl + "" + openApiRequest.ApiVersion() + "" + openApiRequest.ServiceCode();
                context.ApiServiceUrl = url;
                context.ApiMethodName = openApiRequest.ApiMethodName();
                context.Version = openApiRequest.ApiVersion();
                context.AppId = appId;
                context.AppKey = appKey;
                context.ConfigBean = configBean;
                context.ServiceCode = openApiRequest.ServiceCode();
                if (openApiRequest.NeedToken())
                {
                    OpenApiCache.GetCurrentToken(context);
                    response = HttpTransport.Instance.DoPost(context, request);
                }
                else
                {
                    response = HttpTransport.Instance.DoPost(configBean.IsProd, url, token, request);
                }
                if (string.IsNullOrEmpty(response)) throw new OpenApiException("服务提供方未返回！");
                if (!string.IsNullOrEmpty(encodeCharSet))
                {
                    byte[] srcBytes = Encoding.GetEncoding(encodeCharSet).GetBytes(response);
                    trsp = openApiParser.Parse(Encoding.GetEncoding(encodeCharSet).GetString(srcBytes));
                }
                else
                {
                    trsp = openApiParser.Parse(response);
                }
                //token超时异常处理
                if (trsp != null)
                {
                    string errcode = trsp.errCode;
                    int i = 0;
                    while (i>0) {
                        if (string.Equals(errcode, Constants.ERR_CODE.AUTHENTICATION_FAILED)) {
                        
                            OpenApiCache.RemoveCache(appId);
                            execute_(openApiRequest, openApiParser, token, encodeCharSet);
                            i++;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                throw new Exception("异常："+e.StackTrace + "{}:\n" + e.Message);
            }
            return trsp;
        }
}