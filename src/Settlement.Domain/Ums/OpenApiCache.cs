using Settlement.Domain.Utils;

namespace Settlement.Domain.Ums;

public class OpenApiCache
{
    private static readonly Dictionary<string, TokenResponse> Cache = new Dictionary<string, TokenResponse>();
    public static TokenResponse GetCurrentToken(OpenApiContext context)
    {
        string appId = context.AppId;
        TokenResponse tokenResponse = null;
        if (Cache.ContainsKey(appId)) {
            tokenResponse = Cache[appId];
        }
        else
        {
            tokenResponse = TokenUtils.GetToken(context);
            Cache[appId] = tokenResponse;
        }
        context.CurrentToken = tokenResponse;
        return tokenResponse;
    }
    public static void RemoveCache(string appId) {
        if(Cache.ContainsKey(appId)) Cache.Remove(appId);
    }
}