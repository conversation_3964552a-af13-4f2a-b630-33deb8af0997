using System.Text.Json.Serialization;
using Settlement.Domain.Utils;
using Settlement.Infrastructure.Common;

namespace Settlement.Domain.Ums;

public class TokenRequest : OpenApiRequest<TokenResponse>
{
    
    public TokenRequest(string appId, string appKey)
    {
        this.AppId = appId;
        this.AppKey = appKey;
        this.Timestamp = string.Format("{0:yyyyMMddHHmmss}", DateTime.Now);
        this.Nonce = Guid.NewGuid().ToString("D");
        this.SignMethod = "SHA256";
        this.Signature = TokenUtils.GenerateSignature(this.AppId, this.Timestamp, this.Nonce, this.AppKey);
    }
    
    [JsonPropertyName("appId")]
    public string AppId { get; set; }

    [JsonPropertyName("appKey")]
    public string AppKey { get; set; }

    [JsonPropertyName("timestamp")]
    public string Timestamp { get; set; }
    
    [JsonPropertyName("nonce")]
    public string Nonce { get; set; }

    [JsonPropertyName("signMethod")]
    public string SignMethod { get; set; }

    [JsonPropertyName("signature")]
    public string Signature { get; set; }


    
    
    public Type ResponseClass<T>() where T : OpenApiResponse
    {
        return typeof(TokenResponse);
    }

    public string ApiVersion()
    {
        return "v1";
    }

    public string ApiMethodName()
    {
        return "获取开放平台token";
    }

    public string ServiceCode()
    {
        return "/token/access";
    }

    public bool NeedToken()
    {
        return false;
    }
}