namespace Settlement.Domain.Ums;

public class Constants
{
    public static class ERR_CODE
    {
        public static string NORMAL = "0000";
        public static string AUTHENTICATION_FAILED = "1000";
        public static string AUTHORIZATION_FAILED = "1001";
        public static string USER_AUTHENTICATION_FAILED = "2000";
        public static string USER_OPERATION_FAILED = "2001";
        public static string EXTERNAL_SESSION_NOT_FOUND = "2100";
        public static string ILLEGAL_CALL = "9000";
        public static string ILLEGAL_PARAMETER = "9001";
        public static string FATAL = "9999";
    }
    public static class ERR_INFO
    {
        public static string NORMAL = "正常";
        public static string AUTHENTICATION_FAILED = "认证失败";
        public static string AUTHORIZATION_FAILED = "授权失败";
        public static string USER_AUTHENTICATION_FAILED = "用户认证失败";
        public static string USER_OPERATION_FAILED = "用户操作失败";
        public static string EXTERNAL_SESSION_NOT_FOUND = "外部会话未找到";
        public static string ILLEGAL_CALL = "非法访问";
        public static string ILLEGAL_PARAMETER = "请求参数校验失败";
        public static string FATAL = "系统错误";
    }
    
}