namespace Settlement.Domain.Ums;

public class TokenResponse : OpenApiResponse
{
    /**
         * 服务授权令牌
         */
    public string accessToken { set; get; }
    /**
     * 有效期
     */
    public int expiresIn { set; get; }
    /**
     * 令牌开始生效的时间，以收到时间为准
     */
    public DateTime effectTime { set; get; }

    /**
     * 令牌的过期时间，单位为秒
     */
    public int timeout { set; get; }
    /**
     * 在令牌过期前，要提前重新申请
     */
    public int aheadInterval { set; get; }
    
}