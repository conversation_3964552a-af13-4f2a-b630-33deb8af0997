namespace Settlement.Domain.Ums;

public class ConfigBean
{
    /*
      字符集格式
       */
    private string charSet = "UTF-8";
    /*
    是否是生产环境
     */
    private bool isProd = false;

    private string version = "v1";
    /**
     * token申请失效后，重试次数
     */
    private int tokenAcquireReties = 3;
    /**
     * token提前申请的时间
     */
    private int tokenAcquireAheadInterval = 600;
    /*
    获取token servicecode
     */
    private string tokenServiceCode = "/token/access";

    public string CharSet { set { charSet = value; } get { return charSet; } }

    public bool IsProd { set { isProd = value; } get { return isProd; } }

    public string Version {set { version = value; } get { return version; }}

    public int TokenAcquireReties { set { tokenAcquireReties = value; } get { return tokenAcquireReties; } }

    public int TokenAcquireAheadInterval { set { tokenAcquireAheadInterval = value; } get { return tokenAcquireAheadInterval; } }

    public string TokenServiceCode {
        set { tokenServiceCode = value; }
        get { return tokenServiceCode; }
    }
}