namespace Settlement.Domain.Ums;

public class OpenApiContext
{
        private string  requestId;
        public string RequestId { set { requestId = value; } get { return requestId; } }
        private TokenResponse currentToken;
        public TokenResponse CurrentToken { set { currentToken = value; } get { return currentToken; } }
        private long startTime;
        public long StartTime { set { startTime = value; } get { return startTime; } }
        private long endTime;
        public long EndTime { set { endTime = value; } get { return endTime; } }
        private string request;
        public string Request { set { request = value; } get { return request; } }
        private string response;
        public string Response { set { response = value; } get { return response; } }
        private Dictionary<string, Object> params_;
        public Dictionary<string, Object> Params_ { set { params_ = value; } get { return params_; } }
        private string apiMethodName;
        public string ApiMethodName { set { apiMethodName = value; } get { return apiMethodName; } }
        private string serviceCode;
        public string ServiceCode { set { serviceCode = value; } get { return serviceCode; } }
        private string openServUrl;
        public string OpenServUrl { set { openServUrl = value; } get { return openServUrl; } }
        private string version;
        public string Version { set { version = value; } get { return version; } }
        private string appId;
        public string AppId { set { appId = value; } get { return appId; } }
        private string appKey;
        public string AppKey { set { appKey = value; } get { return appKey; } }
        private string sandboxKey;
        public string SandboxKey { set { sandboxKey = value; } get { return sandboxKey; } }
        private ConfigBean configBean;
        public ConfigBean ConfigBean { set { configBean = value; } get { return configBean; } }
        private string apiServiceUrl;
        public string ApiServiceUrl { set { apiServiceUrl = value; } get { return apiServiceUrl; } }
    
}