using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Swashbuckle.AspNetCore.Annotations;

namespace Settlement.Domain.AutoReg;

public class AutoRegModel
{
}

public class RegNotifyReq
{
    // /// <summary>
    // /// 返回状态码
    // /// 0000 成功
    // /// </summary>
    // [JsonPropertyName("res_code")]
    // public string? ResCode { get; set; }

    /// <summary>
    /// 来源平台账户
    /// 接入平台用户唯一标识，不超过50位
    /// </summary>
    [JsonPropertyName("accesser_acct")]
    public string? AccesserAcct { get; set; } 

    /// <summary>
    /// 自助签约平台流水号
    /// 档案资料上传接口返回
    /// </summary>
    [JsonPropertyName("ums_reg_id")]
    public string UmsRegId { get; set; } = default!;

    /// <summary>
    /// 申请状态
    /// 00：签约中 01：签约成功 02：入网审核中 03：入网成功 04：入网失败
    /// 05：对公账户待验证或异常 06：风控审核中 28：资料验证失败
    /// 31：冻结账户 99：其它错误
    /// </summary>
    [JsonPropertyName("apply_status")]
    public string? ApplyStatus { get; set; }

    /// <summary>
    /// 申请状态对应的描述信息
    /// </summary>
    [JsonPropertyName("apply_status_msg")]
    public string? ApplyStatusMsg { get; set; }

    /// <summary>
    /// 商户号
    /// 入网成功时返回
    /// 8983102500071141（系统审核接入方式本参数不返回，商户号体现在多用用商户号中）
    /// </summary>
    [JsonPropertyName("mer_no")]
    public string? MerNo { get; set; }

    /// <summary>
    /// 企业号
    /// 对于入网成功且开通泛账户的商户，返回企业号，此返回参数非实时更新
    /// </summary>
    [JsonPropertyName("company_no")]
    public string? CompanyNo { get; set; }

    /// <summary>
    /// 失败原因
    /// 协议退回原因
    /// </summary>
    [JsonPropertyName("fail_reason")]
    public string? FailReason { get; set; }

    /// <summary>
    /// 商户多应用信息
    /// </summary>
    [JsonPropertyName("mapp_info_list")]
    public List<MappInfo> MappInfoList { get; set; }

    /// <summary>
    /// 微信备案商户号
    /// </summary>
    [JsonPropertyName("wechatPayRecordMchntNo")]
    public string WechatPayRecordMchntNo { get; set; }

    /// <summary>
    /// 支付宝备案商户号
    /// </summary>
    [JsonPropertyName("aliPayRecordMchntNo")]
    public string AliPayRecordMchntNo { get; set; }

    /// <summary>
    /// 银联云闪付备案商户号
    /// </summary>
    [JsonPropertyName("unionPayRecordMchntNo")]
    public string UnionPayRecordMchntNo { get; set; }
}

public class MappInfo
{
    /// <summary>
    /// 多应用商户号
    /// </summary>
    [JsonPropertyName("mapp_no")]
    public string MappNo { get; set; }

    /// <summary>
    /// 多应用终端号列表
    /// 若有多个终端，逗号分隔
    /// </summary>
    [JsonPropertyName("term_app_no_list")]
    public string TermAppNoList { get; set; }

    /// <summary>
    /// 卡种费率列表
    /// </summary>
    [JsonPropertyName("card_type_fee_list")]
    public List<CardTypeFee> CardTypeFeeList { get; set; }

    /// <summary>
    /// 业务类型ID
    /// </summary>
    [JsonPropertyName("apptype_id")]
    public string ApptypeId { get; set; }
}

public class CardTypeFee
{
    /// <summary>
    /// 卡种
    /// </summary>
    [JsonPropertyName("card_type")]
    public string CardType { get; set; }

    /// <summary>
    /// 费率
    /// </summary>
    [JsonPropertyName("card_fee")]
    public string CardFee { get; set; }
}

public class ComplexUploadReq
{
    /// <summary>
    /// 服务名称
    /// </summary>
    [SwaggerIgnore]
    [JsonPropertyName("service")]
    public string Service { get; set; } = "complex_upload";
    

    /// <summary>
    /// 接入平台id  外部系统平台标识，由ums分配
    /// </summary>
    [SwaggerIgnore]
    [JsonPropertyName("accesser_id")]
    //[Required(ErrorMessage = "接入平台id不能为空")]
    public string AccesserId { get; set; } = default!;

    /// <summary>
    /// 签名方式 SHA-256，必须大写。
    /// </summary>
    [SwaggerIgnore]
    [JsonPropertyName("sign_type")]
    public string SignType { get; set; } = "SHA-256";

    /// <summary>
    /// 请求时间
    /// </summary>
    [SwaggerIgnore]
    [JsonPropertyName("request_date")]
    public string RequestDate { get; set; } = DateTime.Now.ToString("yyyyMMddHHmmss");

    /// <summary>
    /// 请求流水号
    /// </summary>
    [SwaggerIgnore]
    [JsonPropertyName("request_seq")]
    //[Required(ErrorMessage = "请求流水号不能为空")]
    //[MaxLength(30, ErrorMessage = "请求流水号不能超过30位")]
    public string RequestSeq { get; set; } = default!;

    /// <summary>
    /// 平台用户id
    /// </summary>
    [SwaggerIgnore]
    [JsonPropertyName("accesser_user_id")]
    //[Required(ErrorMessage = "平台用户id不能为空")]
    //[MaxLength(50, ErrorMessage = "平台用户id不能超过50个字符")]
    public string AccesserUserId { get; set; } = default!;
    
    /// <summary>
    /// 商户id
    /// </summary>
    [Required(ErrorMessage = "商户id不能为空")]
    public string MerchantId { get; set; } = default!;

    /// <summary>
    /// 注册类型
    /// </summary>
    [JsonPropertyName("reg_mer_type")]
    [Required(ErrorMessage = "注册类型不能为空")]
    public string RegMerType { get; set; } = default!;

    /// <summary>
    /// 法人身份证姓名
    /// </summary>
    [JsonPropertyName("legal_name")]
    [Required(ErrorMessage = "法人身份证姓名不能为空")]
    [MaxLength(30, ErrorMessage = "法人身份证姓名不能超过30个字符")]
    public string LegalName { get; set; } = default!;

    /// <summary>
    /// 法人身份证号
    /// </summary>
    [JsonPropertyName("legal_idcard_no")]
    [Required(ErrorMessage = "法人身份证号不能为空")]
    [MaxLength(36, ErrorMessage = "法人身份证号不能超过36个字符")]
    public string LegalIdcardNo { get; set; } = default!;

    /// <summary>
    /// 法人手机号
    /// </summary>
    [JsonPropertyName("legal_mobile")]
    [Required(ErrorMessage = "法人手机号不能为空")]
    [MaxLength(30, ErrorMessage = "法人手机号不能超过30个字符")]
    public string LegalMobile { get; set; } = default!;

    /// <summary>
    /// 法人邮箱
    /// </summary>
    [JsonPropertyName("legal_email")]
    [MaxLength(30, ErrorMessage = "法人邮箱不能超过30个字符")]
    public string? LegalEmail { get; set; }

    /// <summary>
    /// 法人证件开始日期 yyyy-MM-dd
    /// </summary>
    [JsonPropertyName("legalCardBeginDate")]
    [Required(ErrorMessage = "法人证件开始日期不能为空")]
    public DateOnly LegalCardBeginDate { get; set; } = default!;

    /// <summary>
    /// 法人代表证件截止日期 yyyy-MM-dd
    /// </summary>
    [JsonPropertyName("legal_card_deadline")]
    [Required(ErrorMessage = "法人代表证件截止日期不能为空")]
    public DateOnly LegalCardDeadline { get; set; } = default!;

    /// <summary>
    /// 法人性别 企业类型为小微商户时必填，参数按国际标准传 0-未知的性别  1-男性   2-女性    5-女性改（变）为男性 6-男性改（变）为女性 9-未说明的性别
    /// </summary>
    [JsonPropertyName("legal_sex")]
    public string? LegalSex { get; set; }

    /// <summary>
    /// 法人职业  企业类型为小微商户时必填 0-各类专业、技术人员  1-国家机关、党群组织、企事业单位的负责人 2-办事人员和有关人员 3-商业工作人员 4-服务性工作人员 5-农林牧渔劳动者 6-生产工作、运输工作和部分体力劳动者 7-不便分类的其他劳动者
    /// </summary>
    [JsonPropertyName("legal_occupation")]
    public string? LegalOccupation { get; set; }

    /// <summary>
    /// 法人职业详细描述
    /// </summary>
    [JsonPropertyName("legalmanCareerDesc")]
    [MaxLength(40, ErrorMessage = "法人职业详细描述不能超过40个字符")]
    public string? LegalmanCareerDesc { get; set; }

    /// <summary>
    /// 商户营业名称
    /// </summary>
    [JsonPropertyName("shop_name")]
    [Required(ErrorMessage = "商户营业名称不能为空")]
    [MaxLength(60, ErrorMessage = "商户营业名称不能超过60个字符")]
    public string ShopName { get; set; } = default!;

    /// <summary>
    /// 开户行行号 （所属支行查询接口返回）
    /// </summary>
    [JsonPropertyName("bank_no")]
    [Required(ErrorMessage = "开户行行号不能为空")]
    [MaxLength(18, ErrorMessage = "开户行行号不能超过18个字符")]
    public string BankNo { get; set; } = default!;

    /// <summary>
    /// 账户类型 '0:个人账户  1:公司账户';
    /// </summary>
    [JsonPropertyName("bank_acct_type")]
    [Required(ErrorMessage = "账户类型不能为空")]
    public string BankAcctType { get; set; } = default!;

    /// <summary>
    /// 开户行帐号
    /// </summary>
    [JsonPropertyName("bank_acct_no")]
    [Required(ErrorMessage = "开户行帐号不能为空")]
    [MaxLength(40, ErrorMessage = "开户行帐号不能超过40个字符")]
    public string BankAcctNo { get; set; } = default!;

    /// <summary>
    /// 开户帐号名称 对公账户填写公司名称，需与营业执照名称保持一致  个人账户填写法人姓名
    /// </summary>
    [JsonPropertyName("bank_acct_name")]
    [Required(ErrorMessage = "开户帐号名称不能为空")]
    [MaxLength(127, ErrorMessage = "开户帐号名称不能超过127个字符")]
    public string BankAcctName { get; set; } = default!;

    /// <summary>
    /// 营业省份id
    /// </summary>
    [JsonPropertyName("shop_province_id")]
    [Required(ErrorMessage = "营业省份id不能为空")]
    [MaxLength(2, ErrorMessage = "营业省份id不能超过2个字符")]
    public string ShopProvinceId { get; set; } = default!;

    /// <summary>
    /// 营业市id
    /// </summary>
    [JsonPropertyName("shop_city_id")]
    [Required(ErrorMessage = "营业市id不能为空")]
    [MaxLength(6, ErrorMessage = "营业市id不能超过6个字符")]
    public string ShopCityId { get; set; } = default!;

    /// <summary>
    /// 营业区id
    /// </summary>
    [JsonPropertyName("shop_country_id")]
    [Required(ErrorMessage = "营业区id不能为空")]
    [MaxLength(6, ErrorMessage = "营业区id不能超过6个字符")]
    public string ShopCountryId { get; set; } = default!;

    /// <summary>
    /// 营业地址补充信息
    /// </summary>
    [JsonPropertyName("shop_addr_ext")]
    [MaxLength(80, ErrorMessage = "营业地址补充信息不能超过80个字符")]
    public string? ShopAddrExt { get; set; }

    /// <summary>
    /// 社会信用统一代码/营业执照号。 00：企业商户（必填）01：个人工商户（必填）02：小微商户（选填）03：机关事业单位或社会团体（必填）
    /// </summary>
    [JsonPropertyName("shop_lic")]
    [MaxLength(50, ErrorMessage = "社会信用统一代码/营业执照号不能超过50个字符")]
    public string? ShopLic { get; set; }

    /// <summary>
    /// 行业类别编码 调用行业类别查询接口返回
    /// </summary>
    [JsonPropertyName("mccCode")]
    [Required(ErrorMessage = "行业类别编码不能为空")]
    public string MccCode { get; set; } = default!;

    /// <summary>
    /// 申请开通业务
    /// </summary>
    [JsonPropertyName("product")]
    [SwaggerIgnore]
    public List<ProductInfo> Product { get; set; } = [new ProductInfo()];

    // /// <summary>
    // /// 是否开通收支双线
    // /// </summary>
    // [JsonPropertyName("receipt2Line")]
    // public string Receipt2Line { get; set; }

    /// <summary>
    /// 控股股东姓名 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [JsonPropertyName("shareholderName")]
    [MaxLength(20, ErrorMessage = "控股股东姓名不能超过20个字符")]
    public string? ShareholderName { get; set; }

    /// <summary>
    /// 控股股东证件号 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [JsonPropertyName("shareholderCertno")]
    [MaxLength(20, ErrorMessage = "控股股东证件号不能超过20个字符")]
    public string? ShareholderCertno { get; set; }

    /// <summary>
    /// 控股股东证件开始日期 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [JsonPropertyName("shareholderCertBeginDate")]
    public DateOnly? ShareholderCertBeginDate { get; set; }

    /// <summary>
    /// 控股股东证件有效期 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [JsonPropertyName("shareholderCertExpire")]
    public DateOnly? ShareholderCertExpire { get; set; }

    /// <summary>
    /// 控股股东证件证件类型 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [JsonPropertyName("shareholderCertType")]
    public string ShareholderCertType { get; set; } = "1";

    /// <summary>
    /// 控股股东家庭地址 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [JsonPropertyName("shareholderHomeAddr")]
    public string? ShareholderHomeAddr { get; set; }

    /// <summary>
    /// 商户传真
    /// </summary>
    [JsonPropertyName("fax")]
    [MaxLength(20, ErrorMessage = "商户传真不能超过20个字符")]
    public string? Fax { get; set; }

    /// <summary>
    /// 终端维护经理
    /// </summary>
    [JsonPropertyName("lastTerminalManager")]
    public string? LastTerminalManager { get; set; }

    /// <summary>
    /// 客户维护经理
    /// </summary>
    [JsonPropertyName("lastClientManager")]
    public string? LastClientManager { get; set; }

    /// <summary>
    /// 所属服务区域
    /// </summary>
    [JsonPropertyName("serviceDistrict")]
    public string? ServiceDistrict { get; set; }

    /// <summary>
    /// 细分服务区域
    /// </summary>
    [JsonPropertyName("detailDistrict")]
    public string? DetailDistrict { get; set; }

    /// <summary>
    /// 发展部门
    /// </summary>
    [JsonPropertyName("developingDept")]
    public string? DevelopingDept { get; set; }

    /// <summary>
    /// 发展人
    /// </summary>
    [JsonPropertyName("developingPersonID")]
    public string? DevelopingPersonID { get; set; }

    /// <summary>
    /// 受益人列表
    /// </summary>
    [JsonPropertyName("bnfList")]
    public List<BeneficiaryInfo>? BnfList { get; set; }

    /// <summary>
    /// 法人家庭地址
    /// </summary>
    [JsonPropertyName("legalmanHomeAddr")]
    [MaxLength(60, ErrorMessage = "法人家庭地址不能超过60个字符")]
    public string? LegalmanHomeAddr { get; set; }

    /// <summary>
    /// 上传图片列表
    /// </summary>
    [JsonPropertyName("pic_list")]
    [Required(ErrorMessage = "上传图片列表不能为空")]
    public List<DocumentInfo> PicList { get; set; } = default!;

    /// <summary>
    /// 备注
    /// </summary>
    [JsonPropertyName("remark")]
    [MaxLength(200, ErrorMessage = "备注不能超过200个字符")]
    public string? Remark { get; set; }

    /// <summary>
    /// 二维码id列表
    /// </summary>
    [JsonPropertyName("ums_qrcode_list")]
    public string? UmsQrcodeList { get; set; }

    /// <summary>
    /// 商户类型
    /// </summary>
    [JsonPropertyName("mchntType")]
    public string? MchntType { get; set; }

    /// <summary>
    /// 商户对外名称
    /// </summary>
    [JsonPropertyName("externalName")]
    public string? ExternalName { get; set; }

    /// <summary>
    /// 平台全称
    /// </summary>
    [JsonPropertyName("accesserDesc")]
    public string? AccesserDesc { get; set; }

    /// <summary>
    /// 是否连锁商户
    /// </summary>
    [JsonPropertyName("isChain")]
    //[Required(ErrorMessage = "是否连锁商户不能为空")]
    public string IsChain { get; set; } = "1";

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [JsonPropertyName("contactName")]
    [MaxLength(16, ErrorMessage = "联系人姓名不能超过16个字符")]
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    [JsonPropertyName("contactMobile")]
    [MaxLength(11, ErrorMessage = "联系人手机号不能超过11个字符")]
    public string? ContactMobile { get; set; }

    /// <summary>
    /// 联系人证件类型
    /// </summary>
    [JsonPropertyName("contactCardType")]
    public string? ContactCardType { get; set; }

    /// <summary>
    /// 联系人证件号
    /// </summary>
    [JsonPropertyName("contactCardNo")]
    [MaxLength(36, ErrorMessage = "联系人证件号不能超过36个字符")]
    public string? ContactCardNo { get; set; }

    /// <summary>
    /// 平台名称
    /// </summary>
    [JsonPropertyName("accesserName")]
    public string? AccesserName { get; set; }

    /// <summary>
    /// 分账主商户ID
    /// </summary>
    [JsonPropertyName("mchntArr")]
    public List<string>? MchntArr { get; set; }
}

public class ProductInfo
{
    /// <summary>
    /// 开通业务id
    /// </summary>
    [JsonPropertyName("product_id")]
    [Required(ErrorMessage = "开通业务id不能为空")]
    [MaxLength(20, ErrorMessage = "开通业务id不能超过20个字符")]
    public string ProductId { get; set; } = "1";
}

public class BeneficiaryInfo
{
    /// <summary>
    /// 受益人姓名
    /// </summary>
    [JsonPropertyName("bnfName")]
    public string? BnfName { get; set; }

    /// <summary>
    /// 受益人证件号
    /// </summary>
    [JsonPropertyName("bnfCertno")]
    public string? BnfCertno { get; set; }

    /// <summary>
    /// 受益人证件开始日期 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [JsonPropertyName("bnfCertBeginDate")]
    public DateOnly? BnfCertBeginDate { get; set; }

    /// <summary>
    /// 受益人证件有效期 商户类型为小微或机关事业单位时不必传，其他商户类型必传
    /// </summary>
    [JsonPropertyName("bnfCertExpire")]
    public DateOnly? BnfCertExpire { get; set; }

    /// <summary>
    /// 受益人证件类型 不填默认为身份证
    /// </summary>
    [JsonPropertyName("bnfCertType")]
    public string BnfCertType { get; set; } = "1";

    /// <summary>
    /// 受益人家庭地址 商户类型为小微或机关事业单位时不必传，其他商户类型必填
    /// </summary>
    [JsonPropertyName("bnfHomeAddr")]
    public string? BnfHomeAddr { get; set; }

    /// <summary>
    /// 受益人电话 若上传受益人信息，则必填
    /// </summary>
    [JsonPropertyName("bnfMobile")]
    public string? BnfMobile { get; set; }

    /// <summary>
    /// 受益人国籍 0中国 若上传受益人信息，且证件类型为非身份证，则必填
    /// </summary>
    [JsonPropertyName("bnfNationality")]
    public string? BnfNationality { get; set; }

    /// <summary>
    /// 受益人性别1-男性2-女性 若上传受益人信息，且证件类型为非身份证，则必填
    /// </summary>
    [JsonPropertyName("bnfSex")]
    public string? BnfSex { get; set; }

    /// <summary>
    /// 受益人出生日期 若上传受益人信息，且证件类型为非身份证，则必填
    /// </summary>
    [JsonPropertyName("bnfBirthday")]
    public DateOnly? BnfBirthday { get; set; }

    /// <summary>
    /// 受益人是否为特定自然人
    /// </summary>
    [JsonPropertyName("bnfNatrlPrsn")]
    public string? BnfNatrlPrsn { get; set; }

    /// <summary>
    /// 受益人图片列表
    /// </summary>
    [JsonPropertyName("bnfPicList")]
    public List<DocumentInfo>? BnfPicList { get; set; }
}

public class DocumentInfo
{
    /// <summary>
    /// 上传图片类型
    /// 0001 法人身份证 | 0011 身份证反面 | 0002 商户营业执照(个人/企业必传) | 0003 商户税务登记证 | 0004 组织机构代码证 | 0005 门头照片 | 0006 对公结算材料 | 0007 自拍照 | 0008/0013/0014 辅助证明材料(选传) | 0022 室内照片 | 0099 其他材料 | 0012 授权证书 | 0016-0020 必传其一(租赁协议/产权证明/执业资质证照/第三方证明/小微商户证明) | 0021 经营商品 | 0025 银行卡正面照 | 0026 银行卡背面照 | 0032 民办非登记证书 | 0033 双方授权结算账户资金管理证明(对公非同名必填) | 0034 网站或APP截图(最多8张) | 0040 客户经理现场考察照 / 0041 受益人证明材料 / 0042 受益人证件正面照 / 0043 受益人证件反面照
    /// </summary>
    [JsonPropertyName("document_type")]
    [Required(ErrorMessage = "上传图片类型不能为空")]
    public string DocumentType { get; set; } = default!;

    /// <summary>
    /// 图片名称
    /// </summary>
    [JsonPropertyName("document_name")]
    [Required(ErrorMessage = "图片名称不能为空")]
    public string DocumentName { get; set; } = default!;

    /// <summary>
    /// 图片路径
    /// </summary>
    [JsonPropertyName("file_path")]
    [Required(ErrorMessage = "图片路径不能为空")]
    public string FilePath { get; set; } = default!;

    /// <summary>
    /// 图片大小
    /// </summary>
    [JsonPropertyName("file_size")]
    [Required(ErrorMessage = "图片大小不能为空")]
    public string FileSize { get; set; } = default!;
}

public class ComplexUploadResp
{
    [JsonPropertyName("res_code")] public string? ResCode { get; set; }

    [JsonPropertyName("res_msg")] public string? ResMsg { get; set; }

    /// <summary>
    /// 请求流水号
    /// </summary>
    [JsonPropertyName("request_seq")]
    public string? RequestSeq { get; set; }

    /// <summary>
    /// 自助签约平台整个申请过程中的唯一标识
    /// </summary>
    [JsonPropertyName("ums_reg_id")]
    public string? umsRegId { get; set; }
}

/// <summary>
/// 请求数据基类
/// </summary>
public class AutoRegReqBase
{
    /// <summary>
    /// 接入平台id  外部系统平台标识，由ums分配
    /// </summary>
    [JsonPropertyName("accesser_id")]
    public string? Base_AccesserId { get; private set; }

    public void SetInit(string AccesserId)
    {
        this.Base_AccesserId = AccesserId ?? string.Empty;
    }

    /// <summary>
    /// 签名方式 SHA-256，必须大写。
    /// </summary>
    [JsonPropertyName("sign_type")]
    public string Base_SignType { get; } = "SHA-256";

    /// <summary>
    /// 请求时间
    /// </summary>
    [JsonPropertyName("request_date")]
    public string Base_RequestDate { get; } = DateTime.Now.ToString("yyyyMMddHHmmss");

    /// <summary>
    /// 请求流水号
    /// </summary>
    [JsonPropertyName("request_seq")]
    public string Base_RequestSeq { get; } = Guid.NewGuid().ToString("N");
}

/// <summary>
/// 3.13  省市区行业数据下载接口
/// </summary>
public class DataDownloadReq : AutoRegReqBase
{
    /// <summary>
    /// 服务名称
    /// </summary>
    [JsonPropertyName("service")]
    public string Service { get; } = "data_download";

    /// <summary>
    /// 文件类型 province,city,county,mcc可选，分别对应省、市、区和行业数据。
    /// </summary>
    [JsonPropertyName("data_type")]
    [Required(ErrorMessage = "数据类型不能为空")]
    public string? DataType { get; set; }
}

/// <summary>
/// 3.5  所属支行查询接口
/// </summary>
public class BranchBankListReq : AutoRegReqBase
{
    /// <summary>
    /// 服务名称
    /// </summary>
    [JsonPropertyName("service")]
    public string Service { get; } = "branch_bank_list";

    /// <summary>
    /// 可以传2位的省，4位的市或者6位的区
    /// </summary>
    [JsonPropertyName("areaCode")]
    public string? AreaCode { get; set; }

    /// <summary>
    /// 请求的关键字，支持多个关键字查询，用"/"分割，需要符合前后顺序。建议使用支行关键字查询，不要直接使用银行全称或银行名称查询。
    /// </summary>
    [JsonPropertyName("key")]
    public string? Key { get; set; }
}

/// <summary>
/// 3.6  对公账户认证接口
/// </summary>
public class CompanyAccountVerifyReq : AutoRegReqBase
{
    /// <summary>
    /// 服务名称
    /// </summary>
    [JsonPropertyName("service")]
    public string Service { get; } = "company_account_verify";

    /// <summary>
    /// 自助签约平台流水号
    /// *档案资料上传接口返回
    /// </summary>
    [JsonPropertyName("ums_reg_id")]
    public string? UmsRegId { get; set; }

    /// <summary>
    /// 自助签约对公账号
    /// *档案资料上传接口中提供的对公账号
    /// </summary>
    [JsonPropertyName("company_account")]
    public string? CompanyAccount { get; set; }

    /// <summary>
    /// 交易金额
    /// *对公账户收到的验证交易金额，以分为单位
    /// </summary>
    [JsonPropertyName("trans_amt")]
    public string? trans_amt { get; set; }
}

/// <summary>
/// 3.7  发起对公账户验证交易接口
/// </summary>
public class RequestAccountVerifyReq : AutoRegReqBase
{
    /// <summary>
    /// 服务名称
    /// </summary>
    [JsonPropertyName("service")]
    public string Service { get; } = "request_account_verify";

    /// <summary>
    /// 自助签约平台流水号
    /// *档案资料上传接口返回
    /// </summary>
    [JsonPropertyName("ums_reg_id")]
    public string? UmsRegId { get; set; }

    /// <summary>
    /// 自助签约对公账号
    /// *档案资料上传接口中提供的对公账号
    /// </summary>
    [JsonPropertyName("company_account")]
    public string? CompanyAccount { get; set; }
}

/// <summary>
/// 3.35  对公账户交易查询接口
/// </summary>
public class NmrsQryEntpAccountDetailReq : AutoRegReqBase
{
    /// <summary>
    /// 服务名称
    /// </summary>
    [JsonPropertyName("service")]
    public string Service { get; } = "nmrs_qry_entp_account_detail";

    /// <summary>
    /// 协议号
    /// </summary>
    [JsonPropertyName("protocolNo")]
    public string? ProtocolNo { get; set; }
}

/// <summary>
/// 3.36  DM审核失败图片上传接口
/// </summary>
public class NmrsDmPicReUploadReq : AutoRegReqBase
{
    /// <summary>
    /// 服务名称
    /// </summary>
    [JsonPropertyName("service")]
    public string Service { get; } = "nmrs_dm_pic_re_upload";

    /// <summary>
    /// 协议号
    /// </summary>
    [JsonPropertyName("protocolNo")]
    public string? ProtocolNo { get; set; }

    /// <summary>
    /// 图片列表
    /// </summary>
    [JsonPropertyName("pic_list")]
    public List<AutoRegPicList>? PicList { get; set; }
}

/// <summary>
/// 3.37  控股股东/受益人姓名查询接口
/// </summary>
public class BnfNameListQryReq : AutoRegReqBase
{
    /// <summary>
    /// 服务名称
    /// </summary>
    [JsonPropertyName("service")]
    public string Service { get; } = "bnf_name_list_qry";

    /// <summary>
    /// 社会统一信用代码
    /// *同一个号码最多查询2次
    /// </summary>
    [JsonPropertyName("unionCreditNo")]
    [Required(ErrorMessage = "社会统一信用代码不能为空")]
    public string? ProtocolNo { get; set; }
}

public class AutoRegPicList
{
    /// <summary>
    /// 图片名称
    /// </summary>
    [JsonPropertyName("document_name")]
    public string? DocumentName { get; set; }

    /// <summary>
    /// 上传图片类型
    /// </summary>
    [JsonPropertyName("document_type")]
    public string? DocumentType { get; set; }

    /// <summary>
    /// 图片路径
    /// </summary>
    [JsonPropertyName("file_path")]
    public string? FilePath { get; set; }

    /// <summary>
    /// 图片大小
    /// </summary>
    [JsonPropertyName("file_size")]
    public string? FileSize { get; set; }
}

public class UserComplexUploadStatusResp
{
    [JsonPropertyName("ums_reg_id")]
    public string? UmsRegId { get; set; }
    /// <summary>
    /// 申请状态，可能的值：
    /// 00：签约中
    /// 01：签约成功（中间状态）
    /// 02：入网审核中（人工审核流程）
    /// 03：入网成功（最终成功状态）
    /// 04：入网失败
    /// 05：对公账户待验证或异常（对公账户状态）
    /// 06：风控审核中（系统审核状态）
    /// 11：短信签生成合同成功（短信签约流程）
    /// 18：资料填写中（前端流程状态）
    /// 28：资料验证失败
    /// 31：冻结账户
    /// 32：客服视频核验中（可再次调用签约接口）
    /// 33：客服视频核验失败（可再次调用签约接口）
    /// 34：待客户经理补充材料（等待客户经理处理工单）
    /// 35：DM已冻结
    /// 99：其它错误
    /// </summary>
    [JsonPropertyName("apply_status")]
    public string? ApplyStatus { get; set; }
    /// <summary>
    /// 申请状态对应的描述信息
    /// </summary>
    [JsonPropertyName("apply_status_msg")]
    public string? ApplyStatusMsg { get; set; }
    /// <summary>
    /// 开户行账号
    /// </summary>
    [JsonPropertyName("bank_acct_no")]
    public string? BankAcctNo { get; set; }
    
    /// <summary>
    /// 注册类型：
    /// 00-企业商户,01-个体工商户,
    /// 02-小微商户,03-机关事业单位或社会团体,
    /// 05-民办非企业
    /// </summary>
    [JsonPropertyName("reg_mer_type")]
    public string? RegMerType { get; set; } 
    
    /// <summary>
    /// 账户类型：
    /// 0-个人账户,1-公司账户
    /// </summary>
    [JsonPropertyName("bank_acct_type")]
    public string? BankAcctType { get; set; }
}