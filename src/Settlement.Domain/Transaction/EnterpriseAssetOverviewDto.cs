using System.ComponentModel.DataAnnotations;

namespace Settlement.Domain.Transaction;

/// <summary>
/// 企业资产概览请求
/// </summary>
public class EnterpriseAssetOverviewReq
{
    /// <summary>
    /// 商户ID
    /// </summary>
    [Required(ErrorMessage = "商户ID不能为空")]
    public string MerchantId { get; set; } = string.Empty;
}

/// <summary>
/// 企业资产概览响应
/// </summary>
public class EnterpriseAssetOverviewResp
{
    /// <summary>
    /// 可提现金额（单位：元）
    /// </summary>
    public decimal WithdrawableAmount { get; set; }

    /// <summary>
    /// 已提现金额（单位：元）
    /// </summary>
    public decimal WithdrawnAmount { get; set; }

    /// <summary>
    /// 已发放绩效工资（单位：元）
    /// </summary>
    public decimal PaidPerformanceSalary { get; set; }

    /// <summary>
    /// 项目退款金额（单位：元）
    /// </summary>
    public decimal ProjectRefundAmount { get; set; }

    /// <summary>
    /// 内部交付金额（单位：元）
    /// </summary>
    public decimal InternalDeliveryAmount { get; set; }

    /// <summary>
    /// 外部交付金额（单位：元）
    /// </summary>
    public decimal ExternalDeliveryAmount { get; set; }

    /// <summary>
    /// 商户ID
    /// </summary>
    public string MerchantId { get; set; } = string.Empty;

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 商户提现信息请求
/// </summary>
public class MerchantWithdrawInfoReq
{
    /// <summary>
    /// 商户ID
    /// </summary>
    [Required(ErrorMessage = "商户ID不能为空")]
    public string MerchantId { get; set; } = string.Empty;
}

/// <summary>
/// 商户提现信息响应
/// </summary>
public class MerchantWithdrawInfoResp
{
    /// <summary>
    /// 商户ID
    /// </summary>
    public string MerchantId { get; set; } = string.Empty;

    /// <summary>
    /// 可提现金额（单位：元）
    /// </summary>
    public decimal WithdrawableAmount { get; set; }

    /// <summary>
    /// 银行卡号
    /// </summary>
    public string BankAcctNo { get; set; } = string.Empty;

    /// <summary>
    /// 收款人（商户营业名称）
    /// </summary>
    public string PayeeName { get; set; } = string.Empty;
    
    /// <summary>
    /// 账户号
    /// </summary>
    public string? AcctNo { get; set; }
}