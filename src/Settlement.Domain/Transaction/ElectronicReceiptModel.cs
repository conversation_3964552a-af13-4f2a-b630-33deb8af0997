using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Settlement.Domain.Transaction;

/// <summary>
/// 电子回单查询请求
/// </summary>
public class ElectronicReceiptReq
{
    /// <summary>
    /// 客户身份识别码
    /// </summary>
    [JsonPropertyName("acctNo")]
    [Required(ErrorMessage = "账户号不能为空")]
    public string AcctNo { get; set; } = default!;

    /// <summary>
    /// 应用流水号
    /// </summary>
    [JsonPropertyName("appSsn")]
    [Required(ErrorMessage = "应用流水号不能为空")]
    public string AppSsn { get; set; } = default!;

    /// <summary>
    /// 商户记账日期 yyyymmdd
    /// </summary>
    [JsonPropertyName("appAcctDate")]
    [Required(ErrorMessage = "商户记账日期不能为空")]
    public string AppAcctDate { get; set; } = default!;

    /// <summary>
    /// 交易类型
    /// </summary>
    [JsonPropertyName("transType")]
    [Required(ErrorMessage = "交易类型不能为空")]
    public string TransType { get; set; } = default!;
}

/// <summary>
/// 电子回单查询响应
/// </summary>
public class ElectronicReceiptResp
{
    /// <summary>
    /// 银商商户号
    /// </summary>
    [JsonPropertyName("mchntNo")]
    public string? MchntNo { get; set; }

    /// <summary>
    /// 客户身份识别码
    /// </summary>
    [JsonPropertyName("acctNo")]
    public string? AcctNo { get; set; }

    /// <summary>
    /// 应用流水号
    /// </summary>
    [JsonPropertyName("appSsn")]
    public string? AppSsn { get; set; }

    /// <summary>
    /// 商户记账日期 yyyymmdd
    /// </summary>
    [JsonPropertyName("appAcctDate")]
    public string? AppAcctDate { get; set; }

    /// <summary>
    /// 交易日期 yyyymmdd
    /// </summary>
    [JsonPropertyName("transDate")]
    public string? TransDate { get; set; }

    /// <summary>
    /// 交易时间 HHmmss
    /// </summary>
    [JsonPropertyName("transTime")]
    public string? TransTime { get; set; }

    /// <summary>
    /// 电子回单编号
    /// </summary>
    [JsonPropertyName("billNo")]
    public string? BillNo { get; set; }

    /// <summary>
    /// 电子回单 BASE64码
    /// </summary>
    [JsonPropertyName("bill")]
    public string? Bill { get; set; }

    /// <summary>
    /// 错误码
    /// </summary>
    [JsonPropertyName("errCode")]
    public string ErrorCode { get; set; } = default!;

    /// <summary>
    /// 错误码描述
    /// </summary>
    [JsonPropertyName("errInfo")]
    public string? ErrorInfo { get; set; }
}