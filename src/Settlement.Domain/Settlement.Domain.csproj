﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\net8.0\Settlement.Domain.xml</DocumentationFile>
    <NoWarn>1701;1702;1591;NU1803;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Public\Entity\Entity.csproj" />
    <ProjectReference Include="..\Public\Infrastructure\Infrastructure.csproj" />
  </ItemGroup>

</Project>
