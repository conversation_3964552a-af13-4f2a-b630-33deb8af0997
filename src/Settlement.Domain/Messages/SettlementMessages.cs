using System.Text.Json.Serialization;

namespace Settlement.Domain.Messages;

/// <summary>
/// 结算消息基类
/// </summary>
public abstract class SettlementMessageBase
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 业务ID
    /// </summary>
    public string BusinessId { get; set; } = default!;
}

/// <summary>
/// 服务奖金发起消息
/// </summary>
public class ServiceBonusInitiatedMessage : SettlementMessageBase
{
    /// <summary>
    /// 合同编码
    /// </summary>
    public string ContractCode { get; set; } = default!;

    /// <summary>
    /// 经办人
    /// </summary>
    public string AgentHrNo { get; set; } = default!;

    /// <summary>
    /// 订单编号
    /// </summary>
    public string OrderNumber { get; set; } = default!;

    /// <summary>
    /// 发放人员列表
    /// </summary>
    public List<IssueItem> IssueList { get; set; } = new();
}

/// <summary>
/// 开票流程发起消息
/// </summary>
public class InvoiceInitiatedMessage : SettlementMessageBase
{
    /// <summary>
    /// 凭证记录id 
    /// </summary>
    public string VoucherId { get; set; } = default!;
}

/// <summary>
/// 银联商务进件消息
/// </summary>
public class ChinaumsRegistrationMessage : SettlementMessageBase
{
    /// <summary>
    /// 用户注册ID
    /// </summary>
    public string UmsRegId { get; set; } = default!;

    /// <summary>
    /// 注册状态
    /// </summary>
    public string Status { get; set; } = default!;

    /// <summary>
    /// 用户信息
    /// </summary>
    public string UserInfo { get; set; } = default!;
}

/// <summary>
/// 发放项目
/// </summary>
public class IssueItem
{
    /// <summary>
    /// 人员ID
    /// </summary>
    public string PersonId { get; set; } = default!;

    /// <summary>
    /// 发放金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 发放类型
    /// </summary>
    public string Type { get; set; } = default!;
}

/// <summary>
/// 银联商务充值动账提醒
/// </summary>
public class ChinaumsCallbackRechargeMessage : SettlementMessageBase
{
    /// <summary>
    /// 外部流水号
    /// </summary>
    public string AppSsn { get; set; }

    /// <summary>
    /// 应用记账日期
    /// *yyyymmdd
    /// </summary>
    public string AppAcctDate { get; set; }

    /// <summary>
    /// 请求日期
    /// *yyyymmdd
    /// </summary>
    public string ReqDate { get; set; }

    /// <summary>
    /// 请求时间
    /// *HHmmss
    /// </summary>
    public string ReqTime { get; set; }

    /// <summary>
    /// 交易流水号
    /// </summary>
    public string TransSsn { get; set; }

    /// <summary>
    /// 账户记账日期
    /// *yyyymmdd
    /// </summary>
    public string AcctDate { get; set; }

    /// <summary>
    /// 交易日期
    /// *yyyymmdd
    /// </summary>
    public string TransDate { get; set; }

    /// <summary>
    /// 交易时间
    /// *HHmmss
    /// </summary>
    public string TransTime { get; set; }

    /// <summary>
    /// 出金账户号
    /// </summary>
    public string OutAcctNo { get; set; }

    /// <summary>
    /// 入金账户号
    /// </summary>
    public string InAcctNo { get; set; }

    /// <summary>
    /// 交易金额
    /// *单位：分
    /// </summary>
    public string TransAmt { get; set; }

    /// <summary>
    /// 平台手续费
    /// *单位：分
    /// </summary>
    public string PltfmFee { get; set; }

    /// <summary>
    /// 机构手续费
    /// *单位：分
    /// </summary>
    public string InstFee { get; set; }

    /// <summary>
    /// 银商手续费
    /// *单位：分
    /// </summary>
    public string Fee { get; set; }

    /// <summary>
    /// 交易附言
    /// </summary>
    public string? TransNote { get; set; }

    /// <summary>
    /// 交易类型
    /// *201636 单位支付账户上账
    /// </summary>
    public string? TransType { get; set; }
}


/// <summary>
/// API类型常量
/// </summary>
public static class ApiTypes
{
    /// <summary>
    /// NovaPin API
    /// </summary>
    public const string NovaPin = "NovaPin";

    /// <summary>
    /// 银联商务API
    /// </summary>
    public const string Chinaums = "Chinaums";

    /// <summary>
    /// 银联商务天满API
    /// </summary>
    public const string OpenChinaums = "OpenChinaums";
    /// <summary>
    /// 通知API
    /// </summary>
    public const string Notification = "Notification";
}
