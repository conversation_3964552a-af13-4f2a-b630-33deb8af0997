using Config.Enums.Nkp;
using Settlement.Domain.Invoice;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Settlement.Infrastructure.Model;

public class OAuthTokenRequest
{
    [JsonPropertyName("grant_type")]
    public string GrantType { get; set; } = "client_credentials";

    [JsonPropertyName("client_id")]
    public string ClientId { get; set; } = "fenrun";

    [JsonPropertyName("client_secret")]
    public string ClientSecret { get; set; } = "fenrun";
}

public class OAuthTokenResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; }

    [JsonPropertyName("token_type")]
    public string TokenType { get; set; }

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonPropertyName("scope")]
    public string Scope { get; set; }
}

// 服务奖金发起流程模型
public class IssueItem
{
    /// <summary>
    /// 人员
    /// </summary>
    [JsonPropertyName("hrNo")]
    public string HrNo { get; set; }
    /// <summary>
    /// 金额
    /// </summary>

    [JsonPropertyName("issuedAmount")]
    public decimal IssuedAmount { get; set; }
    /// <summary>
    /// 说明
    /// </summary>

    [JsonPropertyName("remark")]
    public string Remark { get; set; }
}

public class InitiateServiceBonusRequest
{
    /// <summary>
    /// 合同编号 （例如：SO.202407120003）
    /// </summary>
    [JsonPropertyName("contractCode")]
    public string ContractCode { get; set; }
    /// <summary>
    /// 经办人是指这个流程的提交人、创建人（钉钉工号：例如 000588）
    /// </summary>

    [JsonPropertyName("agentHrNo")]
    public string AgentHrNo { get; set; }

    [JsonPropertyName("issueList")]
    public List<IssueItem> IssueList { get; set; }

    [JsonPropertyName("orderNumber")]
    public string OrderNumber { get; set; }
}

public class InitiateServiceBonusResponse
{
    [JsonPropertyName("code")]
    public int Code { get; set; }

    [JsonPropertyName("msg")]
    public string Msg { get; set; }

    [JsonPropertyName("data")]
    public object Data { get; set; }
}

// 开票流程发起模型 
public class InitiateInvoiceRequest
{
    public InitiateInvoiceRequest() { }
    public InitiateInvoiceRequest(InitiateInvoiceReq req, string OrderNumber, string AgentHrNo)
    {
        this.ContractCode = req.ContractCode;
        this.Amount = req.Amount;
        this.Unit = req.Unit;
        this.InvoiceType = req.InvoiceType;
        this.BuyerTaxnum = req.BuyerTaxnum;
        this.BuyerName = req.BuyerName;
        this.BuyerAddress = req.BuyerAddress;
        this.BuyerTel = req.BuyerTel;
        this.BuyerAccountName = req.BuyerAccountName;
        this.BuyerAccount = req.BuyerAccount;
        this.InvoiceName = req.InvoiceName;
        this.GoodsCode = req.GoodsCode;
        this.VatRate = req.VatRate;
        this.WithTaxFlag = req.WithTaxFlag;
        this.FavouredpolicyFlag = req.FavouredpolicyFlag;
        this.SpecType = req.SpecType;
        this.PushMode = req.PushMode;
        this.BuyerPhone = req.BuyerPhone;
        this.Email = req.Email;
        this.ShowBankAccountType = req.ShowBankAccountType;
        this.ShowAddressTelType = req.ShowAddressTelType;
        this.ShowCheckerType = req.ShowCheckerType;
        this.Remark = req.Remark;

        this.OrderNumber = OrderNumber;
        this.AgentHrNo = AgentHrNo;
    }
    /// <summary>
    /// 订单编号
    /// *诺快聘这边自定义的流水号
    /// </summary>
    [SwaggerIgnore]
    [JsonPropertyName("orderNumber")]
    public string OrderNumber { get; set; } = default!;

    /// <summary>
    /// 合同编码
    /// </summary>
    [Required(ErrorMessage = "合同编码不能为空")]
    [JsonPropertyName("contractCode")]
    public string ContractCode { get; set; } = default!;

    /// <summary>
    /// 经办人
    /// </summary>
    [SwaggerIgnore]
    [JsonPropertyName("agentHrNo")]
    public string AgentHrNo { get; set; } = default!;

    /// <summary>
    /// 开票金额
    /// </summary>
    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }
    /// <summary>
    /// 单位
    /// </summary>
    [JsonPropertyName("unit")]
    public string Unit { get; set; } = default!;

    /// <summary>
    /// 开票类型
    /// </summary>
    [JsonPropertyName("invoiceType")]
    public NovaPinInvoiceType InvoiceType { get; set; }

    /// <summary>
    /// 购方税号
    /// </summary>
    [JsonPropertyName("buyertaxnum")]
    public string? BuyerTaxnum { get; set; }

    /// <summary>
    /// 购方名称
    /// </summary>
    [JsonPropertyName("buyername")]
    public string? BuyerName { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [JsonPropertyName("buyeraddress")]
    public string? BuyerAddress { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    [JsonPropertyName("buyertel")]
    public string? BuyerTel { get; set; }

    /// <summary>
    /// 开户行
    /// </summary>
    [JsonPropertyName("buyeraccountname")]
    public string? BuyerAccountName { get; set; }

    /// <summary>
    /// 开户行账号
    /// </summary>
    [JsonPropertyName("buyeraccount")]
    public string? BuyerAccount { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    [JsonPropertyName("invoicename")]
    public string? InvoiceName { get; set; }

    /// <summary>
    /// 商品税收分类编码
    /// </summary>
    [JsonPropertyName("goodscode")]
    public string? GoodsCode { get; set; }

    /// <summary>
    /// 税率
    /// </summary>
    [JsonPropertyName("vatrate")]
    public decimal? VatRate { get; set; }

    /// <summary>
    /// 含税标志
    /// </summary>
    [JsonPropertyName("withtaxflag")]
    public string? WithTaxFlag { get; set; }

    /// <summary>
    /// 含税标志
    /// </summary>
    [JsonPropertyName("favouredpolicyflag")]
    public string? FavouredpolicyFlag { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    [JsonPropertyName("spectype")]
    public string? SpecType { get; set; }

    /// <summary>
    /// 推送方式
    /// </summary>
    [JsonPropertyName("pushmode")]
    public NovaPinInvoicePushMode PushMode { get; set; }

    /// <summary>
    /// 推送手机
    /// </summary>
    [JsonPropertyName("buyerphone")]
    public string? BuyerPhone { get; set; }

    /// <summary>
    /// 推送邮箱
    /// </summary>
    [JsonPropertyName("email")]
    public string? Email { get; set; }

    /// <summary>
    /// 是否显示开户行及账号
    /// </summary>
    [JsonPropertyName("showbankaccounttype")]
    public NovaPinInvoiceShowBankAccountType ShowBankAccountType { get; set; }
    /// <summary>
    /// 是否显示地址及电话
    /// </summary>
    [JsonPropertyName("showaddressteltype")]
    public NovaPinInvoiceShowAddressTelType ShowAddressTelType { get; set; }
    /// <summary>
    /// 是否展示收款和复核人
    /// </summary>
    [JsonPropertyName("showcheckertype")]
    public NovaPinInvoiceShowCheckerType ShowCheckerType { get; set; }

    /// <summary>
    /// 发票备注
    /// </summary>
    [JsonPropertyName("remark")]
    public string? Remark { get; set; }
}

public class InitiateInvoiceResponse
{
    [JsonPropertyName("code")]
    public int Code { get; set; }

    [JsonPropertyName("msg")]
    public string Msg { get; set; }

    [JsonPropertyName("data")]
    public string Data { get; set; }

    public bool IsSuccess()
    {
        return this.Code == 200;
    }
}