﻿using Infrastructure.Exceptions;
using Org.BouncyCastle.Ocsp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Settlement.Infrastructure.Model
{
    /// <summary>
    /// 参数验证
    /// </summary>
    public class ParamsValidation
    {
        /// <summary>
        /// 参数验证, 如果参数验证失败则抛出异常
        /// </summary>
        /// <param name="req"></param>
        /// <exception cref="BadRequestException"></exception>
        public static void Validate<T>(T req) where T : class
        {
            //验证参数
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(req);
            bool isValid = Validator.TryValidateObject(req, validationContext, validationResults, true);
            if (!isValid)
                throw new BadRequestException(validationResults.FirstOrDefault()?.ErrorMessage);
        }

        /// <summary>
        /// 参数验证, 如果参数验证失败则返回失败列表
        /// </summary>
        /// <param name="req"></param>
        /// <param name="validationResults"></param>
        /// <returns></returns>
        public static bool TryValidate<T>(T req, out List<ValidationResult>? validationResults) where T : class
        {
            //验证参数
            validationResults = [];
            var validationContext = new ValidationContext(req);
            bool isValid = Validator.TryValidateObject(req, validationContext, validationResults, true);
            if (!isValid)
                return false;

            return true;
        }
    }
}
