using System.Security.Cryptography;
using System.Text;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.Crypto.Paddings;
using Org.BouncyCastle.Crypto.Parameters;

namespace Settlement.Infrastructure.Common;

public static class Sm4CryptoUtils
{
    // SM4-ECB 加密
    public static byte[] Encrypt(byte[] key, byte[] plaintext)
    {
        // 校验密钥长度（SM4 密钥必须为 16 字节）
        if (key.Length != 16)
            throw new ArgumentException("SM4 key must be 16 bytes (128 bits).");

        // 初始化 SM4 引擎（ECB 模式）
        var engine = new SM4Engine();
        var cipher = new PaddedBufferedBlockCipher(engine, new Pkcs7Padding());

        // 设置密钥（ECB 模式无 IV）
        var keyParam = new KeyParameter(key);
        cipher.Init(true, keyParam); // true 表示加密

        // 处理加密
        var output = new byte[cipher.GetOutputSize(plaintext.Length)];
        var len = cipher.ProcessBytes(plaintext, 0, plaintext.Length, output, 0);
        cipher.DoFinal(output, len); // 完成加密

        return output;
    }

    // SM4-ECB 解密
    public static byte[] Decrypt(byte[] key, byte[] ciphertext)
    {
        if (key.Length != 16)
            throw new ArgumentException("SM4 key must be 16 bytes (128 bits).");

        var engine = new SM4Engine();
        var cipher = new PaddedBufferedBlockCipher(engine, new Pkcs7Padding());

        var keyParam = new KeyParameter(key);
        cipher.Init(false, keyParam); // false 表示解密

        var output = new byte[cipher.GetOutputSize(ciphertext.Length)];
        var len = cipher.ProcessBytes(ciphertext, 0, ciphertext.Length, output, 0);
        cipher.DoFinal(output, len);

        return output;
    }

    // ：加密字符串（UTF-8 编码）
    public static string EncryptString(string key, string plaintext)
    {
        var keyBytes = Encoding.UTF8.GetBytes(key);
        var plainBytes = Encoding.UTF8.GetBytes(plaintext);
        var cipherBytes = Encrypt(keyBytes, plainBytes);
        
        return ToHexString(cipherBytes);
    }
    
    /// <summary>
    /// 加密字符串（UTF-8 编码）
    /// </summary>
    /// <param name="key"></param>
    /// <param name="plaintext"></param>
    /// <returns></returns>
    public static string EncryptString(byte[] key, string plaintext)
    {
        var plainBytes = Encoding.UTF8.GetBytes(plaintext);
        var cipherBytes = Encrypt(key, plainBytes);
        return ToHexString(cipherBytes);
    }

    // ：解密字符串
    public static string DecryptString(string key, string ciphertext)
    {
        var keyBytes = Encoding.UTF8.GetBytes(key);
        var cipherBytes = Convert.FromBase64String(ciphertext);
        var plainBytes = Decrypt(keyBytes, cipherBytes);
        return Encoding.UTF8.GetString(plainBytes);
    }
    
    
    public static byte[] GenerateSM4Key()
    {
        byte[] key = new byte[16];
        using (RandomNumberGenerator rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(key);
        }
        return key;
    }

    // 将密钥转换为十六进制字符串表示（便于存储和传输）
    public static string ToHexString(byte[] bytes)
    {
        return BitConverter.ToString(bytes).Replace("-", "").ToLowerInvariant();
    }
    
}