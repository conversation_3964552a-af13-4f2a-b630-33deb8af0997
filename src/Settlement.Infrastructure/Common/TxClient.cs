// using System.Collections;
// using System.Text;
// using Newtonsoft.Json;
// using Newtonsoft.Json.Linq;
// using Org.BouncyCastle.Crypto.Parameters;
// using System.Net.Http.Headers;
// namespace Settlement.Infrastructure.Common;
//
// public class TxClient
// {
//     private readonly ILogger _logger;
//     private const string YYYY_MM_DD = "yyyyMMdd";
//     private const string HH_MM_SS = "HHmmss";
//     private readonly DemoConfig _config;
//     private readonly ECPrivateKeyParameters _privateKey;
//     private readonly ECPublicKeyParameters _tlPublicKey;
//
//     public TxClient(DemoConfig config)
//     {
//         _config = config;
//         _privateKey = Sm2Utils.PrivKeySm2FromBase64Str(config.PrivateKeyStr);
//         _tlPublicKey = Sm2Utils.PubKeySm2FromBase64Str(config.AllinpayPublicKeyStr);
//         _logger = new ConsoleLogger();
//     }
//
//     public TxResponse SendRequest(string transCode, BizParameter param)
//     {
//         return SendRequest(transCode, param, null);
//     }
//
//     private TxResponse? SendRequest(string transCode, BizParameter param, string url)
//     {
//         var request = AssembleRequest(transCode, param);
//         _logger.LogInformation($"Request: {JsonConvert.SerializeObject(request)}");
//         
//         var respStr = Post(request.ToString(), url ?? GetReqUrl()).GetAwaiter().GetResult();
//         _logger.LogInformation($"Response: {respStr}");
//         
//         Verify(respStr);
//         _logger.LogInformation("Signature verified successfully");
//         
//         return JsonConvert.DeserializeObject<TxResponse>(respStr);
//     }
//
//     private void Verify(string respStr)
//     {
//         var json = JObject.Parse(respStr);
//         var sign = json["sign"]!.ToString();
//         json.Remove("sign");
//         json.Remove("signType");
//         var srcSignMsg = Sm2Utils.JsonMapToStr(json);
//         if (!Sm2Utils.Verify(_tlPublicKey, srcSignMsg, sign))
//             throw new Exception("Response signature verification failed");
//     }
//
//     private TxRequest AssembleRequest(string transCode, BizParameter param)
//     {
//         var request = new TxRequest
//         {
//             AppId = _config.AppId,
//             SpAppId = _config.SpAppId,
//             TransCode = transCode,
//             Format = _config.Format,
//             Charset = _config.Charset,
//             TransDate = DateTime.Now.ToString(YYYY_MM_DD),
//             TransTime = DateTime.Now.ToString(HH_MM_SS),
//             Version = _config.Version,
//             BizData = param.ToString()
//         };
//
//         var signedValue = Sm2Utils.JsonMapToStr(JObject.FromObject(request));
//         _logger.LogInformation($"Data to sign: {signedValue}");
//         
//         request.Sign = Sm2Utils.Sign(_privateKey, signedValue);
//         request.SignType = _config.SignType;
//         
//         return request;
//     }
//
//     private async Task<string> Post(string param, string url)
//     {
//         using var handler = new HttpClientHandler 
//         { 
//             ServerCertificateCustomValidationCallback = (_, _, _, _) => true 
//         };
//         
//         using var client = new HttpClient(handler);
//         client.DefaultRequestHeaders.Accept.Add(
//             new MediaTypeWithQualityHeaderValue("application/json"));
//         
//         var content = new StringContent(param, Encoding.UTF8, "application/json");
//         var response = await client.PostAsync(url, content);
//         
//         return await response.Content.ReadAsStringAsync();
//     }
//
//     protected string GetReqUrl() => _config.Url;
//     public DemoConfig GetConfig() => _config;
// }
//
//
//
// // Supporting classes
// public class DemoConfig
// {
//     public string AppId { get; set; } = "21762000921804636162";
//     
//     private string SecretKey { get; set; } = "878427523d3525e070298d44481b8d2e";
//     public string SpAppId { get; set; }
//
//     public string PrivateKeyStr { get; set; } =
//         "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgiaZmB+feACtziE8SYjVZsaQwLNLRiyO8ebSupeoWIF2gCgYIKoEcz1UBgi2hRANCAATwEo0zq6KaB992PToWeJH52LmfS0sFovnB8/LMaoIAOTlFJtA3YgjWXKlO3KT+GqOCfCC4xE60isCr28tqy7hM";
//     public string AllinpayPublicKeyStr { get; set; } = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEu9LNkJlyLtjJxtQWIGlcZ/hyHt5eZ7LEH1nfOiK1H9HsE1cMPu5KK5jZVTtAyc7lPMXixUMirf6A3tMbuMbgqg==";
//
//     public string Format { get; set; } = "json";
//     public string Charset { get; set; } = "UTF-8";
//     public string Version { get; set; } = "1.0";
//     public string SignType { get; set; } = "SM3withSM2";
//     public string Url { get; set; } = "http://116.228.64.55:28082/yst-service-api/tx/handle";
// }
//
// public class TxRequest
// {
//     [JsonProperty("appId")]
//     public string AppId { get; set; }
//     [JsonProperty("spAppId")]
//     public string SpAppId { get; set; }
//     [JsonProperty("transCode")]
//     public string TransCode { get; set; }
//     [JsonProperty("format")]
//     public string Format { get; set; }
//     [JsonProperty("charset")]
//     public string Charset { get; set; }
//     [JsonProperty("transDate")]
//     public string TransDate { get; set; }
//     [JsonProperty("transTime")]
//     public string TransTime { get; set; }
//     [JsonProperty("version")]
//     public string Version { get; set; }
//     [JsonProperty("bizData")]
//     public string BizData { get; set; }
//     [JsonProperty("signType")]
//     public string SignType { get; set; }
//     [JsonProperty("sign")]
//     public string Sign { get; set; }
//
//     public override string ToString() => JsonConvert.SerializeObject(this);
// }
//
// public class TxResponse
// {
//     // Add properties based on your response structure
// }
//
// public class BizParameter
// {
//     private readonly JObject _jObject = new JObject();
//
//     public void AddParam(string paramName, string paramValue)
//     {
//         _jObject[paramName] = paramValue;
//     }
//
//     public void AddParam(string paramName, int paramValue)
//     {
//         _jObject[paramName] = paramValue;
//     }
//
//     public void AddMapParam(string paramName, Dictionary<string, object> paramMap)
//     {
//         _jObject[paramName] = JObject.FromObject(paramMap);
//     }
//
//     public void AddListParam(string paramName, IEnumerable paramValue)
//     {
//         _jObject[paramName] = JArray.FromObject(paramValue);
//     }
//
//     public override string ToString()
//     {
//         return _jObject.ToString(Formatting.None);
//     }
// }
//
// public interface ILogger
// {
//     void LogInformation(string message);
//     void LogError(string message, Exception ex);
// }
//
// public class ConsoleLogger : ILogger
// {
//     public void LogInformation(string message) => Console.WriteLine($"[INFO] {message}");
//     public void LogError(string message, Exception ex) => 
//         Console.WriteLine($"[ERROR] {message}: {ex}");
// }
//
// public class Program
// {
//     private static readonly ILogger _logger = new ConsoleLogger();
//
//     public static void Main(string[] args)
//     {
//         try
//         {
//             // 初始化配置（需根据实际情况填充参数）
//             var demoConfig = new DemoConfig();
//          
//             // 组装参数
//             var bizParameter = new BizParameter();
//             bizParameter.AddParam("respTraceNum", "20240228171459208501776449");
//             bizParameter.AddParam("verifyCode", "111111");
//
//             // 发送请求
//             var txClient = new TxClient(demoConfig);
//             var txResponse = txClient.SendRequest("3010", bizParameter);
//             
//             // 打印响应结果
//         }
//         catch (Exception ex)
//         {
//             _logger.LogError("Application error", ex);
//         }
//     }
// }
