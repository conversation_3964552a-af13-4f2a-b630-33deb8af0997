
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
namespace Settlement.Infrastructure.Common;

public static class CertificateUtil
{
    /// <summary>
    /// 根据PFX证书流和密码获取私钥
    /// </summary>
    /// <param name="pfxStream">PFX证书流</param>
    /// <param name="alias">证书别名（可选）</param>
    /// <param name="password">证书密码</param>
    /// <returns>私钥对象</returns>
    public static RSA? GetPrivateKey(Stream pfxStream, string alias, string password)
    {
        // .NET中不需要显式处理alias，直接从证书获取私钥
        var certificate = new System.Security.Cryptography.X509Certificates.X509Certificate2(
            GetCertBytes(pfxStream), 
            password,
            System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable
        );
        
        return certificate.GetRSAPrivateKey();
    }
    public static RSA? GetPrivateKey(byte[] certBytes, string alias, string? password)
    {
        // .NET中不需要显式处理alias，直接从证书获取私钥
        var certificate = new System.Security.Cryptography.X509Certificates.X509Certificate2(certBytes
            , 
            password,
            System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable
        );
        
        return certificate.GetRSAPrivateKey();
    }

    public static byte[] GetCertBytes(Stream stream)
    {
        using var ms = new MemoryStream();
        stream.CopyTo(ms);
        return ms.ToArray();
    }

    /// <summary>
    /// 采用SHA256withRSA对报文进行签名，并返回Hex编码后的签名字符串
    /// </summary>
    /// <param name="jsonStr">待签名的JSON字符串</param>
    /// <param name="privateKey">RSA私钥</param>
    /// <returns>Hex编码的签名字符串</returns>
    public static string Sign(string jsonStr, RSA privateKey)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(jsonStr);
            var signBytes = Sign(dataBytes, privateKey, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            return ByteArrayToHexString(signBytes);
        }
        catch (Exception ex)
        {
            throw new SecurityException("签名失败", ex);
        }
    }

    private static byte[] Sign(byte[] data, RSA privateKey, HashAlgorithmName hashAlgorithm, RSASignaturePadding padding)
    {
        return privateKey.SignData(data, hashAlgorithm, padding);
    }

    /// <summary>
    /// 根据公钥证书字符串生成公钥对象
    /// </summary>
    /// <param name="publicKeyCertString">PEM格式的公钥证书字符串</param>
    /// <returns>公钥对象</returns>
    public static RSA GeneratePublicKey(string publicKeyCertString)
    {
        // 证书方式加载（PEM格式）
        if (publicKeyCertString.Contains("-----BEGIN CERTIFICATE-----"))
        {
            var cert = new X509Certificate2(Encoding.UTF8.GetBytes(publicKeyCertString));
            return cert.GetRSAPublicKey()!;
        }
        
        // 移除PEM格式的头尾标记
        var publicKeyPem = publicKeyCertString
            .Replace("-----BEGIN PUBLIC KEY-----", "")
            .Replace("-----END PUBLIC KEY-----", "")
            .Replace("\n", "")
            .Replace("\r", "");
            
        var publicKeyBytes = Convert.FromBase64String(publicKeyPem);
        
        using var rsa = RSA.Create();
        rsa.ImportSubjectPublicKeyInfo(publicKeyBytes, out _);
        return rsa;
    }

    /// <summary>
    /// 验证RSA签名
    /// </summary>
    /// <param name="sign">Hex编码的签名字符串</param>
    /// <param name="publicKey">RSA公钥</param>
    /// <param name="data">原始数据字符串</param>
    /// <returns>签名是否有效</returns>
    public static bool VerifyRsa(string sign, RSA publicKey, string data)
    {
        try
        {
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signBytes = HexStringToByteArray(sign);
            return Verify(dataBytes, signBytes, publicKey, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        }
        catch (Exception)
        {
            return false;
        }
    }

    private static bool Verify(byte[] data, byte[] signature, RSA publicKey, HashAlgorithmName hashAlgorithm, RSASignaturePadding padding)
    {
        return publicKey.VerifyData(data, signature, hashAlgorithm, padding);
    }

    /// <summary>
    /// 字节数组转换为十六进制字符串
    /// </summary>
    public static string ByteArrayToHexString(byte[] bytes)
    {
        return BitConverter.ToString(bytes).Replace("-", "").ToLowerInvariant();
    }

    /// <summary>
    /// 十六进制字符串转换为字节数组
    /// </summary>
    public static byte[] HexStringToByteArray(string hex)
    {
        if (string.IsNullOrEmpty(hex) || hex.Length % 2 != 0)
            return new byte[0];
            
        var result = new byte[hex.Length / 2];
        for (int i = 0; i < result.Length; i++)
        {
            result[i] = Convert.ToByte(hex.Substring(i * 2, 2), 16);
        }
        return result;
    }
}

// 自定义异常类
public class SecurityException : Exception
{
    public SecurityException(string message) : base(message) { }
    public SecurityException(string message, Exception innerException) : base(message, innerException) { }
}