using Org.BouncyCastle.Crypto.Modes;
using Org.BouncyCastle.Crypto.Paddings;
using Org.BouncyCastle.Pkcs;
using Org.BouncyCastle.X509;

namespace Settlement.Infrastructure.Common;

using System;
using System.Text;
using Org.BouncyCastle.Asn1.X9;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Digests;
using Org.BouncyCastle.Crypto.Encodings;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.Math.EC;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Utilities.Encoders;
public class GmCryptoUtil
{
     // SM2曲线参数
    private static readonly X9ECParameters Sm2EcParams = ECNamedCurveTable.GetByName("sm2p256v1");
    private static readonly ECDomainParameters Sm2DomainParams = new ECDomainParameters(
        Sm2EcParams.Curve, Sm2EcParams.G, Sm2EcParams.N, Sm2EcParams.H);

    /// <summary>
    /// SM2公钥加密
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKey">Base64编码的公钥</param>
    /// <returns>Hex大写的密文</returns>
    public static string Sm2Encrypt(string plainText, string publicKey)
    {
        byte[] pubKeyBytes = Convert.FromBase64String(publicKey);
        ECPublicKeyParameters pubKey = (ECPublicKeyParameters)PublicKeyFactory.CreateKey(pubKeyBytes);
        
        SM2Engine sm2Engine = new SM2Engine(new SM3Digest());
        sm2Engine.Init(true, new ParametersWithRandom(pubKey));
        
        byte[] input = Encoding.UTF8.GetBytes(plainText);
        byte[] encrypted = sm2Engine.ProcessBlock(input, 0, input.Length);
        return Hex.ToHexString(encrypted).ToUpper();
    }

    /// <summary>
    /// SM2私钥解密
    /// </summary>
    /// <param name="cipherText">Hex大写的密文</param>
    /// <param name="privateKey">Base64编码的私钥</param>
    /// <returns>明文</returns>
    public static string Sm2Decrypt(string cipherText, string privateKey)
    {
        byte[] privKeyBytes = Convert.FromBase64String(privateKey);
        ECPrivateKeyParameters privKey = (ECPrivateKeyParameters)PrivateKeyFactory.CreateKey(privKeyBytes);
        
        SM2Engine sm2Engine = new SM2Engine(new SM3Digest());
        sm2Engine.Init(false, privKey);
        
        byte[] encrypted = Hex.Decode(cipherText);
        byte[] decrypted = sm2Engine.ProcessBlock(encrypted, 0, encrypted.Length);
        return Encoding.UTF8.GetString(decrypted);
    }

    /// <summary>
    /// SM4 ECB模式加密
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="hexKey">Hex编码的密钥</param>
    /// <returns>Hex大写的密文</returns>
    public static string Sm4EcbEncrypt(string plainText, string hexKey)
    {
        byte[] key = Hex.Decode(hexKey);
        SM4Engine sm4Engine = new SM4Engine();
        PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(
            new EcbBlockCipher(sm4Engine), new Pkcs7Padding());
        
        cipher.Init(true, new KeyParameter(key));
        
        byte[] input = Encoding.UTF8.GetBytes(plainText);
        byte[] output = new byte[cipher.GetOutputSize(input.Length)];
        int len = cipher.ProcessBytes(input, 0, input.Length, output, 0);
        len += cipher.DoFinal(output, len);
        
        return Hex.ToHexString(output, 0, len).ToUpper();
    }

    /// <summary>
    /// SM4 ECB模式解密
    /// </summary>
    /// <param name="cipherText">Hex大写的密文</param>
    /// <param name="hexKey">Hex编码的密钥</param>
    /// <returns>明文</returns>
    public static string Sm4EcbDecrypt(string cipherText, string hexKey)
    {
        byte[] key = Hex.Decode(hexKey);
        byte[] encrypted = Hex.Decode(cipherText);
        
        SM4Engine sm4Engine = new SM4Engine();
        PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(
            new EcbBlockCipher(sm4Engine), new Pkcs7Padding());
        
        cipher.Init(false, new KeyParameter(key));
        
        byte[] output = new byte[cipher.GetOutputSize(encrypted.Length)];
        int len = cipher.ProcessBytes(encrypted, 0, encrypted.Length, output, 0);
        len += cipher.DoFinal(output, len);
        
        return Encoding.UTF8.GetString(output, 0, len);
    }

    /// <summary>
    /// 生成SM2密钥对
    /// </summary>
    /// <returns>(公钥Base64, 私钥Base64)</returns>
    public static (string, string) GenerateSm2KeyPair()
    {
        ECKeyPairGenerator generator = new ECKeyPairGenerator();
        generator.Init(new ECKeyGenerationParameters(Sm2DomainParams, new SecureRandom()));
        
        AsymmetricCipherKeyPair keyPair = generator.GenerateKeyPair();
        byte[] pubKey = SubjectPublicKeyInfoFactory.CreateSubjectPublicKeyInfo(
            keyPair.Public).GetDerEncoded();
        byte[] privKey = PrivateKeyInfoFactory.CreatePrivateKeyInfo(
            keyPair.Private).GetDerEncoded();
        
        return (Convert.ToBase64String(pubKey), Convert.ToBase64String(privKey));
    }

    /// <summary>
    /// 测试SM2和SM4加密流程
    /// </summary>
    public static void TestSm2Sm4Enc()
    {
        // 生成密钥对
        var (publicKey, privateKey) = GenerateSm2KeyPair();
        string keyPlain = "0000111122223333444455556666FFFF";
        string plainText = "Hello, 国密算法!";
        
        // SM2加密SM4密钥
        string keyEnc = Sm2Encrypt(keyPlain, publicKey);
        // SM4加密明文
        string plainEnc = Sm4EcbEncrypt(plainText, keyPlain);
        
        Console.WriteLine($"enc keyEnc = {keyEnc}");
        Console.WriteLine($"enc textEnc = {plainEnc}");
        
        // SM2解密SM4密钥
        string keySrc = Sm2Decrypt(keyEnc, privateKey);
        // SM4解密密文
        string plainSrc = Sm4EcbDecrypt(plainEnc, keySrc);
        
        Console.WriteLine($"dec keySrc = {keySrc}");
        Console.WriteLine($"dec textSrc = {plainSrc}");
    }

    public static void Main(string[] args)
    {
        TestSm2Sm4Enc();
    }
    
    
    public static string Sm2EncryptForHutool(string plainText, string publicKeyBase64)
{
    // 1. 强制指定 sm2p256v1 曲线
    var ecParams = ECNamedCurveTable.GetByName("sm2p256v1");
    var ecDomainParams = new ECDomainParameters(ecParams.Curve, ecParams.G, ecParams.N, ecParams.H);

    // 2. 加载 DER 格式公钥
    var pubKeyBytes = Convert.FromBase64String(publicKeyBase64);
    var pubKey = (ECPublicKeyParameters)PublicKeyFactory.CreateKey(pubKeyBytes);

    // 3. 显式配置 C1C3C2 模式和 SM3 哈希
    var sm2Engine = new SM2Engine(new SM3Digest(), SM2Engine.Mode.C1C3C2); // 关键点！
    sm2Engine.Init(true, new ParametersWithRandom(pubKey, new SecureRandom()));

    // 4. 加密并返回 Hex 字符串（与 Hutool 兼容）
    byte[] encrypted = sm2Engine.ProcessBlock(Encoding.UTF8.GetBytes(plainText), 0, plainText.Length);
    return BitConverter.ToString(encrypted).Replace("-", "").ToUpper();
}
}