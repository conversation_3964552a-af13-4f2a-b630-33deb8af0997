
using System.Text;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Asn1.X9;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Digests;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Utilities.Encoders;
using Base64 = Org.BouncyCastle.Utilities.Encoders.Base64;

namespace Settlement.Infrastructure.Common;

public static class Sm2Utils
{
    
    public static string JsonMapToStr(JObject json)
    {
        var sb = new StringBuilder();
        foreach (var prop in json.Properties().OrderBy(p => p.Name))
        {
            if (!string.IsNullOrEmpty(prop.Value?.ToString()))
                sb.Append($"{prop.Name}={prop.Value}&");
        }
        return sb.Length > 0 ? sb.ToString(0, sb.Length - 1) : "";
    }

    public static ECPrivateKeyParameters PrivKeySm2FromBase64Str(string key)
    {
        var keyBytes = Base64.Decode(key);
        return (ECPrivateKeyParameters)PrivateKeyFactory.CreateKey(keyBytes);
    }

    public static ECPublicKeyParameters PubKeySm2FromBase64Str(string key)
    {
        var keyBytes = Base64.Decode(key);
        return (ECPublicKeyParameters)PublicKeyFactory.CreateKey(keyBytes);
    }

    public static string Sign(ECPrivateKeyParameters privateKey, string data)
    {
        var signer = SignerUtilities.GetSigner("SM3withSM2");
        signer.Init(true, privateKey);
        signer.BlockUpdate(Encoding.UTF8.GetBytes(data), 0, data.Length);
        return Base64.ToBase64String(signer.GenerateSignature());
    }

    public static bool Verify(ECPublicKeyParameters publicKey, string data, string signature)
    {
        if (string.IsNullOrEmpty(signature)) return false;
        try
        {
            // 使用十六进制解码签名（与Java代码一致）
            byte[] signatureBytes = Hex.Decode(signature);
            
            // 准备验证器
            var signer = SignerUtilities.GetSigner("SM3withSM2");
            signer.Init(false, publicKey);
            // 处理数据
            byte[] dataBytes = Encoding.UTF8.GetBytes(data);
            signer.BlockUpdate(dataBytes, 0, dataBytes.Length);
            // 验证签名
            return signer.VerifySignature(signatureBytes);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"SM2签名验证异常: {ex.Message}");
            return false;
        }
    }

    public static bool VerifyHex(string publicKey, string data, string signature)
    {
        try
        {
            byte[] keyByte = DecodeHex(publicKey);
            byte[] dataByte = Encoding.UTF8.GetBytes(data);
            byte[] signatureByte = DecodeHex(signature);

            if (dataByte == null || dataByte.Length == 0 ||
                signatureByte == null || signatureByte.Length == 0 ||
                keyByte == null || keyByte.Length == 0)
            {
                return false;
            }

            // 使用BouncyCastle初始化SM2验证
            X9ECParameters sm2p256v1 = ECNamedCurveTable.GetByName("sm2p256v1");
            ECDomainParameters domainParams = new ECDomainParameters(
                sm2p256v1.Curve, sm2p256v1.G, sm2p256v1.N, sm2p256v1.H);
            ECPublicKeyParameters pubKey = (ECPublicKeyParameters)PublicKeyFactory.CreateKey(keyByte);

            ISigner signer = SignerUtilities.GetSigner("SM3withSM2");
            signer.Init(false, pubKey);
            signer.BlockUpdate(dataByte, 0, dataByte.Length);
            return signer.VerifySignature(signatureByte);
        }
        catch
        {
            return false;
        }
    }

    private static byte[] DecodeHex(string data)
    {
        try
        {
            return Enumerable.Range(0, data.Length)
                .Where(x => x % 2 == 0)
                .Select(x => Convert.ToByte(data.Substring(x, 2), 16))
                .ToArray();
        }
        catch
        {
            Console.WriteLine("进行hex解码失败.");
            return [];
        }
    }

    // SM2 加密（明文输入，Base64 密文输出）
    public static string Encrypt(string publicKeyBase64, string plainText)
    {
        byte[] publicKeyBytes = Convert.FromBase64String(publicKeyBase64);
        byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
        
        // 解析公钥
        AsymmetricKeyParameter publicKey = PublicKeyFactory.CreateKey(publicKeyBytes);
        
        // 执行加密
        SM2Engine engine = new SM2Engine();
        engine.Init(true, new ParametersWithRandom(publicKey, new SecureRandom()));
        byte[] encryptedBytes = engine.ProcessBlock(plainBytes, 0, plainBytes.Length);
        
        return Convert.ToBase64String(encryptedBytes);
    }
    
    /// <summary>
    /// 专为Hutool 5.5.1兼容的SM2加密方法 - 使用C1C2C3模式
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKeyDerBase64">Base64编码的公钥</param>
    /// <returns>Base64编码的密文</returns>
    public static string Sm2EncryptForHutool(string plainText, string publicKeyDerBase64)
    {
        try
        {
            // 1. 强制使用 sm2p256v1 曲线
            var ecParams = ECNamedCurveTable.GetByName("sm2p256v1");
            var ecDomainParams = new ECDomainParameters(ecParams.Curve, ecParams.G, ecParams.N, ecParams.H);

            // 2. 加载 DER 格式公钥
            var pubKey = (ECPublicKeyParameters)PublicKeyFactory.CreateKey(
                Convert.FromBase64String(publicKeyDerBase64));

            // 3. 使用 SM2Engine 并指定 C1C2C3 模式（Hutool默认模式）
            var sm2Engine = new SM2Engine(SM2Engine.Mode.C1C2C3);
            sm2Engine.Init(true, new ParametersWithRandom(pubKey, new SecureRandom()));

            // 4. 加密
            byte[] encrypted = sm2Engine.ProcessBlock(
                Encoding.UTF8.GetBytes(plainText), 0, plainText.Length);

            // 5. 使用Base64编码
            return Convert.ToHexString(encrypted).ToUpperInvariant();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"SM2加密异常: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 专为Hutool 5.5.1兼容的SM2加密方法 - 使用C1C3C2模式
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKeyDerBase64">Base64编码的公钥</param>
    /// <returns>Base64编码的密文</returns>
    public static string Sm2EncryptForHutoolC1C3C2(string plainText, string publicKeyDerBase64)
    {
        try
        {
            // 1. 强制使用 sm2p256v1 曲线
            var ecParams = ECNamedCurveTable.GetByName("sm2p256v1");
            var ecDomainParams = new ECDomainParameters(ecParams.Curve, ecParams.G, ecParams.N, ecParams.H);

            // 2. 加载 DER 格式公钥
            var pubKey = (ECPublicKeyParameters)PublicKeyFactory.CreateKey(
                Convert.FromBase64String(publicKeyDerBase64));

            // 3. 使用 SM2Engine 并指定 C1C3C2 模式
            var sm2Engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            sm2Engine.Init(true, new ParametersWithRandom(pubKey, new SecureRandom()));

            // 4. 加密
            byte[] encrypted = sm2Engine.ProcessBlock(
                Encoding.UTF8.GetBytes(plainText), 0, plainText.Length);

            // 5. 使用Base64编码
            return Convert.ToBase64String(encrypted);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"SM2加密异常: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 专为Hutool兼容的SM2加密方法 - 返回十六进制格式
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKeyDerBase64">Base64编码的公钥</param>
    /// <returns>十六进制编码的密文</returns>
    public static string Sm2EncryptForHutoolHex(string plainText, string publicKeyDerBase64)
    {
        try
        {
            // 1. 强制使用 sm2p256v1 曲线
            var ecParams = ECNamedCurveTable.GetByName("sm2p256v1");
            var ecDomainParams = new ECDomainParameters(ecParams.Curve, ecParams.G, ecParams.N, ecParams.H);

            // 2. 加载 DER 格式公钥
            var pubKey = (ECPublicKeyParameters)PublicKeyFactory.CreateKey(
                Convert.FromBase64String(publicKeyDerBase64));

            // 3. 使用 SM2Engine 默认模式（C1C3C2）
            var sm2Engine = new SM2Engine(new SM3Digest(),SM2Engine.Mode.C1C3C2);
            sm2Engine.Init(true, new ParametersWithRandom(pubKey, new SecureRandom()));

            // 4. 加密
            byte[] encrypted = sm2Engine.ProcessBlock(
                Encoding.UTF8.GetBytes(plainText), 0, plainText.Length);

            // 5. 转换为十六进制字符串
            return BitConverter.ToString(encrypted).Replace("-", "").ToLower();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"SM2加密异常: {ex.Message}");
            throw;
        }
    }

    // SM2 解密（Base64 密文输入，明文输出）
    public static string Decrypt(string privateKeyBase64, string cipherTextBase64)
    {
        byte[] privateKeyBytes = Convert.FromBase64String(privateKeyBase64);
        byte[] cipherBytes = Convert.FromBase64String(cipherTextBase64);
        
        // 解析私钥
        AsymmetricKeyParameter privateKey = PrivateKeyFactory.CreateKey(privateKeyBytes);
        
        // 执行解密
        SM2Engine engine = new SM2Engine();
        engine.Init(false, privateKey);
        byte[] decryptedBytes = engine.ProcessBlock(cipherBytes, 0, cipherBytes.Length);
        
        return Encoding.UTF8.GetString(decryptedBytes);
    }
    
}
