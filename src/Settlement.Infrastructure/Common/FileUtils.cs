using Microsoft.AspNetCore.StaticFiles;

namespace Settlement.Infrastructure.Common;

public static class FileUtils
{
   public static string GetMimeType(string fileName)
    {
        var provider = new FileExtensionContentTypeProvider();
        if (!provider.TryGetContentType(fileName, out string mimeType))
        {
            mimeType = "application/octet-stream"; // 默认类型
        }
        return mimeType;
    }
}