using System.Security.Cryptography;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using Config;
using Entity;
using Entity.Nkp;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ServiceStack;
using ServiceStack.Text;
using Settlement.Domain.Transaction;
using Settlement.Infrastructure.Common;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Settlement.Infrastructure.Proxy;

[Service(ServiceLifetime.Transient)]
public class OpenChinaumsApi
{
    private readonly ConfigManager _config;
    private readonly CacheHelper _cacheHelper;
    private readonly LogManager _log;
    private readonly RequestContext _user;
    private readonly NkpContext _nkpContext;

    public OpenChinaumsApi(IOptionsSnapshot<ConfigManager> config, CacheHelper cacheHelper, LogManager log,
        RequestContext user, NkpContext nkpContext)
    {
        _config = config.Value;
        _cacheHelper = cacheHelper;
        _log = log;
        _user = user;
        _nkpContext = nkpContext;
    }

    /// <summary>
    /// 子账户充值申请
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<SubAccountRechargeResp> SubAccountRecharge(SubAccountRechargeReq req)
    {
        var result = new SubAccountRechargeResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/corp/sub-account/token/recharge-apply-3";
            // 设置请求参数
            DateTime now = DateTime.Now;
            req.ChannelNo = _config.OpenChinaumsChannelNo;
            req.AppSerialNumber = EntityTools.SnowflakeId();
            req.AccountingDate = now.ToString("yyyyMMdd");
            req.RequestDate = now.ToString("yyyyMMdd");
            req.RequestTime = now.ToString("HHmmss");
            req.RandomCode = req.AppSerialNumber;
            req.RequestType = "1";
            _log.Info("SubAccountRecharge 请求", JsonSerializer.Serialize(req));
            result = await RequestAsync<SubAccountRechargeResp>(url, JsonSerializer.Serialize(req));
            _log.Info("SubAccountRecharge 响应", JsonSerializer.Serialize(result));
            // 处理响应结果
            if (!result.ErrorCode.Equals("********"))
            {
                _log.Error("SubAccountRecharge 失败", $"错误码: {result.ErrorCode}, 错误信息: {result.ErrorInfo}");
            }
        }
        catch (Exception e)
        {
            _log.Error("SubAccountRecharge 失败", e.Message);
            throw;
        }

        return result;
    }
    
    
    /// <summary>
    /// 子账户资金回收到上级单位支付账户
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<SubAccountRetrieveResp> SubAccountRetrieve(SubAccountRetrieveReq req)
    {
        var result = new SubAccountRetrieveResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/tran/corp/sub-account/withhold-3";
            // 设置请求参数
            req.ChannelNo = _config.OpenChinaumsChannelNo;
            var options = new JsonSerializerOptions 
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Encoder = JavaScriptEncoder.Create(UnicodeRanges.BasicLatin,
                    UnicodeRanges.CjkUnifiedIdeographs) // 包含中文常用字
            };
            string reqBodyString = JsonSerializer.Serialize(req, options);
            _log.Info("SubAccountRetrieve请求", reqBodyString);
            var resultString = await RequestAsync(url, reqBodyString);
            _log.Info("SubAccountRetrieve响应", resultString);
            result = JsonSerializer.Deserialize<SubAccountRetrieveResp>(resultString);
        }
        catch (Exception e)
        {
            _log.Error("SubAccountRetrieve 失败", e.Message);
            throw;
        }

        return result;
    }

    /// <summary>
    /// 子账户转账支付
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<WithholdPayResp> WithholdPay(WithholdPayReq req)
    {
        var result = new WithholdPayResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/tran/corp/sub-account/withhold-pay-3";
            // 设置请求参数
            req.ChannelNo = _config.OpenChinaumsChannelNo;
            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Encoder = JavaScriptEncoder.Create(UnicodeRanges.BasicLatin,
                    UnicodeRanges.CjkUnifiedIdeographs) // 包含中文常用字
            };
            string reqBodyString = JsonSerializer.Serialize(req, options);
            _log.Info("WithholdPay请求", reqBodyString);
            var resultString = await RequestAsync(url, reqBodyString);
            _log.Info("WithholdPay响应", resultString);
            result = JsonSerializer.Deserialize<WithholdPayResp>(resultString);
        }
        catch (Exception e)
        {
            _log.Error("WithholdPay 失败", e.Message);
            throw;
        }

        return result;
    }

    /// <summary>
    /// 单位支付账户转账接口
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<PayAccountPayResp> PayAccountPay(PayAccountPayReq req)
    {
        var result = new PayAccountPayResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/tran/corp/account/withhold-3";
            // 设置请求参数
            req.ChannelNo = _config.OpenChinaumsChannelNo;
            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Encoder = JavaScriptEncoder.Create(UnicodeRanges.BasicLatin,
                    UnicodeRanges.CjkUnifiedIdeographs) // 包含中文常用字
            };
            string reqBodyString = JsonSerializer.Serialize(req,options);
            _log.Info("PayAccountPay请求", reqBodyString);
            result = await RequestAsync<PayAccountPayResp>(url, reqBodyString);
            _log.Info("PayAccountPay响应", JsonSerializer.Serialize(result));
        }
        catch (Exception e)
        {
            _log.Error("PayAccountPay 失败", e.Message);
            throw;
        }

        return result;
    }

    /// <summary>
    /// 开通创建子账户
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<SubAccountResp> OpenSubAccount(SubAccountReq req)
    {
        var result = new SubAccountResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/corp/sub-account/open-without-pic-3";
            // 设置请求参数
            var sm4key = Sm4CryptoUtils.GenerateSM4Key();
            DateTime now = DateTime.Now;
            req.ChannelNo = _config.OpenChinaumsChannelNo;
            req.MerchantNo = _config.OpenChinaumsMchntNo;
            req.AppSerialNumber = EntityTools.SnowflakeId();
            req.RequestDate = now.ToString("yyyyMMdd");
            req.RequestTime = now.ToString("HHmmss");

            // 查询项目代招企业信息赋值
            var (agentEnt, enterprise) = await GetProjectAgentEntInfo(req.ExternalUserNo);

            // 设置证书有效期
            if (string.IsNullOrWhiteSpace(enterprise.ToTime))
            {
                req.MerchantCertificateValidDate = "********";
            }
            else
            {
                long.TryParse(enterprise.ToTime, out long end);
                req.MerchantCertificateValidDate = end.FromUnixTimeMs().ToLocalTime().ToString("yyyyMMdd");
            }

            // 加密敏感信息
            req.MerchantName = Sm4CryptoUtils.EncryptString(sm4key, enterprise.Name);
            req.CorporateCertificateNo = Sm4CryptoUtils.EncryptString(sm4key, enterprise.CreditCode);
            req.Key = Sm2Utils.Sm2EncryptForHutoolHex(Sm4CryptoUtils.ToHexString(sm4key),
                _config.OpenChinaumsSM2PublicKey);

            // 验证必要的参数并发送请求
            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            };
            var reqBodyString = JsonSerializer.Serialize(req, options);
            _log.Info("OpenSubAccount 请求", reqBodyString);
            result = await RequestAsync<SubAccountResp>(url, reqBodyString);
            _log.Info("OpenSubAccount 响应", JsonSerializer.Serialize(result));

            // 处理响应结果
            if (!result.ErrorCode.Equals("********"))
            {
                _log.Error("OpenSubAccount 失败", $"错误码: {result.ErrorCode}, 错误信息: {result.ErrorInfo}");
            }
        }
        catch (Exception e)
        {
            _log.Error("OpenSubAccount 失败", e.Message);
            throw;
        }

        return result;
    }


    // 新增私有方法用于查询项目代招企业信息
    private async Task<(Agent_Ent agentEnt, Tyc_Enterprise enterprise)> GetProjectAgentEntInfo(string projectId)
    {
        var project = await _nkpContext.Project.FirstOrDefaultAsync(x => x.ProjectId == projectId);
        if (project == null || string.IsNullOrWhiteSpace(project.AgentEntId))
        {
            throw new BadRequestException("项目代招企业信息不存在");
        }

        var agentEnt = await _nkpContext.Agent_Ent
            .Include(agentEnt => agentEnt.TycEnterprise)
            .FirstOrDefaultAsync(x => x.AgentEntId == project.AgentEntId);

        if (agentEnt == null)
        {
            throw new BadRequestException("项目代招企业信息不存在");
        }

        if (string.IsNullOrWhiteSpace(agentEnt.TycEnterprise.Name))
        {
            throw new BadRequestException("代招企业名称不存在");
        }

        if (string.IsNullOrWhiteSpace(agentEnt.TycEnterprise.CreditCode))
        {
            throw new BadRequestException("代招企业统一社会信用代码不存在");
        }

        return (agentEnt, agentEnt.TycEnterprise);
    }


    #region JXY增加，待合并后可将此Regin标签删除

    /// <summary>
    /// 4.3.3[API-token]201696-单位支付账户提现申请
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<AccountWithdrawResp> AccountWithdraw(AccountWithdrawReq req)
    {
        var result = new AccountWithdrawResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/tran/account/withdraw-req-3";
            // 设置请求参数
            req.SetInit(_config.OpenChinaumsChannelNo);

            // 验证必要的参数
            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            };
            var reqBodyString = JsonSerializer.Serialize(req, options);
            _log.Info("AccountWithdraw 请求", reqBodyString);
            result = await RequestAsync<AccountWithdrawResp>(url, reqBodyString);
            _log.Info("AccountWithdraw 响应", JsonSerializer.Serialize(result));

            // 处理响应结果
            if (!result.ErrorCode.Equals("********"))
            {
                _log.Error("AccountWithdraw 失败", $"错误码: {result.ErrorCode}, 错误信息: {result.ErrorInfo}");
            }
            else //补充业务需要的其他参数
            {
                result.AppSsn = result.AppSsn;
                result.RandomCode = result.RandomCode;
            }
        }
        catch (Exception e)
        {
            _log.Error("AccountWithdraw 失败", e.Message);
            throw;
        }

        return result;
    }


    /// <summary>
    /// 4.4.1[API]交易状态查询
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<GetTranStatusResp> GetTranStatus(GetTranStatusReq req)
    {
        var result = new GetTranStatusResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/tran/trans/status/query-3";
            // 设置请求参数
            req.SetInit(_config.OpenChinaumsChannelNo);

            // 验证必要的参数
            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            };
            var reqBodyString = JsonSerializer.Serialize(req, options);
            _log.Info("GetTranStatus请求", reqBodyString);
            var resultString = await RequestAsync(url, reqBodyString);
            _log.Info("GetTranStatus响应", resultString);
            result = JsonSerializer.Deserialize<GetTranStatusResp>(resultString);

            // 处理响应结果
            if (!result.ErrorCode.Equals("********"))
            {
                _log.Error("GetTranStatus 失败", $"错误码: {result.ErrorCode}, 错误信息: {result.ErrorInfo}");
            }
        }
        catch (Exception e)
        {
            _log.Error("GetTranStatus 失败", e.Message);
            throw;
        }

        return result;
    }

    /// <summary>
    /// 4.4.2[API]单位支付账户余额查询V2.1
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<AccountBalanceResp> AccountBalance(AccountBalanceReq req, UmsCerRespWithKey? cert)
    {
        var result = new AccountBalanceResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/tran/account/account-bal/query-3";
            // 设置请求参数
            req.SetInit(_config.OpenChinaumsChannelNo);

            // 设置证书信息
            if (cert != null)
            {
                req.DigitalNumber = cert!.Dn!;
                req.CertSign = CertificateUtil.Sign(cert.Dn + req.RandomCode + req.AccountNumber,
                    cert.PfxToPrivateKey!);
            }

            // 验证必要的参数
            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Encoder = JavaScriptEncoder.Create(UnicodeRanges.BasicLatin,
                    UnicodeRanges.CjkUnifiedIdeographs) // 包含中文常用字
            };
            var reqBodyString = JsonSerializer.Serialize(req, options);
            _log.Info("AccountBalance 请求", reqBodyString);
            result = await RequestAsync<AccountBalanceResp>(url, reqBodyString);
            _log.Info("AccountBalance 响应", JsonSerializer.Serialize(result));

            // 处理响应结果
            if (!result.ErrorCode.Equals("********"))
            {
                _log.Error("AccountBalance 失败", $"错误码: {result.ErrorCode}, 错误信息: {result.ErrorInfo}");
            }
        }
        catch (Exception e)
        {
            _log.Error("AccountBalance 失败", e.Message);
            throw;
        }

        return result;
    }

    /// <summary>
    /// 4.4.5[API]子账户账户余额查询
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<SubAccountBalanceResp> SubAccountBalance(SubAccountBalanceReq req)
    {
        var result = new SubAccountBalanceResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/vact/corp/sub-account-bal/query-3";
            // 设置请求参数
            req.SetInit(_config.OpenChinaumsChannelNo);

            // 验证必要的参数
            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            };
            var reqBodyString = JsonSerializer.Serialize(req, options);
            _log.Info("SubAccountBalance 请求", reqBodyString);
            result = await RequestAsync<SubAccountBalanceResp>(url, reqBodyString);
            _log.Info("SubAccountBalance 响应", JsonSerializer.Serialize(result));

            // 处理响应结果
            if (!result.ErrorCode.Equals("********"))
            {
                _log.Error("SubAccountBalance 失败", $"错误码: {result.ErrorCode}, 错误信息: {result.ErrorInfo}");
            }
        }
        catch (Exception e)
        {
            _log.Error("SubAccountBalance 失败", e.Message);
            throw;
        }

        return result;
    }

    /// <summary>
    /// 4.4.8[API]电子回单查询
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public async Task<ElectronicReceiptResp> GetElectronicReceipt(ElectronicReceiptReq req)
    {
        var result = new ElectronicReceiptResp();
        try
        {
            var url = _config.OpenChinaumsBaseUrl + "/v1/inip/common/pic-upload/vact/corp/account/receipt-3";
            // 设置请求参数
            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            };
            var reqBodyString = JsonSerializer.Serialize(req, options);
            _log.Info("GetElectronicReceipt 请求", reqBodyString);
            var resultString = await RequestAsync<string>(url, reqBodyString);
            _log.Info("GetElectronicReceipt 响应",resultString );
            result = JsonSerializer.Deserialize<ElectronicReceiptResp>(resultString);
            // 处理响应结果
            if (!result.ErrorCode.Equals("********"))
            {
                _log.Error("GetElectronicReceipt 失败", $"错误码: {result.ErrorCode}, 错误信息: {result.ErrorInfo}");
            }
        }
        catch (Exception e)
        {
            _log.Error("GetElectronicReceipt 失败", e.Message);
            throw;
        }

        return result;
    }

    #endregion

    public async Task<T> RequestAsync<T>(string url, string reqBody)
    {
        try
        {
            var client = new FlurlClient(url)
                .Configure(settings =>
                {
                    settings.JsonSerializer = new SystemTextJsonSerializer(new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true,
                        WriteIndented = true,
                        Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });
                });
            var response = await client.Request("")
                .WithHeader("Content-Type", "application/json")
                .WithHeader("Authorization",
                    GetAuthorization(_config.OpenChinaumsAppId, _config.OpenChinaumsAppKey,
                        DateTime.Now.ToString("yyyyMMddHHmmss"), Guid.NewGuid().ToString("N"), reqBody))
                .PostStringAsync(reqBody)
                .ReceiveJson<T>();
            return response;
        }
        catch (FlurlHttpException ex)
        {
            // 获取更多错误详情
            var statusCode = ex.StatusCode;
            var responseBody = await ex.GetResponseStringAsync();
            var requestHeaders = ex.Call.Request.Headers;
            var requestMethod = ex.Call.HttpRequestMessage.Method;
            var requestUrl = ex.Call.HttpRequestMessage.RequestUri;
            // 构建详细的错误信息
            var errorDetails = new StringBuilder();
            errorDetails.AppendLine($"URL: {requestUrl}");
            errorDetails.AppendLine($"Method: {requestMethod}");
            errorDetails.AppendLine($"Status Code: {statusCode}");
            errorDetails.AppendLine("Request Headers:");
            foreach (var header in requestHeaders)
            {
                errorDetails.AppendLine($"{header.Name}:{header.Value}");
            }

            errorDetails.AppendLine($"Request Body: {reqBody}");
            errorDetails.AppendLine($"Response Body: {responseBody ?? "null"}");
            // 抛出包含详细信息的异常
            throw new Exception(errorDetails.ToString(), ex);
        }
        catch (Exception ex)
        {
            // 处理其他异常
            throw new Exception("请求发送失败", ex);
        }
    }

    public async Task<string> RequestAsync(string url, string reqBody)
    {
        try
        {
            var client = new FlurlClient(url)
                .Configure(settings =>
                {
                    settings.JsonSerializer = new SystemTextJsonSerializer(new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true,
                        WriteIndented = true,
                        Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });
                });
            var response = await client.Request("")
                .WithHeader("Content-Type", "application/json")
                .WithHeader("Authorization",
                    GetAuthorization(_config.OpenChinaumsAppId, _config.OpenChinaumsAppKey,
                        DateTime.Now.ToString("yyyyMMddHHmmss"), Guid.NewGuid().ToString("N"), reqBody))
                .PostStringAsync(reqBody)
                .ReceiveString();
            return response;
        }
        catch (FlurlHttpException ex)
        {
            // 获取更多错误详情
            var statusCode = ex.StatusCode;
            var responseBody = await ex.GetResponseStringAsync();
            var requestHeaders = ex.Call.Request.Headers;
            var requestMethod = ex.Call.HttpRequestMessage.Method;
            var requestUrl = ex.Call.HttpRequestMessage.RequestUri;
            // 构建详细的错误信息
            var errorDetails = new StringBuilder();
            errorDetails.AppendLine($"URL: {requestUrl}");
            errorDetails.AppendLine($"Method: {requestMethod}");
            errorDetails.AppendLine($"Status Code: {statusCode}");
            errorDetails.AppendLine("Request Headers:");
            foreach (var header in requestHeaders)
            {
                errorDetails.AppendLine($"{header.Name}:{header.Value}");
            }

            errorDetails.AppendLine($"Request Body: {reqBody}");
            errorDetails.AppendLine($"Response Body: {responseBody ?? "null"}");
            // 抛出包含详细信息的异常
            throw new Exception(errorDetails.ToString(), ex);
        }
        catch (Exception ex)
        {
            // 处理其他异常
            throw new Exception("请求发送失败", ex);
        }
    }

    public string GetAuthorization(string appId, string appKey, string timestamp, string nonce, string body)
    {
        // 计算 body 的 SHA256 哈希
        byte[] bodyBytes = Encoding.UTF8.GetBytes(body);
        string bodyHash;
        using (SHA256 sha256 = SHA256.Create())
        {
            byte[] hashBytes = sha256.ComputeHash(bodyBytes);
            bodyHash = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
        }

        // 准备签名字符串
        string stringToSign = appId + timestamp + nonce + bodyHash;

        // 计算 HMAC-SHA256 签名
        byte[] signatureBytes;
        using (HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(appKey)))
        {
            signatureBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(stringToSign));
        }

        // Base64 编码签名
        string signature = Convert.ToBase64String(signatureBytes);

        // 构建授权头
        return $"OPEN-BODY-SIG AppId=\"{appId}\",Timestamp=\"{timestamp}\",Nonce=\"{nonce}\",Signature=\"{signature}\"";
    }
}
