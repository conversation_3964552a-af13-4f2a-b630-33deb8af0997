﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  
  <ItemGroup>
  <ProjectReference Include="..\Public\Infrastructure\Infrastructure.csproj" />
  <ProjectReference Include="..\Settlement.Domain\Settlement.Domain.csproj" />
  <PackageReference Include="BouncyCastle.Cryptography" Version="2.2.1" />
  </ItemGroup>

  <PropertyGroup>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\net8.0\Settlement.Infrastructure.xml</DocumentationFile>
    <NoWarn>1701;1702;1591;NU1803;</NoWarn>
  </PropertyGroup>
</Project>
