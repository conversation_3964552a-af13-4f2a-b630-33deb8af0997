﻿using System;
using System.Security.Cryptography;
using Infrastructure.Exceptions;
using System.Text;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;
using Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using Config.Enums;
using Config.CommonModel;

namespace Infrastructure.Common;

public static class RetryHelper
{
    public static async Task<TResult> Do<TResult>(Func<TResult> action, int retryIntervalMs = 500, int maxAttempCount = 3)
    {
        Exception? exception = null;
        for (int i = 0; i < maxAttempCount; i++)
        {
            try
            {
                return action();
            }
            catch (Exception ex)
            {
                exception = ex;
                await Task.Delay(retryIntervalMs);
            }
        }

        throw exception ?? new Exception("未知错误");
    }

    public static async Task<TResult> Do<TResult>(Func<Task<TResult>> action, int retryIntervalMs = 50, int maxAttempCount = 3)
    {
        Exception? exception = null;
        for (int i = 0; i < maxAttempCount; i++)
        {
            try
            {
                return await action();
            }
            catch (Exception ex)
            {
                exception = ex;
                await Task.Delay(retryIntervalMs);
            }
        }

        throw exception ?? new Exception("未知错误");
    }

    public static async Task Do(Action action, int retryIntervalMs = 50, int maxAttempCount = 3)
    {
        Exception? exception = null;
        for (int i = 0; i < maxAttempCount; i++)
        {
            try
            {
                action();
                return;
            }
            catch (Exception ex)
            {
                exception = ex;
                await Task.Delay(retryIntervalMs);
            }
        }

        throw exception ?? new Exception("未知错误");
    }
}