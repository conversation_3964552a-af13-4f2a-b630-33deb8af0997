﻿using System;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using Azure;
using Config;
using Config.Enums.Nkp;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using MiniExcelLibs;
using ServiceStack.Text;
using Yitter.IdGenerator;

namespace Infrastructure.Common;

public class Tools
{
    /// <summary>
    /// 身份证号验证
    /// </summary>
    /// <param name="name"></param>
    /// <param name="identityCard"></param>
    /// <returns></returns>
    public static bool Certification(string identityCard, string? name = null)
    {
        if (string.IsNullOrWhiteSpace(identityCard))
            return false;

        var reg = new Regex(@"^[\u4e00-\u9fa5]{0,}$");
        if (!string.IsNullOrWhiteSpace(name) && !reg.IsMatch(name))
            return false;

        long n = 0;
        if (identityCard.Length < 18 || long.TryParse(identityCard.Remove(17), out n) == false || n < Math.Pow(10, 16) || long.TryParse(identityCard.Replace('x', '0').Replace('X', '0'), out n) == false)
        {
            return false;
        }
        string address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
        if (address.IndexOf(identityCard.Remove(2), StringComparison.Ordinal) == -1)
        {
            return false;
        }
        string birth = identityCard.Substring(6, 8).Insert(6, "-").Insert(4, "-");
        DateTime time = new DateTime();
        if (DateTime.TryParse(birth, out time) == false)
        {
            return false;
        }
        string[] arrVarifyCode = ("1,0,x,9,8,7,6,5,4,3,2").Split(',');
        string[] Wi = ("7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2").Split(',');
        char[] Ai = identityCard.Remove(17).ToCharArray();
        int sum = 0;
        for (int i = 0; i < 17; i++)
        {
            sum += int.Parse(Wi[i]) * int.Parse(Ai[i].ToString());
        }
        int y = -1;
        Math.DivRem(sum, 11, out y);
        if (arrVarifyCode[y] != identityCard.Substring(17, 1).ToLower())
        {
            return false;//校验码验证  
        }

        return true;
    }

    /// <summary>
    /// 身份证号加*
    /// </summary>
    /// <param name="idCard"></param>
    /// <returns></returns>
    public static string IdCardToX(string? idCard)
    {
        if (string.IsNullOrWhiteSpace(idCard) || idCard.Length < 18)
            return string.Empty;

        return string.Join('*', new string[idCard.Length - 3]) + idCard.Substring(14, 4);
    }

    public static string MobilToX(string? mobile)
    {
        if (mobile?.Length != 11)
            return string.Empty;

        return Regex.Replace(mobile, "(\\d{3})(\\d{4})(\\d{4})", "$1****$3");
    }

    //获取本周
    public static DateTime ThisWeek()
    {
        var weeknow = Convert.ToInt32(DateTime.Today.DayOfWeek);
        weeknow = weeknow == 0 ? 7 : weeknow;
        return DateTime.Today.AddDays(1 - weeknow);
    }

    /// <summary>
    /// 获取本月
    /// </summary>
    /// <returns></returns>
    public static DateTime ThisMonth()
    {
        return DateTime.Today.AddDays(1 - DateTime.Today.Day);
    }

    /// <summary>
    /// 计算年龄
    /// </summary>
    /// <param name="birthdate"></param>
    /// <returns></returns>
    public static int? GetAgeByBirthdate(DateOnly? birthdate)
    {
        if (birthdate == null)
            return null;

        DateTime now = DateTime.Now;
        int age = now.Year - birthdate.Value.Year;
        if (now.Month < birthdate.Value.Month || (now.Month == birthdate.Value.Month && now.Day < birthdate.Value.Day))
            age--;
        return age < 0 ? 0 : age;
    }

    /// <summary>
    /// 实体转换
    /// </summary>
    /// <typeparam name="TFrom"></typeparam>
    /// <typeparam name="TTo"></typeparam>
    /// <param name="model"></param>
    /// <returns></returns>
    public static TTo ModelConvert<TTo>(object model)
    {
        var json = JsonSerializer.SerializeToString(model);
        var result = JsonSerializer.DeserializeFromString<TTo>(json);

        return result!;
    }

    private static bool SnowflakeConfig = false;

    /// <summary>
    /// 注册雪花worker
    /// </summary>
    /// <param name="workderId">最大16383</param>
    public static void SnowflakeWorkerInit(ushort workderId)
    {
        var options = new IdGeneratorOptions
        {
            WorkerId = workderId,
            WorkerIdBitLength = 14,
            SeqBitLength = 7
        };
        YitIdHelper.SetIdGenerator(options);
        SnowflakeConfig = true;
    }

    /// <summary>
    /// 自动注册雪花worker
    /// </summary>
    public static async Task SnowflakeWorkerInit(bool isProd)
    {
        using var locker = await MyRedis.Lock("SnowflakeLocker", 10);

        var snowflakeKey = string.Empty;
        var minId = 3000;
        var maxId = 16000;
        if (isProd)
            snowflakeKey = "snowflakeworkeyprod";
        else
        {
            minId = 101;
            maxId = 1000;
            snowflakeKey = "snowflakeworkey";
        }



        var nextWokerId = MyRedis.Client.Incr(snowflakeKey);
        if (nextWokerId < minId || nextWokerId > maxId)
        {
            nextWokerId = minId;
            MyRedis.Client.Set(snowflakeKey, nextWokerId);
        }

        var options = new IdGeneratorOptions
        {
            WorkerId = (ushort)nextWokerId,
            WorkerIdBitLength = 14,
            SeqBitLength = 7
        };
        YitIdHelper.SetIdGenerator(options);
        SnowflakeConfig = true;
    }

    /// <summary>
    /// 雪花Id
    /// </summary>
    /// <returns></returns>
    public static string NextId()
    {
        if (!SnowflakeConfig)
            throw new Exception("尚未注册worker");

        return YitIdHelper.NextId().ToString();
    }

    /// <summary>
    /// 集合转树结构
    /// </summary>
    /// <typeparam name="object"></typeparam>
    public static IEnumerable<object> GetChildrenTreeNode<T, K>(IEnumerable<T> notes, Func<T, K> idSelector,
        Func<T, K> parentIdSelector, K? rootId = default(K), string childrenNodeName = "Children") where T : class
    {
        var dView = notes.Where(x =>
        {
            K pid = parentIdSelector(x);
            return (rootId == null && pid == null) || (rootId != null && pid?.Equals(rootId) == true);
        }).ToList();
        // List<Object> objList = new List<object>();
        foreach (var dr in dView)
        {
            var testType = typeof(T);
            var propertyInfos = testType.GetProperties(BindingFlags.Instance | BindingFlags.Public
            | BindingFlags.NonPublic | BindingFlags.Static);

            //******************获取源对象数据****************//
            Dictionary<string, object> dicObj = new Dictionary<string, object>();

            foreach (var property in propertyInfos)
            {
                var value = property?.GetValue(dr);
                if (value != null)
                {
                    dicObj.Add(property!.Name, value);
                }
            }
            var aa = idSelector(dr);
            var children = GetChildrenTreeNode(notes, idSelector, parentIdSelector, idSelector(dr), childrenNodeName);
            if (children?.Count() > 0)
                dicObj.Add(childrenNodeName, children);
            //objList.Add(dicObj);
            yield return dicObj;
        }
        // return objList;
    }

    public static List<double> GetRandomMmoney(double money, int n)
    {
        double[] array = new double[n];
        Redpackage red = new Redpackage() { money = money, count = n };
        for (int i = 0; i < n; i++)
        {
            array[i] = GetRMPri(red);
        }
        return array.OrderBy(o => Guid.NewGuid()).ToList();
    }

    /// <summary>
    /// 即开即中，考虑机会均等，减少金额差较大的几率
    /// 随机产生，额度在0.01和剩余平均值*2之间
    /// </summary>
    /// <returns></returns>
    private static double GetRMPri(Redpackage redpackage)
    {
        //如果最后一个，返回全部
        if (redpackage.count == 1)
        {
            redpackage.count--;
            return Math.Round(redpackage.money);
        }
        var perAmount = (int)(redpackage.money / redpackage.count);

        //随机生成
        Random ran = new Random();
        double min = perAmount / 2;
        double max = perAmount * 2;
        min = min > 1 ? min : 1;
        max = max > 1 ? max : 1;
        double money = ran.NextDouble() * max;

        money = money <= min ? min : money;
        money = Convert.ToInt32(money);
        redpackage.count--;
        redpackage.money -= money;
        return money;
    }

    public static string GetErrMsg(Exception e)
    {
        if (string.IsNullOrWhiteSpace(e.InnerException?.Message))
            return e.Message;
        else
            return $"{e.Message}::{e.InnerException?.Message}";
    }

    /// <summary>
    /// 图片样式300x300
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string? ImageStyle300(string? str)
    {
        var result = str;

        if (!string.IsNullOrWhiteSpace(str))
            result = $"{str}?x-oss-process=style/300x300";

        return result;
    }

    public static IResult DownLoadExcel(object data, string fileName)
    {
        var memoryStream = new MemoryStream();
        memoryStream.SaveAs(data);
        memoryStream.Seek(0, SeekOrigin.Begin);
        return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
    }

    public static IResult DownLoadExcelByTemplate(string tplPath, object data, string fileName)
    {
        var memoryStream = new MemoryStream();
        memoryStream.SaveAsByTemplate(tplPath, data);
        memoryStream.Seek(0, SeekOrigin.Begin);
        return Results.File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
    }
}

public class BusinessTools
{
    public static string FormattingSalary(int Min, int Max, PostSalaryType SalaryType, PostWorkNature WorkNature, int Months = 12)
    {
        var salaryValue = WorkNature switch
        {
            PostWorkNature.全职 => FormattingSalary(Min, Max),
            PostWorkNature.应届毕业生 => FormattingSalary(Min, Max),
            _ => Min + "-" + Max
        };

        return SalaryType switch
        {
            PostSalaryType.面议 => "面议",
            PostSalaryType.月薪 => salaryValue + (Months > 12 ? $"·{Months}薪" : string.Empty),
            PostSalaryType.年薪 => salaryValue,
            PostSalaryType.周薪 => salaryValue + "元/周",
            PostSalaryType.日薪 => salaryValue + "元/天",
            PostSalaryType.时薪 => salaryValue + "元/时",
            _ => string.Empty,
        };
    }

    /// <summary>
    /// 格式化薪资显示
    /// </summary>
    /// <param name="Min"></param>
    /// <param name="Max"></param>
    /// <returns></returns>
    private static string FormattingSalary(int Min, int Max)
    {
        return (Min / 1000) + "k" + "-" + (Max / 1000) + "k";
    }
}

public class Redpackage
{
    /// <summary>
    /// 剩余红包数量
    /// </summary>
    public int count;

    /// <summary>
    /// 剩余金额
    /// </summary>
    public double money;
}