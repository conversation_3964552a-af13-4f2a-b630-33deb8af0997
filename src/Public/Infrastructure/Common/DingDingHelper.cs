﻿using Config;
using Flurl.Http;
using Flurl;
using ServiceStack.Text;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Config.CommonModel.DingDing;

namespace Infrastructure.Common;

/// <summary>
/// 钉钉api帮助类
/// </summary>
[Service(ServiceLifetime.Transient)]
public class DingDingHelper
{
    private readonly LogManager _log;

    public DingDingHelper(LogManager log)
    {
        _log = log;
    }

    /// <summary>
    /// 获取钉钉token（依托其他接口不可单独调用）
    /// </summary>
    /// <returns></returns>
    private async Task<string> GetDingDingAccessToken()
    {
        string redisKey = "dingdingkey";
        string token = MyRedis.Client.Get(redisKey);
        if (!string.IsNullOrWhiteSpace(token))
        {
            return token;
        }
        var url = $"{Constants.DingDingDomain}/gettoken?appkey={Constants.DingDingAppKey}&appsecret={Constants.DingDingAppSecret}";


        var tokenResult = await url.GetJsonAsync<DingDingAccountToken>();

        if (tokenResult.errcode == 0)
        {
            MyRedis.Client.Set(redisKey, token, 3600);
            return tokenResult.access_token!;
        }
        else
        {
            throw new Exception($"获取钉钉token失败:{url}_{tokenResult.errcode}_{tokenResult.errmsg}");
        }
    }

    /// <summary>
    /// 通用get请求
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="path"></param>
    /// <param name="param"></param>
    /// <returns></returns>
    private async Task<T> Get<T>(string path, object param) where T : class
    {
        string token = await GetDingDingAccessToken();

        try
        {
            var restr = await $"{Constants.DingDingDomain}/{path}?access_token={token}"
            .SetQueryParams(param)
            .GetStringAsync();

            var resp = JsonSerializer.DeserializeFromString<DingDingResponse<T>>(restr);

            if (resp.errcode != 0)
                throw new Exception($"钉钉请求错误：{restr}");

            return resp.result!;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 通用post请求
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="path"></param>
    /// <param name="param"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private async Task<T> Post<T>(string path, object param) where T : class
    {
        string token = await GetDingDingAccessToken();

        try
        {
            var restr = await $"{Constants.DingDingDomain}/{path}?access_token={token}"
            .PostJsonAsync(param)
            .ReceiveString();

            var resp = JsonSerializer.DeserializeFromString<DingDingResponse<T>>(restr);

            if (resp.errcode != 0)
                throw new Exception($"钉钉请求错误：{restr}");

            return resp.result!;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }

    /// <summary>
    /// 根据手机号获取用户userid
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetDingDingUserIdByPhoneResponse> GetDingDingUserIdByPhone(GetDingDingUserIdByPhoneRequest model)
    {
        var result = await Post<GetDingDingUserIdByPhoneResponse>($"topapi/v2/user/getbymobile", model);
        return result;
    }

    /// <summary>
    /// 根据unionid获取用户userid
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetDingDingUserIdByUnionIdResponse> GetDingDingUserIdByUnionId(GetDingDingUserIdByUnionIdRequest model)
    {
        var result = await Post<GetDingDingUserIdByUnionIdResponse>($"topapi/user/getbyunionid", model);
        return result;
    }

    /// <summary>
    /// 根据用户userid获取用户信息详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetDingDingUserDetailsByUserIdResponse> GetDingDingUserDetailsByUserId(GetDingDingUserDetailsByUserIdRequest model)
    {
        var result = await Post<GetDingDingUserDetailsByUserIdResponse>($"topapi/v2/user/get", model);
        return result;
    }

    /// <summary>
    /// 根据部门id获取部门名称
    /// </summary>
    /// <param name="BranchId"></param>
    /// <returns></returns>
    public async Task<GetGetDingBranchByBranchIdResponse> GetDingBranchByBranchId(int BranchId)
    {
        var result = await Post<GetGetDingBranchByBranchIdResponse>($"topapi/v2/department/get", new { dept_id = BranchId });
        return result;
    }

    /// <summary>
    /// 创建部门
    /// </summary>
    /// <returns></returns>
    public async Task<DdCreateDepartmentResponse> CreateDepartment(DdCreateDepartment model)
    {
        var result = await Post<DdCreateDepartmentResponse>($"topapi/v2/department/create", model);
        return result;
    }

    /// <summary>
    /// 更新用户
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<DdUpdateUserResponse> UpdateUser(DdUpdateUser model)
    {
        var result = await Post<DdUpdateUserResponse>($"topapi/v2/user/update", model);
        return result;
    }

    /// <summary>
    /// 获取部门列表
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetDepartmentInfo[]> GetDepartmentListSub(long? dept_id = null)
    {
        var result = await Post<GetDepartmentInfo[]>($"topapi/v2/department/listsub", new { dept_id = dept_id });
        return result;
    }

    /// <summary>
    /// 获取部门用户详情
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<GetDeptUsersResponse> GetDeptUsers(GetDeptUsers model)
    {
        var result = await Post<GetDeptUsersResponse>($"topapi/v2/user/list", model);
        return result;
    }
}
