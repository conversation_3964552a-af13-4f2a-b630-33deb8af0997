﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Infrastructure.Common;

public class Md5Helper
{
    public static string Md5(string str)
    {
        //将输入字符串转换成字节数组
        var buffer = Encoding.Default.GetBytes(str);
        //接着，创建Md5对象进行散列计算
        var data = MD5.Create().ComputeHash(buffer);

        //创建一个新的Stringbuilder收集字节
        var sb = new StringBuilder();

        //遍历每个字节的散列数据 
        foreach (var t in data)
        {
            //格式每一个十六进制字符串
            sb.Append(t.ToString("X2"));
        }

        //返回十六进制字符串
        return sb.ToString();
    }

    /// <summary>
    /// base64加密
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string Base64Encrypt(string str)
    {
        byte[] encbuff = Encoding.UTF8.GetBytes(str);
        return Convert.ToBase64String(encbuff);
    }
}

