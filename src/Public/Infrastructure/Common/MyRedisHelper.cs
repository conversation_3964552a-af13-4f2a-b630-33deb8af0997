﻿using System.Runtime.InteropServices;
using System;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Caching.Memory;
using FreeRedis;
using static FreeRedis.RedisClient;

namespace Infrastructure.Common;

public class MyRedis
{
    public static RedisClient Client = default!;

    // /// <summary>
    // /// 自定义开启分布式锁，超时返回null，可设置重试时间，csredis默认3毫秒一次太耗资源
    // /// </summary>
    // /// <param name="name">锁名称</param>
    // /// <param name="timeoutSeconds">超时（秒）</param>
    // /// <returns></returns>
    // public static CSRedisClientLock? Lock(string name, int timeoutSeconds = 10, int retryMillisecond = 100)
    // {
    //     name = $"CSRedisClientLock:{name}";
    //     var startTime = DateTime.Now;
    //     while (DateTime.Now.Subtract(startTime).TotalSeconds < timeoutSeconds)
    //     {
    //         var value = Guid.NewGuid().ToString();
    //         if (MyRedisHelper.MyRedis.Instance.Set(name, value, timeoutSeconds, RedisExistence.Nx) == true)
    //         {
    //             double refreshSeconds = (double)timeoutSeconds / 2.0;
    //             return new CSRedisClientLock(MyRedisHelper.MyRedis.Instance, name, value, timeoutSeconds, refreshSeconds, true);
    //         }
    //         Thread.CurrentThread.Join(retryMillisecond);
    //     }
    //     return null;
    // }

    // /// <summary>
    // /// 加锁
    // /// </summary>
    // /// <param name="name"></param>
    // /// <param name="timeoutSeconds"></param>
    // /// <returns></returns>
    // public static Task<LockController?> Lock(string name, int timeoutSeconds = 10)
    // {
    //     return Task.Run(() => (LockController?)MyRedis.Lock(name, 10));
    // }

    // <summary>
    /// 开启分布式锁，若超时返回null
    /// </summary>
    /// <param name="name">锁名称</param>
    /// <param name="timeoutSeconds">超时（秒）</param>
    /// <param name="autoDelay">自动延长锁超时时间，看门狗线程的超时时间为timeoutSeconds/2 ， 在看门狗线程超时时间时自动延长锁的时间为timeoutSeconds。除非程序意外退出，否则永不超时。</param>
    /// <returns></returns>
    public static Task<MyLocker?> Lock(string name, int timeoutSeconds, int reConnectSeconds = 50)
    {
        return Task.Run(() =>
        {
            name = $"RdsLock:{name}";
            var startTime = DateTime.Now;
            while (DateTime.Now.Subtract(startTime).TotalSeconds < timeoutSeconds)
            {
                var value = Guid.NewGuid().ToString();
                if (Client.SetNx(name, value, timeoutSeconds) == true)
                    return new MyLocker(Client, name, value);

                Thread.CurrentThread.Join(reConnectSeconds);
            }
            return null;
        });
    }

    /// <summary>
    /// 尝试开启分布式锁，若失败立刻返回null
    /// </summary>
    /// <param name="name"></param>
    /// <param name="timeoutSeconds"></param>
    /// <returns></returns>
    public static MyLocker? TryLock(string name, int timeoutSeconds = 10)
    {
        name = $"RdsLock:{name}";
        var value = Guid.NewGuid().ToString();
        if (Client.SetNx(name, value, timeoutSeconds) == true)
            return new MyLocker(Client, name, value);
        return null;
    }
}

// public class MyRedisLocker : IDisposable
// {

//     string _name;
//     int _timeoutSeconds;

//     internal MyRedisLocker(string name, int timeoutSeconds)
//     {
//         _name = name;
//         _timeoutSeconds = timeoutSeconds;
//     }

//     public void Dispose() => MyRedisHelper.MyRedis.Del(_name);
// }

public class MyLocker : IDisposable
{

    RedisClient _client;
    string _name;
    string _value;

    public MyLocker(RedisClient rds, string name, string value)
    {
        _client = rds;
        _name = name;
        _value = value;
    }

    /// <summary>
    /// 释放分布式锁
    /// </summary>
    /// <returns>成功/失败</returns>
    public bool Unlock()
    {
        return _client.Eval(@"local gva = redis.call('GET', KEYS[1])
if gva == ARGV[1] then
  redis.call('DEL', KEYS[1])
  return 1
end
return 0", new[] { _name }, _value)?.ToString() == "1";
    }

    public void Dispose() => this.Unlock();
}
