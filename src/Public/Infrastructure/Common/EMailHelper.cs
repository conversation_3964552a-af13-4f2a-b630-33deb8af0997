﻿using System;
using Config;
using Infrastructure.Extend;
using MailKit.Net.Smtp;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MimeKit;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class EMailHelper
{
    private ConfigManager _config;
    private readonly LogManager _logger;
    public EMailHelper(LogManager logger, IOptions<ConfigManager> config)
    {
        _config = config.Value;
        _logger = logger;
    }

    public async Task SendEmailAsync(MimeMessage message, List<MailboxAddress> address, string fromName = "诺零工")
    {
        if (string.IsNullOrWhiteSpace(_config.Warning?.SendMail))
            return;

        try
        {
            message.From.Add(new MailboxAddress(fromName, _config.Warning.SendMail));
            message.To.AddRange(address);

            using var client = new SmtpClient();

            client.ServerCertificateValidationCallback = (s, c, h, e) => true;
            client.AuthenticationMechanisms.Remove("XOAUTH2");

            await client.ConnectAsync(_config.Warning.SendMailSmtp, 465, true);
            await client.AuthenticateAsync(_config.Warning.SendMail, _config.Warning.SendMailKey);
            await client.SendAsync(message);
            await client.DisconnectAsync(true);
        }
        catch (Exception e)
        {
            _logger.Error("发邮件错误", Tools.GetErrMsg(e));
        }
    }
}