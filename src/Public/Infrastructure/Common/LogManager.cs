﻿using System;
using System.Text;
using Config.Enums;
using Config.CommonModel;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Entity;

namespace Infrastructure.Common;

[Service(ServiceLifetime.Transient)]
public class LogManager
{
    ILogger<LogManager> _logger;
    private RequestContext _request;
    public LogManager(RequestContext request,
        ILogger<LogManager> logger)
    {
        _request = request;
        _logger = logger;
    }

    public void Error<T>(string title, string message, T data)
    {
        var dataStr = string.Empty;
        if (data != null)
            dataStr = ServiceStack.Text.JsonSerializer.SerializeToString(data);

        Error(title, message, dataStr);
    }

    public void Error(string title, string message, string data = "")
    {
        var user = $"{_request?.Name}/{_request?.Account}/{_request?.Id}";
        _logger.LogError("{title} {message} {data}", title, message, data);
    }

    public void Info(string title, string message, string data = "")
    {
        _logger.LogInformation("{title} {message} {data}", title, message, data);
    }
}

