﻿using System;
using System.Text.RegularExpressions;

namespace Infrastructure.Common;

public static class DataValid
{
    /// <summary>
    /// 是否为有效的手机号
    /// </summary>
    /// <param name="mobile"></param>
    /// <returns></returns>
    public static bool IsMobile(this string? mobile)
    {
        if (string.IsNullOrEmpty(mobile))
            return false;

        return Regex.IsMatch(mobile, "^1[0-9]{10}$");//^(13[0-9]|15[012356789]|18[0-9]|14[579]|17[135678])[0-9]{8}$//^1[0-9]{10}
    }

    /// <summary>
    /// 是否为邮箱
    /// </summary>
    /// <param name="email"></param>
    /// <returns></returns>
    public static bool IsEMail(this string email)
    {
        if (string.IsNullOrEmpty(email))
            return false;

        return Regex.IsMatch(email, "^[a-zA-Z0-9\\._-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$");
    }
}