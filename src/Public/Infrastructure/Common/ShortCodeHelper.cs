/// <summary>
/// 短链接帮助类
/// <para>当前id可以存储到分布式缓存，起始值不要太小，否则会导致短网址太短。</para>
/// </summary>
public static class ShortCodeHelper
{
    /// <summary>
    /// 默认能够使用的字符集
    /// </summary>
    public static string CharSequence = "R9UspPSrgMVLQXJG470THbBdK2ah3NWEceDAniwly1YZ6Ifquox8FjOm5kCvtz";

    /// <summary>
    /// 混淆id为字符串
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static string MixIn(long id)
    {
        string Key = Convert(id);
        int s = 0;
        foreach (char c in Key)
        {
            s += (int)c;
        }
        int Len = Key.Length;
        int x = (s % Len);
        char[] arr = Key.ToCharArray();
        char[] newarr = new char[arr.Length];
        Array.Copy(arr, x, newarr, 0, Len - x);
        Array.Copy(arr, 0, newarr, Len - x, x);
        string NewKey = "";
        foreach (char c in newarr)
        {
            NewKey += c;
        }
        return NewKey;
    }

    /// <summary>
    /// 解开混淆字符串
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public static long MixOut(string key)
    {
        int s = 0;
        foreach (char c in key)
        {
            s += (int)c;
        }
        int Len = key.Length;
        int x = (s % Len);
        x = Len - x;
        char[] arr = key.ToCharArray();
        char[] newarr = new char[arr.Length];
        Array.Copy(arr, x, newarr, 0, Len - x);
        Array.Copy(arr, 0, newarr, Len - x, x);
        string NewKey = "";
        foreach (char c in newarr)
        {
            NewKey += c;
        }
        return Convert(NewKey);
    }

    /// <summary>
    /// 10进制转换为CharSequence长度的进制
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    private static string Convert(long id)
    {
        //递归结束条件
        if (id < CharSequence.Length)
            return CharSequence[(int)id].ToString();

        //递归计算每一位
        return Convert(id / CharSequence.Length) + CharSequence[(int)(id % CharSequence.Length)];
    }

    /// <summary>
    /// 将CharSequence长度的进制转为10进制
    /// </summary>
    /// <param name="Code"></param>
    /// <returns></returns>
    private static long Convert(string Code)
    {
        long num = 0;
        int Len = Code.Length;
        //从最后一位开始计算每一位代表的10进制数值
        for (int i = Len - 1; i >= 0; i--)
        {
            int t = CharSequence.IndexOf(Code[i]); //索引即为字符代表的10进制数值
            double s = (Len - i) - 1; //所处于的位代表的是CharSequence长度指定的几次幂
            long m = (long)(Math.Pow(CharSequence.Length, s) * t);
            num += m;
        }
        return num;
    }
}