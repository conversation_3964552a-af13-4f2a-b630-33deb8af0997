using Config;
using Config.Enums;
using Entity.Nkp;
using Flurl;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Nest;
using NetTopologySuite.Index.HPRtree;
using Newtonsoft.Json.Linq;
using RTools_NTS.Util;
using ServiceStack.Text;
using System.Reflection;

namespace Infrastructure.Proxy;

[Service(ServiceLifetime.Transient)]
public class NuoPinApi
{
    private readonly ConfigManager _config;
    private readonly LogManager _log;
    public NuoPinApi(IOptionsSnapshot<ConfigManager> config, LogManager log)
    {
        _config = config.Value;
        _log = log;
    }

    public async Task<NuoApi<object>> PostStaffingAdviser(string entId, string userId, string userName, string mobile)
    {
        string url = $"{_config.DataRdsServer}/NoahAccredit/CentreService/v1/Enterprise/SetEntStaffingAdviser";

        var token = await GetToken();


        var result = await url.WithOAuthBearerToken(token.AccessToken)
        .PostJsonAsync(new { PK_EID = entId, StaffingAdviser = new { ID = userId, Name = userName, Mobile = mobile }, IsSendMsg = false })
        .ReceiveJson<NuoApi<object>>();

        if (result == null)
            throw new Exception("获取诺聘hr信息失败");

        return result;
    }

    /// <summary>
    /// 获取结果
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="service">取值：NoahAccredit|NoahRecruit|NoahMetadata|NoahCircle|NoahActivity|NoahSettle|NoahBring|NoahTalentPool</param>
    /// <param name="method">Controller/Action</param>
    /// <param name="parameters">匿名对象</param>
    /// <param name="version">版本号</param>
    /// <param name="timeout">请求超时(秒)</param>
    /// <returns></returns>
    public async Task<T> GetResult<T>(string service, string method, object parameters, int version = 1, int timeout = 30)
    {
        if (timeout < 1) timeout = 30;
        string url = $"{_config.DataRdsServer}/{service}/CentreService/v{version}/{method}";
        var token = await GetToken();

        var result = await url.WithOAuthBearerToken(token.AccessToken)
            .WithTimeout(timeout)
            .PostJsonAsync(parameters)
            .ReceiveJson<NuoApi<T>>();

        return result.Data;
    }

    /// <summary>
    /// 诺聘获取token
    /// </summary>
    /// <returns></returns>
    public async Task<NuoPinToken> GetToken()
    {
        var cacheKey = $"nuopintoken";

        var result = MyRedis.Client.Get<NuoPinToken?>(cacheKey);

        if (!string.IsNullOrWhiteSpace(result?.AccessToken))
            return result;

        string url = $"{_config.DataRdsServer}/noahaccredit/oauth/token";

        try
        {
            result = (await url.PostJsonAsync(new
            {
                grant_type = "client_credentials",
                client_id = "6030519482000010006",
                client_secret = "2F031256C24542339C36597E1F381876",
                scope = "NoahAccredit NoahRecruit NoahMetadata NoahCircle NoahActivity NoahSettle NoahBring NoahTalentPool"
            }).ReceiveJson<NuoApi<NuoPinToken>>())?.Data;

            if (string.IsNullOrWhiteSpace(result?.AccessToken))
                throw new Exception("获取诺聘内部token失败");

            MyRedis.Client.Set(cacheKey, result, result.TokenExpiresTime / 2);

            return result;
        }
        catch (FlurlHttpException ex)
        {
            var error = await ex.GetResponseStringAsync();
            throw new Exception($"httpstatus:{ex.StatusCode}_{error}");
        }
    }
}

public class NuoPinToken
{
    public string AccessToken { get; set; } = string.Empty;
    public int TokenExpiresTime { get; set; }
    public string TokenType { get; set; } = string.Empty;
    public string Scope { get; set; } = string.Empty;
}

public class NuoApi<T>
{
    public int ErrCode { get; set; }
    public string ErrMsg { get; set; } = string.Empty;
    public T? Data { get; set; }
}

public class NuoApiPageData<T>
{
    public T? Rows { get; set; }

    public int Total { get; set; }
}