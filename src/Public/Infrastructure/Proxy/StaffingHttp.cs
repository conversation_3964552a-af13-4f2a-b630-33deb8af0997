using ServiceStack.Text;
using Flurl;
using Flurl.Http;
using Microsoft.Extensions.Hosting;
using Infrastructure.Exceptions;
using Config.CommonModel;
using Microsoft.Extensions.DependencyInjection;
using Infrastructure.Extend;

namespace Infrastructure.Proxy;

[Service(ServiceLifetime.Scoped)]
public class StaffingHttp
{
    private string _domain = "https://openapi.nuopin.cn/staffing/internal/v1";
    private readonly IHostEnvironment _hostEnvironment;
    public StaffingHttp(IHostEnvironment hostEnvironment)
    {
        _hostEnvironment = hostEnvironment;
        if (!hostEnvironment.IsProduction())
            _domain = "https://test-openapi.nuopin.cn/staffing/internal/v1";
    }

    /// <summary>
    /// 通用Get请求
    /// </summary>
    /// <param name="path"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T?> Get<T>(string path, object param)
    {
        var token = GetAccessToken();
        try
        {
            var restr = await $"{_domain}/{path}"
            .SetQueryParams(param)
            .WithOAuthBearerToken(token)
            .GetStringAsync();

            var resp = JsonSerializer.DeserializeFromString<T>(restr);

            return resp!;
        }
        catch (FlurlHttpException ex)
        {
            await ProcessingResults(ex);
            return default;
        }
    }

    /// <summary>
    /// 通用Post请求
    /// </summary>
    /// <param name="path"></param>
    /// <param name="param"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T?> Post<T>(string path, object param)
    {
        var token = GetAccessToken();
        try
        {
            var restr = await $"{_domain}/{path}"
            .WithOAuthBearerToken(token)
            .PostJsonAsync(param).ReceiveString();

            var resp = JsonSerializer.DeserializeFromString<T>(restr);

            return resp!;
        }
        catch (FlurlHttpException ex)
        {
            await ProcessingResults(ex);
            return default;
        }
    }

    /// <summary>
    /// 通用Put请求
    /// </summary>
    /// <param name="path"></param>
    /// <param name="param"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T?> Put<T>(string path, object param)
    {
        var token = GetAccessToken();
        try
        {
            var restr = await $"{_domain}/{path}"
            .WithOAuthBearerToken(token)
            .PutJsonAsync(param).ReceiveString();

            var resp = JsonSerializer.DeserializeFromString<T>(restr);

            return resp!;
        }
        catch (FlurlHttpException ex)
        {
            await ProcessingResults(ex);
            return default;
        }
    }

    /// <summary>
    /// 通用Delete请求
    /// </summary>
    /// <param name="path"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<T?> Delete<T>(string path)
    {
        var token = GetAccessToken();

        try
        {
            var restr = await $"{_domain}/{path}"
            .WithOAuthBearerToken(token)
            .DeleteAsync().ReceiveString();

            var resp = JsonSerializer.DeserializeFromString<T>(restr);

            return resp!;
        }
        catch (FlurlHttpException ex)
        {
            await ProcessingResults(ex);
            return default;
        }
    }

    /// <summary>
    /// 获取token
    /// </summary>
    /// <returns></returns>
    public string GetAccessToken()
    {
        return "8F842AACB09F4A3986314929CA99B553";
    }

    private async Task ProcessingResults(FlurlHttpException ex)
    {
        var error = await ex.GetResponseStringAsync();

        var msg = string.Empty;
        var code = string.Empty;
        try
        {
            var errorType = JsonSerializer.DeserializeFromString<ErrorType>(error);
            msg = errorType?.msg;
            code = errorType?.code ?? string.Empty;
        }
        catch
        {
            msg = "未知错误";
        }

        if (string.IsNullOrEmpty(msg))
            msg = "未知错误";

        if (ex.StatusCode == 401)
            throw new UnauthorizedException(msg, code);
        else if (ex.StatusCode == 400)
            throw new BadRequestException(msg, code);
        else if (ex.StatusCode == 403)
            throw new ForbiddenException(msg, code);
        else if (ex.StatusCode == 404)
            throw new NotFoundException(msg, code);
        else if (ex.StatusCode == 406)
            throw new NotAcceptableException(msg, code);
        else
            throw new Exception(msg);
    }
}
