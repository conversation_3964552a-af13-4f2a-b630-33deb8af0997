<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup>
    <NoWarn>1701;1702;1591;NU1803;CS8603;CS8981;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspectCore.Extensions.DependencyInjection" Version="2.4.0" />
    <PackageReference Include="DateOnlyTimeOnly.AspNet.Swashbuckle" Version="2.1.1" />
    <PackageReference Include="EFCore.NamingConventions" Version="8.0.3" />
    <PackageReference Include="FreeRedis" Version="1.1.5" />
    <PackageReference Include="IGeekFan.AspNetCore.Knife4jUI" Version="0.0.14" />
    <PackageReference Include="MailKit" Version="4.1.0" />
    <PackageReference Include="Flurl.Http" Version="3.2.4" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="10.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Entity\Entity.csproj" />
  </ItemGroup>
</Project>