using Config;
using Config.CommonModel;
using Entity;
using Entity.Main;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.CommonRepository;

[Service(ServiceLifetime.Transient)]
public class TokenRepository
{
    private readonly IDbContextFactory<MainContext> _contextFactory;
    public TokenRepository(IDbContextFactory<MainContext> contextFactory)
    {
        _contextFactory = contextFactory;
    }

    public TokenInfo CreateRefreshToken(string userId, TokenType type, ClientType? client = null)
    {
        var result = new TokenInfo();

        using var _context = _contextFactory.CreateDbContext();
        var rt = new Token_Refresh
        {
            ExpirationTime = DateTime.Now.AddSeconds(Constants.RefreshTokenExpire),
            Token = Guid.NewGuid().ToString().Replace("-", string.Empty),
            UserId = userId,
            Type = type
        };
        _context.Add(rt);

        var at = new Token_Access
        {
            ExpirationTime = DateTime.Now.AddSeconds(Constants.AccessTokenExpire),
            Token_Refresh = rt,
            Token = $"{Constants.TokenPrefix}{Guid.NewGuid().ToString().Replace("-", string.Empty)}",
            UserId = rt.UserId,
            Type = rt.Type,
            Client = client
        };
        _context.Add(at);

        _context.SaveChanges();

        result.AccessToken = at.Token;
        result.RefreshToken = rt.Token;
        result.TokenExpiresTime = Constants.AccessTokenExpire;
        result.UserId = rt.UserId;

        return result;
    }

    public async Task<TokenInfo> RefreshToken(string refreshToken)
    {
        //加锁刷新token
        var lockerKey = $"reftk:{refreshToken}";
        using var locker = await MyRedis.Lock(lockerKey, 10);

        if (locker == null)
            throw new Exception("刷新token获取锁失败");

        var result = new TokenInfo();

        using var _context = _contextFactory.CreateDbContext();

        var rt = _context.Token_Refresh
            .FirstOrDefault(x => x.Token == refreshToken && x.ExpirationTime > DateTime.Now);

        if (rt == null)
            throw new UnauthorizedException(string.Empty);

        //更新token
        rt.Token = Guid.NewGuid().ToString().Replace("-", string.Empty);

        var at = new Token_Access
        {
            ExpirationTime = DateTime.Now.AddSeconds(Constants.AccessTokenExpire),
            Token_Refresh = rt,
            Token = $"{Constants.TokenPrefix}{Guid.NewGuid().ToString().Replace("-", string.Empty)}",
            UserId = rt.UserId,
            Type = rt.Type,
            Client = rt.Client
        };
        _context.Add(at);

        _context.SaveChanges();

        result.AccessToken = at.Token;
        result.RefreshToken = rt.Token;
        result.TokenExpiresTime = Constants.AccessTokenExpire;
        result.UserId = rt.UserId;

        return result;
    }
}
