using System.Linq;
using System.Text;
using Config;
using Config.CommonModel;
using Config.Enums;
using Elasticsearch.Net;
using Entity;
using Entity.Nkp;
using Infrastructure.Common;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace Infrastructure.CommonRepository;

[Service(ServiceLifetime.Singleton)]
public class CommonDicRepository
{
    private IDbContextFactory<NkpContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly CacheHelper _cacheHelper;

    public CommonDicRepository(IDbContextFactory<NkpContext> staffingContextFactory,
        IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, LogManager log,
        CacheHelper cacheHelper)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 地区
    /// </summary>
    /// <returns></returns>
    public List<RegionModel> GetRegion()
    {
        var cacheKey = RedisKey.Dic.Region;
        var disable = -1;
        var result = _cacheHelper.GetMemoryCache<List<RegionModel>>(() =>
        {
            return _cacheHelper.GetRedisCache<List<RegionModel>>(() =>
            {
                using (var _context = _staffingContextFactory.CreateDbContext())
                {
                    var data = _context.Dic_Region.Where(x => x.Status == Config.Enums.Nkp.ActiveStatus.Active && x.Id.Length <= 6)
                    .Select(s => new RegionModel
                    {
                        Id = s.Id,
                        Name = s.Name,
                        City = s.City,
                        County = s.County,
                        Province = s.Province,
                        ParentId = s.ParentId
                    }).ToList();

                    //暂时排除特别行政区
                    data = data.Where(x => !x.Id.StartsWith("8")).ToList();
                    return data!;
                }
            }, cacheKey, disable);
        }, cacheKey, disable);
        return result;
    }

    /// <summary>
    /// 根据地区Id获取地区信息
    /// </summary>
    /// <param name="regionId"></param>
    /// <returns></returns>
    public CityModel GetCityById(string regionId)
    {
        var region = GetRegion();

        var rgList = new List<RegionModel>();

        var rid = regionId;
        while (!string.IsNullOrWhiteSpace(rid))
        {
            var det = region.FirstOrDefault(x => x.Id == rid);
            if (det == null)
                break;

            rid = det.ParentId;

            if (rgList.Any(x => x.Id == rid))
                break;

            rgList.Insert(0, det);
        }

        var result = new CityModel();
        var i = 0;
        result.Id = regionId;
        result.ProvinceName = rgList?.Skip(i++).Take(1).FirstOrDefault()?.Name;
        result.CityName = rgList?.Skip(i++).Take(1).FirstOrDefault()?.Name;
        result.CountyName = rgList?.Skip(i++).Take(1).FirstOrDefault()?.Name;
        result.TownName = rgList?.Skip(i++).Take(1).FirstOrDefault()?.Name;

        return result;
    }
}

public class CommonDicModel
{
    /// <summary>
    /// Id
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 父Id
    /// </summary>
    public string? ParentId { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    public string? Level { get; set; }
}

public class RegionModel
{
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 上级Id
    /// </summary>
    public string ParentId { get; set; } = string.Empty;

    /// <summary>
    /// 区
    /// </summary>
    public string County { get; set; } = default!;

    /// <summary>
    /// 市
    /// </summary>
    public string City { get; set; } = default!;

    /// <summary>
    /// 省
    /// </summary>
    public string Province { get; set; } = default!;
}
