﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Infrastructure.Extend;

public class EnumSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (context.Type.IsEnum)
        {
            schema.Description = string.Join(", ",
            Enum.GetNames(context.Type)
            .Select(name => $"{name} = {Convert.ToInt64(Enum.Parse(context.Type, name))}"));
        }
    }
}
