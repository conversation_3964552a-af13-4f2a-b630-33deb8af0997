﻿using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Dynamic;

namespace Infrastructure.Extend;

public static class EFCoreDbContextExtend
{
    public static IEnumerable<dynamic> SqlQueryDynamic(this DbContext db, string sql, params object[] parameters)
    {
        using (var cmd = db.Database.GetDbConnection().CreateCommand())
        {
            cmd.CommandText = sql;

            if (cmd.Connection!.State != ConnectionState.Open)
            {
                cmd.Connection.Open();
            }

            using var reader = cmd.ExecuteReader();
            while (reader.Read())
            {
                var row = new ExpandoObject() as IDictionary<string, object>;
                for (var fieldCount = 0; fieldCount < reader.FieldCount; fieldCount++)
                {
                    row.Add(reader.GetName(fieldCount), reader[fieldCount]);
                }
                yield return row;
            }
        }
    }
}
