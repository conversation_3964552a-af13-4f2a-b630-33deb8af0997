﻿using System;
using System.ComponentModel;
using System.Reflection;
namespace Infrastructure.Extend;

public static class StringExtend
{
    public static DateTime? ToNullableDate(this string dateString)
    {
        if (string.IsNullOrWhiteSpace(dateString))
        {
            return null;
        }

        if (DateTime.TryParse(dateString, out DateTime resultDate))
        {
            return resultDate;
        }

        return null;
    }

    public static decimal? ToNullableDecimal(this string decimalString)
    {
        if (string.IsNullOrWhiteSpace(decimalString))
        {
            return null;
        }

        if (decimal.TryParse(decimalString, out decimal resultDate))
        {
            return resultDate;
        }

        return null;
    }

    public static int? ToNullableInt(this string intString)
    {
        if (string.IsNullOrWhiteSpace(intString))
        {
            return null;
        }

        if (int.TryParse(intString, out int resultDate))
        {
            return resultDate;
        }

        return null;
    }

    public static string ToYMD(this DateTime date)
    {
        return date.ToString("yyyy-MM-dd");
    }

    public static string ToYMDS(this DateTime date)
    {
        return date.ToString("yyyy-MM-dd HH:mm:ss");
    }

    public static string ToYMD(this DateTime? date)
    {
        return date?.ToString("yyyy-MM-dd") ?? string.Empty;
    }

    public static string ToYMDS(this DateTime? date)
    {
        return date?.ToString("yyyy-MM-dd HH:mm:ss") ?? string.Empty;
    }

    public static DateTime? ToDateTime(this long timeStamp)
    {
        if (timeStamp <= 0)
        {
            return null;
        }
        var start = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        return start.AddSeconds(timeStamp).AddHours(8);
    }

    public static TimeOnly? ToNullableTime(this string? timeString)
    {
        if (string.IsNullOrWhiteSpace(timeString))
        {
            return null;
        }

        if (TimeOnly.TryParse(timeString, out TimeOnly resultTime))
        {
            return resultTime;
        }

        return null;
    }

    public static string ToHHMM(this TimeOnly time)
    {
        return time.ToString("HH:mm");
    }

    /// <summary>
    /// 获取枚举的描述 (Description)
    /// </summary>
    /// <param name="val"></param>
    /// <returns>没有描述信息时返回其Name属性</returns>
    public static string GetDescription(this Enum? val)
    {
        var t = val?.GetType();
        if (t == null || val == null)
            return string.Empty;

        var name = Enum.GetName(t, val);
        if (string.IsNullOrWhiteSpace(name))
            return string.Empty;

        var attr = t.GetMember(name)[0].GetCustomAttribute<DescriptionAttribute>(false);
        return (attr != null && !string.IsNullOrEmpty(attr.Description)) ? attr.Description : name;
    }
}
