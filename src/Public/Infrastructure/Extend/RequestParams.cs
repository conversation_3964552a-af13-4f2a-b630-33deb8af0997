﻿using System;
using System.Text;
using Microsoft.AspNetCore.Http;

namespace Infrastructure.Extend;

public class RequestParams
{
    /// <summary>
    /// 获取参数值
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public static string GetParams(HttpContext context)
    {
        var para = string.Empty;
        var method = context.Request.Method.ToUpper();

        // 检查请求是否包含文件
        if (context.Request.HasFormContentType && context.Request.Form.Files.Count > 0)
        {
            // 跳过文件请求的日志记录
            para = "文件";
        }
        else if (method == "GET" || method == "DELETE")
        {
            para = context.Request.QueryString.Value;
        }
        else if (method == "POST" || method == "PUT")
        {
            if (context.Request.Body.CanSeek)
            {
                context.Request.Body.Seek(0, SeekOrigin.Begin);
            }

            using (var stream = new StreamReader(context.Request.Body, Encoding.UTF8, true, 1024, leaveOpen: true))
            {
                para = stream.ReadToEnd();
            }

            if (context.Request.Body.CanSeek)
            {
                context.Request.Body.Seek(0, SeekOrigin.Begin);
            }
        }
        return para ?? string.Empty;
    }

    public static async Task<string> GetPostBody(HttpContext context)
    {
        context.Request.EnableBuffering(); // 允许重复读取Body
        context.Request.Body.Position = 0; // 重置流位置
        using var reader = new StreamReader(context.Request.Body, Encoding.UTF8);
        string reqJson = await reader.ReadToEndAsync();
        context.Request.Body.Position = 0; // 重置流位置
        return reqJson;
    }
}