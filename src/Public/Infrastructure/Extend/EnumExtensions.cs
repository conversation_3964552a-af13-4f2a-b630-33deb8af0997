using System.ComponentModel;

namespace Infrastructure.Extend;

public class EnumExtensions
{
    public static TEnum? FromDescription<TEnum>(string description) where TEnum : struct
    {
        foreach (var value in Enum.GetValues(typeof(TEnum)))
        {
            var field = value.GetType().GetField(value.ToString());
            if (field != null)
            {
                var attribute = (DescriptionAttribute)Attribute.GetCustomAttribute(
                    field,
                    typeof(DescriptionAttribute))! ?? throw new InvalidOperationException();

                if (attribute?.Description == description)
                {
                    return (TEnum)value;
                }
            }
        }

        return null;
    }
    
}