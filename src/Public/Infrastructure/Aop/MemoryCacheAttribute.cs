﻿using AspectCore.DynamicProxy;
using AspectCore.DynamicProxy.Parameters;
using Infrastructure.Common;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using ServiceStack.Text;

namespace Infrastructure.Aop;

public class MemoryCacheAttribute : AbstractInterceptorAttribute
{
    /// <summary>
    /// Seconds
    /// </summary>
    public int Expire { get; set; }

    // private Microsoft.Extensions.Caching.Memory.IMemoryCache memoryCache;
    private IMemoryCache? _memoryCache;
    public async override Task Invoke(AspectContext context, AspectDelegate next)
    {
        //Determine whether it is an asynchronous method
        bool isAsync = context.IsAsync();

        //First judge whether the method has a return value, and if not, do not make cache judgment
        var methodReturnType = context.GetReturnParameter().Type;
        if (methodReturnType == typeof(void) || methodReturnType == typeof(Task) || methodReturnType == typeof(ValueTask))
        {
            await next(context);
            return;
        }
        var returnType = methodReturnType;
        if (isAsync)
        {
            //Gets the type of the asynchronous return
            returnType = returnType.GenericTypeArguments.First();
        }
        //Get method parameter name
        var param = JsonSerializer.SerializeToString(context.Parameters);
        //Get the method name, that is, cache key value
        var key = $"Methods:{context.ImplementationMethod!.DeclaringType!.FullName}:{context.ImplementationMethod!.Name}:{param}";

        key = Md5Helper.Md5(key);
        //If the cache has a value, the cache value is returned directly
        _memoryCache = context.ServiceProvider.GetService<Microsoft.Extensions.Caching.Memory.IMemoryCache>();
        if (_memoryCache is null)
        {
            await next(context);
            return;
        }
        var cache = _memoryCache.Get(key);
        if (cache != null)
        {
            // var value = JsonSerializer.DeserializeFromString(cache, returnType);
            if (isAsync)
            {
                //Determine whether it is a task or a valuetask
                if (methodReturnType == typeof(Task<>).MakeGenericType(returnType))
                {
                    //Reflection gets the return value of task < > type, which is equivalent to Task.FromResult (value)
                    context.ReturnValue = typeof(Task).GetMethod(nameof(Task.FromResult))!.MakeGenericMethod(returnType).Invoke(null, new[] { cache });
                }
                else if (methodReturnType == typeof(ValueTask<>).MakeGenericType(returnType))
                {
                    //Reflection builds the return value of valuetask < > type, which is equivalent to new valuetask (value)
                    context.ReturnValue = Activator.CreateInstance(typeof(ValueTask<>).MakeGenericType(returnType), cache);
                }
            }
            else
            {
                context.ReturnValue = cache;
            }
            return;
        }

        await next(context);
        object returnValue;
        if (isAsync)
            returnValue = await context.UnwrapAsyncReturnValue();
        else
            returnValue = context.ReturnValue;

        if (returnValue != null)
            _memoryCache.Set(key, returnValue, TimeSpan.FromSeconds(Expire));
    }
}