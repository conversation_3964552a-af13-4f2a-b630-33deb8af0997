using System.Net;
using Config;
using Entity;
using Entity.Nkp;
using Flurl.Http;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace Infrastructure.Middleware;

public class AuthorizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IHostEnvironment _hostingEnvironment;

    public AuthorizationMiddleware(IHostEnvironment hostingEnvironment, RequestDelegate next)
    {
        _hostingEnvironment = hostingEnvironment;
        _next = next;
    }

    public async Task Invoke(HttpContext context, IOptionsSnapshot<ConfigManager> config, MainContext _context,
        RequestContext _user)
    {
        var _config = config.Value;
        var apiMeta = context.GetEndpoint()?.Metadata.GetMetadata<ApiMeta>();

        //是否需要身份验证
        if (apiMeta == null || apiMeta?.AuthType == ApiAuthType.None)
        {
            await _next(context);
            return;
        }

        //拿到token
        var token = string.Empty;
        if (context.Request.Headers.TryGetValue("Authorization", out var headerAuth))
        {
            var headerAuthStr = headerAuth.FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(headerAuthStr) && headerAuthStr.StartsWith("Bearer"))
            {
                token = headerAuthStr.Substring("Bearer".Length).Trim();
                _user.AccessToken = token;
            }
        }

        if (apiMeta?.AuthType == ApiAuthType.Internal)
        {
            //内部验证
            if (string.IsNullOrWhiteSpace(token) || !_config.ServiceKeys!.Contains(token))
            {
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                return;
            }

            _user.Id = token;
        }
        else if (apiMeta?.AuthType == ApiAuthType.Admin)
        {
            //管理端验证
            var at = _context.Token_Access
                .Where(x => x.Token == token && x.ExpirationTime > DateTime.Now)
                .Select(s => new
                {
                    s.Type,
                    s.UserId,
                    s.Client
                }).FirstOrDefault();

            if (at == null || at.Type != TokenType.管理端)
            {
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                return;
            }

            var admin = _context.Admin
                .Where(x => x.Id == at.UserId)
                .Select(s => new
                {
                    s.Id,
                    s.Name,
                    s.Powers,
                    s.Account,
                    s.Status
                }).FirstOrDefault();

            if (admin == null)
            {
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                return;
            }

            _user.Id = admin.Id;
            _user.Name = admin.Name;
            _user.Account = admin.Account;
            _user.Powers = admin.Powers;
        }
        else if (apiMeta?.AuthType == ApiAuthType.Nkp)
        {
            try
            {
                ShortUserInfo? usr = null;
                var tkmd5 = Md5Helper.Md5(token);
                var usrCacheKey = $"ut:{tkmd5}";
                usr = MyRedis.Client.Get<ShortUserInfo?>(usrCacheKey);
                if (usr == null)
                {
                    string url = $"{_config.StaffingServer}/common/v1/service/auth";
                    var userInfo = await url.WithOAuthBearerToken(token).GetJsonAsync<ShortUserInfo>();
                    usr = userInfo;
                }
                if (usr.AppType != TokenType.企业端 || string.IsNullOrWhiteSpace(usr.UserId))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    return;
                }
                _user.Id = usr.UserId;
                _user.Name = usr.NickName;
                _user.Powers = usr.Powers;
                _user.EntId = usr.EntId;
                _user.Account = usr.Mobile;
            }
            catch (HttpRequestException ex) when (ex.StatusCode.HasValue)
            {
                throw new UnauthorizedException($"调用鉴权接口错误：{ex}");
            }
            catch (UnauthorizedException ex)
            {
                throw new UnauthorizedException("鉴权失败！");
            }
            catch (Exception ex)
            {
                throw new Exception("处理鉴权接口响应时发生错误", ex);
            }
        }

        // 继续处理请求
        await _next(context);
    }
}