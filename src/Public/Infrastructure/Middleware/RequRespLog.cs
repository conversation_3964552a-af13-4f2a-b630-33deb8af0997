using Entity;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Net.Http.Headers;
using Serilog;

namespace Infrastructure.Middleware;

/// <summary>
/// 中间件
/// 记录请求和响应数据
/// </summary>
public class RequRespLog
{
    private readonly RequestDelegate _next;
    private readonly IDiagnosticContext _diagnosticContext;

    /// <summary>
    ///
    /// </summary>
    /// <param name="next"></param>
    public RequRespLog(RequestDelegate next, IDiagnosticContext diagnosticContext)
    {
        _next = next;
        _diagnosticContext = diagnosticContext;
    }

    public async Task InvokeAsync(HttpContext context, RequestContext _user)
    {
        context.Request.EnableBuffering();
        Stream originalBody = context.Response.Body;

        var apiStartTime = DateTime.Now;
        var requestId = context.TraceIdentifier;
        _user.RequestId = requestId;
        _user.ConnectionId = context.Connection.Id; 

        //拿到真实ip
        var ipAddress = context.Request.Headers["X-Forwarded-For"].ToString();
        if (string.IsNullOrWhiteSpace(ipAddress))
            ipAddress = context.Connection?.RemoteIpAddress?.ToString().Replace("::ffff:", string.Empty);
        else
        {
            var ips = ipAddress?.Trim().Trim(',').Split(',');
            ipAddress = ips?.FirstOrDefault();
        }

        var method = context.Request.Method.ToUpper();
        var para = RequestParams.GetParams(context);

        // //接口幂等性验证
        // if (method != "GET")
        // {
        //     // var lockKey = Md5Helper.Md5($"{_user.ConnectionId}_{method}_{para}");
        //     var lockKey = Md5Helper.Md5($"{_user.RequestIpAddress}_{_user.UserAgent}_{context.Request.Path}_{method}_{para}");
        //     using var lkr = MyRedis.TryLock(lockKey);
        //     if (lkr == null)
        //         throw new NotAcceptableException(string.Empty);
        // }

        var agent = context.Request?.Headers?[HeaderNames.UserAgent].ToString() ?? string.Empty;

        _user.UserAgent = agent;
        _user.RequestIp = ipAddress;

        await using var ms = new MemoryStream();
        context.Response.Body = ms;
    
        //去掉get请求中过的空字符串，改为null，否则会导致转换错误
        if (context.Request?.Method == HttpMethods.Get)
        {
            var queryCollection = context.Request.Query
                .Where(kvp => !string.IsNullOrEmpty(kvp.Value))
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value.ToString());

            var queryString = string.Join("&", queryCollection.Select(kvp => $"{kvp.Key}={kvp.Value}"));
            context.Request.QueryString = new QueryString("?" + queryString);
        }

        _diagnosticContext.Set("Request", para);

        await _next(context);
        // 存储响应数据
        context.Response.Body.Seek(0, SeekOrigin.Begin);
        var respBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
        context.Response.Body.Seek(0, SeekOrigin.Begin);
        // // 存储响应数据
        // var respBody = ResponseDataLog(ms);
        //
        // // if (_config.RequestLog)
        // {
        //     //异步记录日志
        //     var userId = $"{_user?.Account?.ToString() ?? string.Empty}({_user?.Id ?? string.Empty})";
        //     var url = context.Request?.Path;
        //     var apiEndTime = DateTime.Now;
        //     var ts = (apiEndTime - apiStartTime);
        //     var statusCode = context.Response.StatusCode.ToString();
        // }
        _diagnosticContext.Set("Response", respBody);
        ms.Position = 0;
        await ms.CopyToAsync(originalBody);
    }

    // private static async Task RequestDataLog(HttpContext context)
    // {
    //     var request = context.Request;
    //     var sr = new StreamReader(request.Body);

    //     var content = $" QueryData:{request.Path + request.QueryString}\r\n BodyData:{await sr.ReadToEndAsync()}";

    //     if (!string.IsNullOrEmpty(content))
    //     {
    //         Console.WriteLine($"Request Data:{content}");
    //         request.Body.Position = 0;
    //     }
    // }

    private string ResponseDataLog(Stream ms)
    {
        ms.Position = 0;
        var responseBody = new StreamReader(ms).ReadToEnd();
        return responseBody;
    }
}