using Entity.Ybg;
using EntityFrameworkCore.AutoHistory.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using ServiceStack.Text;
using Yitter.IdGenerator;

namespace Entity;

public partial class YbgContext : DbContext
{
    public YbgContext(DbContextOptions<YbgContext> options)
        : base(options)
    {

    }

    public virtual DbSet<WorkGroup> WorkGroup { get; set; } = default!;
    public virtual DbSet<WorkGroupArchives> WorkGroupArchives { get; set; } = default!;
    public virtual DbSet<WorkGroupDimission> WorkGroupDimission { get; set; } = default!;
    public virtual DbSet<WorkGroupEmployeeContract> WorkGroupEmployeeContract { get; set; } = default!;
    public virtual DbSet<WorkGroupEntry> WorkGroupEntry { get; set; } = default!;
    public virtual DbSet<WorkGroupUser> WorkGroupUser { get; set; } = default!;
    public virtual DbSet<WorkGroupContractTemplate> WorkGroupContractTemplate { get; set; } = default!;
    public virtual DbSet<WorkGroupFiliale> WorkGroupFiliale { get; set; } = default!;
    public virtual DbSet<PersonAuthIDCard> PersonAuthIDCard { get; set; } = default!;
    public virtual DbSet<JobHunter> JobHunter { get; set; } = default!;
    public virtual DbSet<AccountSystem> AccountSystem { get; set; } = default!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        //启用AutoHistory
        modelBuilder.EnableAutoHistory();

        modelBuilder.Entity<WorkGroup>().HasQueryFilter(b => b.IsRemove == 0);
        modelBuilder.Entity<WorkGroupArchives>().HasQueryFilter(b => b.IsRemove == 0);
        modelBuilder.Entity<WorkGroupDimission>().HasQueryFilter(b => b.IsRemove == 0);
        modelBuilder.Entity<WorkGroupEmployeeContract>().HasQueryFilter(b => b.IsRemove == 0);
        modelBuilder.Entity<WorkGroupEntry>().HasQueryFilter(b => b.IsRemove == 0);
        modelBuilder.Entity<WorkGroupUser>().HasQueryFilter(b => b.IsRemove == 0);
        modelBuilder.Entity<PersonAuthIDCard>().HasQueryFilter(b => b.IsRemove == 0);
        modelBuilder.Entity<JobHunter>().HasQueryFilter(b => b.IsRemove == 0);

        modelBuilder.Entity<WorkGroupEmployeeContract>()
        .HasOne(x => x.WorkGroupUser)
        .WithMany()
        .HasForeignKey(x => x.PK_WGSLID);

        modelBuilder.Entity<WorkGroupDimission>()
        .HasOne(x => x.WorkGroupUser)
        .WithMany()
        .HasForeignKey(x => x.PK_WGSLID);

        modelBuilder.Entity<WorkGroupUser>()
        .HasOne(x => x.WorkGroup)
        .WithMany()
        .HasForeignKey(x => x.PK_WGID);

        modelBuilder.Entity<WorkGroupEmployeeContract>()
        .HasOne(x => x.WorkGroupContractTemplate)
        .WithMany().IsRequired(false)
        .HasForeignKey(x => x.PK_WGCID);

        modelBuilder.Entity<WorkGroupDimission>()
        .HasOne(x => x.WorkGroupContractTemplate)
        .WithMany().IsRequired(false)
        .HasForeignKey(x => x.PK_WGCID);

        modelBuilder.Entity<WorkGroupContractTemplate>()
        .HasOne(x => x.WorkGroupFiliale)
        .WithMany().IsRequired(false)
        .HasForeignKey(x => x.PK_WGFID);

        modelBuilder.Entity<WorkGroupUser>()
        .HasOne(x => x.JobHunter)
        .WithMany()
        .HasForeignKey(x => x.PK_JHID);

        modelBuilder.Entity<WorkGroupEmployeeContract>()
        .HasOne(x => x.AccountSystem)
        .WithMany()
        .HasForeignKey(x => x.PK_AID);
    }
}