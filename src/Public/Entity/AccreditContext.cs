﻿using System;
using Entity;
using Microsoft.EntityFrameworkCore;

namespace Entity;

public partial class AccreditContext : DbContext
{
    public AccreditContext(DbContextOptions<AccreditContext> options)
        : base(options)
    {

    }

    public virtual DbSet<NoahEnterprise> Enterprise { get; set; } = default!;
    public virtual DbSet<ServiceHall> ServiceHall { get; set; } = default!;
    public virtual DbSet<PostResumeMutualPushRecord> PostResumeMutualPushRecord { get; set; } = default!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
    }
}