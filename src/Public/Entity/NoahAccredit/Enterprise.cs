﻿using System.ComponentModel.DataAnnotations;

namespace Entity;

public class NoahEnterprise
{
    /// <summary>
    /// 企业的ID
    /// </summary>
    [Key]
    public string PK_EID { get; set; } = default!;
    /// <summary>
    /// 父级ID(邀请注册的用户ID或者企业的ID)
    /// </summary>
    public string? ParentID { get; set; }
    /// <summary>
    /// 父级的邀请码
    /// </summary>
    public string? ParentInviteCode { get; set; }
    /// <summary>
    /// 父级ID的类型
    /// </summary>
    public int ParentType { get; set; }
    /// <summary>
    /// 企业类信息：普通,集团，诺亚等
    /// </summary>
    public int EnterpriseType { get; set; }
    /// <summary>
    /// 企业名称
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 企业名称简称(不查重)
    /// </summary>
    public string? Abbreviation { get; set; }
    /// <summary>
    /// 企业Logo
    /// </summary>
    public string? LogoUrl { get; set; }
    /// <summary>
    /// 企业所属行业
    /// </summary>
    public string? Profession { get; set; }
    /// <summary>
    /// 企业性质
    /// </summary>
    public int Nature { get; set; }
    /// <summary>
    /// 企业规模
    /// </summary>
    public int Scale { get; set; }
    /// <summary>
    /// 企业融资阶段
    /// </summary>
    public int Capital { get; set; }
    /// <summary>
    /// 设置为名企(Noah.Enum.EnableStatus)(关闭=0,开启=1)
    /// </summary>
    public int IsWellknown { get; set; }
    /// <summary>
    /// 企业标签（对应大数据label）
    /// </summary>
    public string? WellknownTags { get; set; }
    /// <summary>
    /// 设置为热门(Noah.Enum.EnableStatus)(关闭=0,开启=1)
    /// </summary>
    public int IsHot { get; set; }
    /// <summary>
    /// 企业标签
    /// </summary>
    public string? Label { get; set; }
    /// <summary>
    /// 企业福利
    /// </summary>
    public string? Welfare { get; set; }
    /// <summary>
    /// 企业所在省No
    /// </summary>
    public string? ProvinceNo { get; set; }
    /// <summary>
    /// 企业所在市No
    /// </summary>
    public string? CityNo { get; set; }
    /// <summary>
    /// 企业所在区No
    /// </summary>
    public string? DistrictNo { get; set; }
    /// <summary>
    /// 县No
    /// </summary>
    public string? CountyNo { get; set; }
    /// <summary>
    /// 企业所在省Name
    /// </summary>
    public string? ProvinceName { get; set; }
    /// <summary>
    /// 企业所在市Name
    /// </summary>
    public string? CityName { get; set; }
    /// <summary>
    /// 企业所在区Name
    /// </summary>
    public string? DistrictName { get; set; }
    /// <summary>
    /// 县Name
    /// </summary>
    public string? CountyName { get; set; }
    /// <summary>
    /// 企业详细地址
    /// </summary>
    public string? Address { get; set; }
    /// <summary>
    /// 企业所在地图经度
    /// </summary>
    //[Column(TypeName = "numeric(18, 6)")]
    public decimal? Longitude { get; set; }
    /// <summary>
    /// 企业所在地图纬度
    /// </summary>
    //[Column(TypeName = "numeric(18, 6)")]
    public decimal? Latitude { get; set; }
    /// <summary>
    /// 企业介绍
    /// </summary>
    public string? Recommend { get; set; }
    /// <summary>
    /// 企业环境照片
    /// </summary>
    public string? EnvironmentImage { get; set; }
    /// <summary>
    /// 企业的环境视频
    /// </summary>
    public string? EnvironmentVideo { get; set; }
    /// <summary>
    /// 营业执照
    /// </summary>
    public string? BusinessLicense { get; set; }
    /// <summary>
    /// 营业执照编号
    /// </summary>
    public string? BusinessLicenseNo { get; set; }
    /// <summary>
    /// 企业状态
    /// </summary>
    public int Status { get; set; }
    /// <summary>
    /// 会员等级
    /// </summary>
    public int MemberLevel { get; set; }
    /// <summary>
    /// 会员有效截至时间
    /// </summary>
    public DateTime? MemberExpireTime { get; set; }
    /// <summary>
    /// 注册来源
    /// </summary>
    public int FromType { get; set; }
    /// <summary>
    /// 总积分
    /// </summary>
    public long TotalIntegral { get; set; }
    /// <summary>
    /// 可用积分
    /// </summary>
    public long UsableIntegral { get; set; }
    /// <summary>
    /// 企业余额
    /// </summary>
    public decimal Balance { get; set; }
    /// <summary>
    /// 企业标签（对应大数据tags）
    /// </summary>
    public string? Tags { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }
    /// <summary>
    /// 时间戳
    /// </summary>
    //[Timestamp]
    //[JsonIgnore]
    public byte[]? TagTimeStamp { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    //[JsonIgnore]
    public int IsRemove { get; set; }


    //新版企业增加字段

    /// <summary>
    /// 对求职者展示名称
    /// </summary>
    public int? Show { get; set; }

    /// <summary>
    /// 企业成立时间
    /// </summary>
    public DateTime? RegistrationTime { get; set; }

    /// <summary>
    /// 官网
    /// </summary>
    public string? OfficialWebsite { get; set; }

    /// <summary>
    /// 企业扩展信息
    /// </summary>
    public string? ExtendedInfo { get; set; }

    /// <summary>
    /// 企业制度json
    /// </summary>
    public string? CorporateSystem { get; set; }

    /// <summary>
    /// 企业形象json
    /// </summary>
    public string? CorporateImage { get; set; }

    /// <summary>
    /// 团队信息json
    /// </summary>
    public string? TeamInfo { get; set; }

    /// <summary>
    /// 大数据更新日期
    /// </summary>
    public DateTime? BigDataTime { get; set; }

    /// <summary>
    /// 融资纪录
    /// </summary>
    public string? FinancingRecord { get; set; }
    /// <summary>
    /// 注册规模
    /// </summary>
    public int RegCapital { get; set; }
    /// <summary>
    /// 招聘中的岗位数量
    /// </summary>
    public int WorkPositionCount { get; set; }

    /// <summary>
    /// 白名单（0否，1是）
    /// </summary>
    public int Whitelist { get; set; }

}
