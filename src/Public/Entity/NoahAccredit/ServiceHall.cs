﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity;

public class ServiceHall
{
    /// <summary>
    /// 网点ID
    /// </summary>
    [Key]
    public string PK_SHID { get; set; } = default!;

    /// <summary>
    /// 网点应用场景类型
    /// *Noah.Enum.NoahAccredit.ServiceHallSceneType
    /// </summary>
    public int SceneType { get; set; }
    /// <summary>
    /// 网点名称
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 营业开始时间
    /// </summary>
    public string? BeginTime { get; set; }
    /// <summary>
    /// 营业截止时间
    /// </summary>
    public string? EndTime { get; set; }
    /// <summary>
    /// 联系方式
    /// </summary>
    public string? ContactInfo { get; set; }

    /// <summary>
    /// 所在省No
    /// </summary>
    public string? ProvinceNo { get; set; }
    /// <summary>
    /// 所在市No
    /// </summary>
    public string? CityNo { get; set; }
    /// <summary>
    /// 所在区No
    /// </summary>
    public string? DistrictNo { get; set; }

    /// <summary>
    /// 所在省Name
    /// </summary>
    public string? ProvinceName { get; set; }
    /// <summary>
    /// 所在市Name
    /// </summary>
    public string? CityName { get; set; }
    /// <summary>
    /// 所在区Name
    /// </summary>
    public string? DistrictName { get; set; }

    /// <summary>
    /// 所在地图经度
    /// </summary>
    [Column(TypeName = "numeric(18, 6)")]
    public decimal? Longitude { get; set; }
    /// <summary>
    /// 所在地图纬度
    /// </summary>
    [Column(TypeName = "numeric(18, 6)")]
    public decimal? Latitude { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 负责人
    /// </summary>
    public string? ChargePersonName { get; set; }
    /// <summary>
    /// 负责人联系方式
    /// </summary>
    public string? ChargePersonMobile { get; set; }

    /// <summary>
    /// 行业标签
    /// *List<int>的Json字符串
    /// *int=Noah.Enum.IndustryTag
    /// </summary>
    public string? IndustryTags { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 显示的Banner
    /// </summary>
    public string? Banners { get; set; }

    /// <summary>
    /// 状态 
    /// *Noah.Enum.UpDownStatus
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 因无需联合表查询，因此将顾问ID放到一个字段里，不在单独另开表
    /// *List<StaffingSimple>的JSON字符串
    /// </summary>
    public string? Staffings { get; set; }

    /// <summary>
    /// 关联顾问数量
    /// *更新Staffings时，更新此字段
    /// </summary>
    public int StaffingCount { get; set; }

    /// <summary>
    /// 邀请码
    /// </summary>
    public string? InviteCode { get; set; }

    /// <summary>
    /// 微信小程序的URL(为空的话，在保存时重建)
    /// </summary>
    public string? MiniProgramQRUrl { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int IsRemove { get; set; }

    /// <summary>
    /// 直播配置
    /// *存储的JSON字符串，List<Noah.SimpleTools.BusinessModel.ServiceHallLive>
    /// </summary>
    public string? Lives { get; set; }

    /// <summary>
    /// 网点的诺快聘管理员
    /// *因不知道怎么配置的，所以暂时先这样吧，如果有调整需更新NoahRecruit项目
    /// </summary>
    public string? StaffingAdviserID { get; set; }

    /// <summary>
    /// 定点位置
    /// *存储的JSON字符串，List<Noah.SimpleTools.BusinessModel.ServiceHallFixedPosition>
    /// </summary>
    public string? FixedPosition { get; set; }

    /// <summary>
    /// 定点位置附近企业的筛选距离
    /// </summary>
    public decimal FixedPositionDistance { get; set; }

    /// <summary>
    /// 金牌顾问
    /// *存储的JSON字符串，List<NoahAccredit.Models.Models.Dtos.ServiceHallGoldAdviser>
    /// </summary>
    public string? GoldAdviser { get; set; }


    /// <summary>
    /// 二屏是否开启
    /// </summary>
    public bool SecondScreenIsOpen { get; set; }

    /// <summary>
    /// 三屏是否开启
    /// </summary>
    public bool ThreeScreenIsOpen { get; set; }

    /// <summary>
    /// 三屏的展示方式：0 视频+文案 1 视频+诺快聘岗位
    /// </summary>
    public int ThreeScreenShowType { get; set; }

    /// <summary>
    /// 三屏的视频链接
    /// </summary>
    public string? ThreeScreenVideoUrl { get; set; }

    /// <summary>
    /// 三屏的文案
    /// </summary>
    public string? ThreeScreenContent { get; set; }

    /// <summary>
    /// 三屏的诺快聘岗位
    /// </summary>
    public string? ThreeScreenPosition { get; set; }

    /// <summary>
    /// 三屏显示的企业ID
    /// </summary>
    public string? ThreeScreenEntID { get; set; }

    /// <summary>
    /// 筛选岗位时，设置默认限制刷新时间条件
    /// </summary>
    public DateTime LimitWPRefreshTime { get; set; } = new DateTime(1999, 1, 1);


    /// <summary>
    /// 企业微信群二维码
    /// </summary>
    public string? WeChatGroupQRUrl { get; set; }

    /// <summary>
    /// 统计方式
    /// *0=无参时，企业数量取关联顾问的企业数(与EntStaffingAdviser.IsFixedForCount关联)；1=与检索到的岗位相关
    /// </summary>
    public int StatisticsType { get; set; }
}
