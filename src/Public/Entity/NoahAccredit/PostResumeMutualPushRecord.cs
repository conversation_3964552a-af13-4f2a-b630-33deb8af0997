﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity;

public class PostResumeMutualPushRecord
{
    /// <summary>
    /// 记录ID
    /// </summary>
    [Key]
    public string PK_PRMPRID { get; set; } = default!;
    /// <summary>
    /// 用户ID
    /// </summary>
    public string? PK_UID { get; set; }
    /// <summary>
    /// 岗位ID
    /// </summary>
    public string? PK_WPID { get; set; }
    /// <summary>
    /// HR账号ID
    /// </summary>
    public string? PK_EAID { get; set; }
    /// <summary>
    /// 匹配的求职意向ID
    /// </summary>
    public string? PK_PEID { get; set; }
    /// <summary>
    /// 匹配到几个条件：1~4
    /// </summary>
    public int? ParamType { get; set; }
    /// <summary>
    /// 目标账号类型：用户账号=1,企业账号=2
    /// *枚举：Noah.Enum.NoahAccredit.DisparkAppAppType
    /// </summary>
    public int? AccountType { get; set; }
    /// <summary>
    /// 简历完善度
    /// </summary>
    public int? ResumePerfect { get; set; }
    /// <summary>
    /// 求职者姓名
    /// </summary>
    public string? UserName { get; set; }
    /// <summary>
    /// 求职者账号
    /// </summary>
    public string? UserMobile { get; set; }
    /// <summary>
    /// HR姓名
    /// </summary>
    public string? HRName { get; set; }
    /// <summary>
    /// HR账号
    /// </summary>
    public string? HRMobile { get; set; }
    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? WorkPositionName { get; set; }
    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EntName { get; set; }
    /// <summary>
    /// 每日第？个推送的(每日推送多个时，因每4小时推送1次，因此记录下是第几个推送的)
    /// </summary>
    public int? DailyPushIndex { get; set; }
    /// <summary>
    /// 配置ID
    /// </summary>
    public string? PK_PRMPSRID { get; set; }
    /// <summary>
    /// 推送类型：1=自动推送，2=自定义推送简历，3=自定义推送岗位
    /// </summary>
    public int? PushType { get; set; }

    /// <summary>
    /// 定时OR立即推送：1=立即推送，2=定时推送
    /// </summary>
    public int? TimeType { get; set; }

    /// <summary>
    /// 自动推送时是否互推(true=互推了，false=单推的)
    /// </summary>
    public bool? IsMutualPush { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
}
