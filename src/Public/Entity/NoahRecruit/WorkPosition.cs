﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity;

public class WorkPosition
{
    /// <summary>
    /// 企业的ID
    /// </summary>
    [Key]
    public string PK_WPID { get; set; } = default!;
    /// <summary>
    /// 所属企业的ID
    /// </summary>
    public string? EnterpriseID { get; set; }
    /// <summary>
    /// 发布的账号ID
    /// </summary>
    public string? EnterpriseAccountID { get; set; }
    /// <summary>
    /// 企业账号信息
    /// </summary>
    public string? EnterpriseConsumer { get; set; }
    /// <summary>
    /// 岗位类别ID
    /// </summary>
    public string? PositionID { get; set; }
    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? WorkPositionName { get; set; }
    /// <summary>
    /// 经验要求
    /// </summary>
    public int? WorkTime { get; set; }
    /// <summary>
    /// 经验要求（验证内容）
    /// </summary>
    public string? WorkTimeVerify { get; set; }
    /// <summary>
    /// 学历要求（名称）
    /// </summary>
    public string? DegreeName { get; set; }
    /// <summary>
    /// 学历要求（级别）
    /// </summary>
    public int? DegreeLevel { get; set; }
    /// <summary>
    /// 学历要求（学历ID）
    /// </summary>
    public string? DegreeID { get; set; }
    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int? MinSalary { get; set; }
    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int? MaxSalary { get; set; }
    /// <summary>
    /// 薪资是否面议
    /// </summary>
    public int? SalaryType { get; set; }
    /// <summary>
    /// 工作地点省
    /// </summary>
    public string? ProvinceNo { get; set; }
    /// <summary>
    /// 工作地点市
    /// </summary>
    public string? CityNo { get; set; }
    /// <summary>
    /// 工作地点区
    /// </summary>
    public string? DistrictNo { get; set; }
    /// <summary>
    /// 工作地点省名称
    /// </summary>
    public string? ProvinceName { get; set; }
    /// <summary>
    /// 工作地点市名称
    /// </summary>
    public string? CityName { get; set; }
    /// <summary>
    /// 工作地点区名称
    /// </summary>
    public string? DistrictName { get; set; }
    /// <summary>
    /// 工作地点地址
    /// </summary>
    public string? Address { get; set; }
    /// <summary>
    /// 工作地点经度
    /// </summary>
    //[Column(TypeName = "numeric(18, 6)")]
    public decimal? WorkLng { get; set; }
    /// <summary>
    /// 工作地点纬度
    /// </summary>
    //[Column(TypeName = "numeric(18, 6)")]
    public decimal? WorkLat { get; set; }
    /// <summary>
    /// 工作性质
    /// </summary>
    public int? WorkNature { get; set; }
    /// <summary>
    /// 招聘人数
    /// </summary>
    public int? RecruitNumber { get; set; }
    /// <summary>
    /// 性别要求
    /// </summary>
    public int? WorkSex { get; set; }
    /// <summary>
    /// 岗位描述（包含要求和职责）
    /// </summary>
    public string? WorkDescription { get; set; }
    /// <summary>
    /// 浏览次数
    /// </summary>
    public int? BrowseNumber { get; set; }
    /// <summary>
    /// 投递次数
    /// </summary>
    public int? DeliverNumber { get; set; }
    /// <summary>
    /// 沟通次数
    /// </summary>
    public int? ConnectNumber { get; set; }
    /// <summary>
    /// 收藏次数
    /// </summary>
    public int? MarkerNumber { get; set; }
    /// <summary>
    /// 刷新时间
    /// </summary>
    public DateTime? RefreshTime { get; set; }
    /// <summary>
    /// 岗位状态
    /// </summary>
    public int? Status { get; set; }
    /// <summary>
    /// 岗位标签使用记录(未过期的)
    /// </summary>
    public string? Label { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 时间戳
    /// </summary>
    //[Timestamp]
    public byte[]? TagTimeStamp { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
    public string? Tags { get; set; }
    /// <summary>
    /// 薪资月数(15薪)
    /// </summary>
    public int? Salary { get; set; }

    //梁锴增加字段-2021-8-24

    /// <summary>
    /// 职位亮点
    /// </summary>
    public string? Benefit { get; set; }

    /// <summary>
    /// 招聘截止日期
    /// </summary>
    public DateTime? EndTime { get; set; }

    ///// <summary>
    ///// 发布到的渠道
    ///// </summary>
    //public string ChannelId { get; set; }

    /// <summary>
    /// 拓展字段json
    /// </summary>
    public string? PositionExtension { get; set; }

    /// <summary>
    /// 职位画像json
    /// </summary>
    public string? Portrait { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public int? AuditStatus { get; set; }

    /// <summary>
    /// 用人部门
    /// </summary>
    public string? DepartmentId { get; set; }

    /// <summary>
    /// 岗位编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 地址Id
    /// </summary>
    public string? PK_EOLID { get; set; }

    /// <summary>
    /// Source
    /// </summary>
    public int? Source { get; set; }

    /// <summary>
    /// 代招企业ID
    /// </summary>
    public string? PK_ROEID { get; set; }

    /// <summary>
    /// 所属代招公司的部门名称
    /// </summary>
    public string? RecruitOthersDepartment { get; set; }

}
