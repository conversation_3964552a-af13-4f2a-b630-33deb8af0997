using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Entity.ShuZiNY;

/// <summary>
/// 员工
/// </summary>
[Table("v_employee")]
public partial class VEmployee
{
    [Key]
    public long EmployeeId { get; set; }
    /// <summary>
    /// 人事编号
    /// </summary>
    public string? HrNo { get; set; }
    /// <summary>
    /// 工号
    /// </summary>
    public string? EmployeeCode { get; set; }
    /// <summary>
    /// 财务编码
    /// </summary>
    public string? FinanceNo { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }
    /// <summary>
    /// 手机
    /// </summary>
    public string? Phone { get; set; }
    /// <summary>
    /// 电话
    /// </summary>
    public string? Tel { get; set; }
    /// <summary>
    /// 分机号
    /// </summary>
    public string? BranchTel { get; set; }
    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }
    /// <summary>
    /// 禁用标志（0：正常；1：禁用; 2: 离职）
    /// </summary>
    public string? LockFlag { get; set; }
    /// <summary>
    /// 岗位（数据字典取值）
    /// </summary>
    public string? PositionNo { get; set; }
    /// <summary>
    /// 职级（数据字典取值）
    /// </summary>
    public string? GradeNo { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Note { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 删除标志：0：正常；1：删除
    /// </summary>
    public string? DelFlag { get; set; }
    /// <summary>
    /// 是否系统用户（0：是；1：否）
    /// </summary>
    public string? SysUserFlag { get; set; }
    /// <summary>
    /// 存储所有部门，以|分隔
    /// </summary>
    public string? DeptList { get; set; }
    /// <summary>
    /// 钉钉userid
    /// </summary>
    public string? DingdingUserId { get; set; }
    /// <summary>
    /// 用户ID
    /// </summary>
    public int? UserId { get; set; }
}