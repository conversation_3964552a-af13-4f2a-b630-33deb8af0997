﻿using Config;
using Config.Enums.Nkp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 虚拟人才库
/// </summary>
[Table("talent_virtual")]
public class Talent_Virtual
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// HrId
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 简历渠道
    /// </summary>
    public TalentVirtualChannel Channel { get; set; } = TalentVirtualChannel.ResumeUpload;

    /// <summary>
    /// 简历状态
    /// </summary>
    public TalentVirtualStatus Status { get; set; } = TalentVirtualStatus.UnRegistered;

    /// <summary>
    /// 求职者Id，建立关系后有值
    /// </summary>
    public string? SeekerId { get; set; } = string.Empty;

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 手机
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 头像
    /// </summary>
    public string HeadPortrait { get; set; } = string.Empty;

    /// <summary>
    /// 性别
    /// </summary>
    public Sex Sex { get; set; } = Sex.女;

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation Education { get; set; } = TalentVirtualEducation.Junior;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Mailbox { get; set; } = string.Empty;

    /// <summary>
    /// 微信
    /// </summary>
    public string WeChat { get; set; } = string.Empty;

    /// <summary>
    /// QQ
    /// </summary>
    public string QQ { get; set; } = string.Empty;

    /// <summary>
    /// 生日
    /// </summary>
    public DateTime Birthday { get; set; } = Constants.DefaultTime;

    /// <summary>
    /// 开始工作日期
    /// </summary>
    public DateTime WorkTime { get; set; } = Constants.DefaultTime;

    /// <summary>
    /// 简历完善度
    /// </summary>
    public int Perfection { get; set; } = 0;

    /// <summary>
    /// 自我评价
    /// </summary>
    public string SelfEvaluation { get; set; } = string.Empty;

    /// <summary>
    /// 技能分析
    /// </summary>
    public TalentVirtaualResumeSkillSub SkillAnalysis { get; set; } = new TalentVirtaualResumeSkillSub();

    /// <summary>
    /// 行业标签（来源小析职业标签字段）
    /// </summary>
    public List<string> IndustryLabel { get; set; } = new List<string>();

    /// <summary>
    /// 职位标签（来源小析职业标签字段）
    /// </summary>
    public List<string> PostLabel { get; set; } = new List<string>();

    /// <summary>
    /// 其他标签（来源小析职业标签字段）
    /// </summary>
    public List<string> OtherLabel { get; set; } = new List<string>();

    /// <summary>
    /// 亮点
    /// </summary>
    public TalentVirtaualResumeHighlights Highlights { get; set; } = new TalentVirtaualResumeHighlights();

    /// <summary>
    /// 风险
    /// </summary>
    public TalentVirtaualResumeRisks Risks { get; set; } = new TalentVirtaualResumeRisks();

    /// <summary>
    /// 原始简历地址
    /// </summary>
    public string OriginalUrl { get; set; } = string.Empty;

    /// <summary>
    /// 所在地
    /// </summary>
    public string Location { get; set; } = string.Empty;

    /// <summary>
    /// 所在地（标准地址）
    /// </summary>
    public string LocationNorm { get; set; } = string.Empty;

    /// <summary>
    /// 详细位置
    /// </summary>
    public string DetailedLocation { get; set; } = string.Empty;

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = default!;

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// 渠道Id
    /// </summary>
    public string? ChannelId { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public User_Hr User_Hr { get; set; } = default!;

    public User_Seeker User_Seeker { get; set; } = default!;

    public List<Talent_Virtual_Edu> Talent_Virtual_Edu { get; set; } = default!;

    public List<Talent_Virtual_Work> Talent_Virtual_Work { get; set; } = default!;

    public List<Talent_Virtual_Project> Talent_Virtual_Project { get; set; } = default!;

    public Talent_Virtual_Hope Talent_Virtual_Hope { get; set; } = default!;
    public List<Talent_Label> Talent_Label{ get; set; } = default!;
}

/// <summary>
/// 技能分析（详情子表）
/// </summary>
public class TalentVirtaualResumeSkillSub
{
    /// <summary>
    /// 专业技能集合
    /// </summary>
    public List<string>? Professional { get; set; }

    /// <summary>
    /// IT技能集合
    /// </summary>
    public List<string>? IT { get; set; }

    /// <summary>
    /// 商业技能集合
    /// </summary>
    public List<string>? Business { get; set; }
}

/// <summary>
/// 亮点
/// </summary>
public class TalentVirtaualResumeHighlights
{
    /// <summary>
    /// 工作经历亮点
    /// </summary>
    public List<string>? occupation { get; set; }

    /// <summary>
    /// 学习经历亮点
    /// </summary>
    public List<string>? education { get; set; }

    /// <summary>
    /// 项目经历亮点
    /// </summary>
    public List<string>? project { get; set; }

    /// <summary>
    /// 其他亮点
    /// </summary>
    public List<string>? others { get; set; }

    /// <summary>
    /// 亮点标签
    /// </summary>
    public List<string>? tags { get; set; }
}

/// <summary>
/// 风险
/// </summary>
public class TalentVirtaualResumeRisks
{
    /// <summary>
    /// 工作经历风险
    /// </summary>
    public List<string>? occupation { get; set; }

    /// <summary>
    /// 学习经历风险点
    /// </summary>
    public List<string>? education { get; set; }

    /// <summary>
    /// 风险点标签
    /// </summary>
    public List<string>? tags { get; set; }
}