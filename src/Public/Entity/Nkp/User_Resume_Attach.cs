﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 附件简历
/// </summary>
[Table("user_resume_attach")]
public class User_Resume_Attach
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    public string UserId { get; set; } = default!;

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 附件地址
    /// </summary>
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// 语言
    /// </summary>
    public ResumeLanguage Language { get; set; } = ResumeLanguage.中文;

    /// <summary>
    /// 附件大小
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Seeker User_Seeker { get; set; } = default!;
}
