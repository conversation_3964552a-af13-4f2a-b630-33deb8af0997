﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 招聘流程简历评论
/// </summary>
[Table("recruit_comment")]
public class Recruit_Comment
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 招聘流程id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 评论内容
    /// </summary>
    public string Content { get; set; } = default!;

    /// <summary>
    /// HrId
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// Hr姓名
    /// </summary>
    public string HrName { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Recruit Recruit { get; set; } = default!;

    public User_Hr User_Hr { get; set; } = default!;
}

