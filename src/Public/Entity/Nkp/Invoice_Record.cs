﻿using Config.Enums.Nkp;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Entity.Nkp
{
    /// <summary>
    /// 开发票记录表
    /// </summary>
    [Table("invoice_record")]
    public class Invoice_Record
    {
        [Key]
        public string Id { get; set; } = Entity.EntityTools.SnowflakeId();
        /// <summary>
        /// 应用场景
        /// </summary>
        public InvoiceRecordUseScence UseScence { get; set; }

        /// <summary>
        /// 凭证记录ID(UseScence=服务奖金发票时使用)
        /// </summary>
        public string VoucherId { get; set; } = default!;

        /// <summary>
        /// 用户id（提交人）
        /// </summary>
        public string UserId { get; set; } = default!;

        /// <summary>
        /// 提交人的姓名
        /// </summary>
        public string AgentHrName { get; set; } = default!;

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; } = default!;
        /// <summary>
        /// 开票商户id 
        /// </summary>
        public string? MerchantId { get; set; }

        /// <summary>
        /// 购方商户id 
        /// </summary>
        public string? BuyerMerchantId { get; set; }
        /// <summary>
        /// 合同编码
        /// </summary>
        [Required(ErrorMessage = "合同编码不能为空")]
        [JsonPropertyName("contractCode")]
        public string ContractCode { get; set; } = default!;

        /// <summary>
        /// 经办人
        /// </summary>
        [Required(ErrorMessage = "经办人不能为空")]
        [JsonPropertyName("agentHrNo")]
        public string AgentHrNo { get; set; } = default!;

        /// <summary>
        /// 开票金(单位：元)
        /// </summary>
        [JsonPropertyName("amount")]
        public decimal Amount { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit { get; set; } = default!;

        /// <summary>
        /// 开票类型
        /// </summary>
        [JsonPropertyName("invoiceType")]
        public NovaPinInvoiceType InvoiceType { get; set; }

        /// <summary>
        /// 购方税号
        /// </summary>
        [JsonPropertyName("buyertaxnum")]
        public string? BuyerTaxnum { get; set; }

        /// <summary>
        /// 购方名称
        /// </summary>
        [JsonPropertyName("buyername")]
        public string? BuyerName { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [JsonPropertyName("buyeraddress")]
        public string? BuyerAddress { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [JsonPropertyName("buyertel")]
        public string? BuyerTel { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        [JsonPropertyName("buyeraccountname")]
        public string? BuyerAccountName { get; set; }

        /// <summary>
        /// 开户行账号
        /// </summary>
        [JsonPropertyName("buyeraccount")]
        public string? BuyerAccount { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        [JsonPropertyName("invoicename")]
        public string? InvoiceName { get; set; }

        /// <summary>
        /// 商品税收分类编码
        /// </summary>
        [JsonPropertyName("goodscode")]
        public string? GoodsCode { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [JsonPropertyName("vatrate")]
        public string? VatRate { get; set; }

        /// <summary>
        /// 含税标志
        /// </summary>
        [JsonPropertyName("withtaxflag")]
        public string? WithTaxFlag { get; set; }

        /// <summary>
        /// 优惠政策标识
        /// </summary>
        [JsonPropertyName("favouredpolicyflag")]
        public string? FavouredpolicyFlag { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [JsonPropertyName("spectype")]
        public string? SpecType { get; set; }

        /// <summary>
        /// 推送方式
        /// </summary>
        [JsonPropertyName("pushmode")]
        public NovaPinInvoicePushMode PushMode { get; set; }

        /// <summary>
        /// 推送手机
        /// </summary>
        [JsonPropertyName("buyerphone")]
        public string? BuyerPhone { get; set; }

        /// <summary>
        /// 推送邮箱
        /// </summary>
        [JsonPropertyName("email")]
        public string? Email { get; set; }

        /// <summary>
        /// 是否显示开户行及账号
        /// </summary>
        [JsonPropertyName("showbankaccounttype")]
        public NovaPinInvoiceShowBankAccountType ShowBankAccountType { get; set; }
        /// <summary>
        /// 是否显示地址及电话
        /// </summary>
        [JsonPropertyName("showaddressteltype")]
        public NovaPinInvoiceShowAddressTelType ShowAddressTelType { get; set; }
        /// <summary>
        /// 是否展示收款和复核人
        /// </summary>
        [JsonPropertyName("showcheckertype")]
        public NovaPinInvoiceShowCheckerType ShowCheckerType { get; set; }

        /// <summary>
        /// 发票备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string? Remark { get; set; }

        [Column("CreateTime")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        [Column("UpdateTime")]
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 状态
        /// </summary>
        public InvoiceRecordStatus Status { get; set; } = InvoiceRecordStatus.已申请;


        /// <summary>
        /// 开具发票后，返回的发票PDF文件链接
        /// </summary>
        public string InvoicePDFUrl { get; set; } = string.Empty;

        /// <summary>
        /// 数字诺亚审核后回调时传的Remark
        /// </summary>
        public string CallBackRemark { get; set; } = string.Empty;

        /// <summary>
        /// 本方公司名称(开票企业)
        /// </summary>
        public string IssuingCompany { get; set; } = string.Empty;

        public List<Invoice_Detail>? Invoice_Detail { get; set; }

        /// <summary>
        /// 关联的凭证信息【服务奖金发票】
        /// </summary>
        public Voucher? Voucher { get; set; }
    }
}
