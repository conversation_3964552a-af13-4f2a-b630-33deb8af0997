﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// 合同文件
/// </summary>
[Table("contract_file")]
public class Contract_File
{
    [Key]
    public string FileId { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 合同Id
    /// </summary>
    public string ContractId { get; set; } = null!;

    /// <summary>
    /// 模板Id
    /// </summary>
    public string TemplateId { get; set; } = null!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// E签宝文件Id
    /// </summary>
    public string EFileId { get; set; } = null!;
}