﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 微信信息
/// </summary>
[Table("wechat")]
public class WeChat
{
    [Key]
    public string WeChatId { get; set; } = default!;

    /// <summary>
    /// 公众号OpenId
    /// </summary>
    public string? WeChatH5OpenId { get; set; }

    /// <summary>
    /// 是否订阅公众号
    /// </summary>
    public bool WeChatH5Subscribe { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}