﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.CommonModel;

using Config.Enums.Nkp;
using NetTopologySuite.Geometries;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// 职位表
/// </summary>
[Table("post")]
public class Post
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string PostId { get; set; } = EntityTools.SnowflakeId();

    public int AutoId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 职位类别
    /// </summary>
    public int Category { get; set; }

    /// <summary>
    /// 工作性质
    /// </summary>
    public PostWorkNature WorkNature { get; set; } = PostWorkNature.全职;

    /// <summary>
    /// 薪资类型
    /// </summary>
    public PostSalaryType SalaryType { get; set; } = PostSalaryType.面议;

    // /// <summary>
    // /// 招聘人数
    // /// </summary>
    // public int RecruitNumber { get; set; }

    /// <summary>
    /// 交付数量
    /// </summary>
    [ConcurrencyCheck]
    public int DeliveryNumber { get; set; }

    /// <summary>
    /// 剩余库存
    /// </summary>
    [ConcurrencyCheck]
    public int LeftStock { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public PostStatus Status { get; set; }

    // /// <summary>
    // /// 是否协同
    // /// </summary>
    // public bool Share { get; set; }

    /// <summary>
    /// 招聘企业Id
    /// </summary>
    public string? AgentEntId { get; set; }

    /// <summary>
    /// 销售是否需要分佣
    /// </summary>
    public bool IsSalesCommission { get; set; }

    /// <summary>
    /// 签约方式
    /// </summary>
    public ContractType? ContractType { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = default!;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 招聘企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 静态地图
    /// </summary>
    public string? LocationMap { get; set; }

    /// <summary>
    /// 坐标
    /// </summary>
    public Point Location { get; set; } = new Point(0, 0);

    /// <summary>
    /// 部门
    /// </summary>
    public string Department { get; set; } = default!;

    /// <summary>
    /// 教育
    /// </summary>
    public EducationType Education { get; set; } = EducationType.高中;

    /// <summary>
    /// 薪资要求(最低)
    /// </summary>
    public int MinSalary { get; set; }

    /// <summary>
    /// 薪资要求(最高)
    /// </summary>
    public int MaxSalary { get; set; }

    /// <summary>
    /// 几薪
    /// </summary>
    public int Salary { get; set; } = 12;

    /// <summary>
    /// 简历是否免审
    /// </summary>
    public bool IsResumeExempt { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 入职过保天数
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 结算方式（日结、月结）
    /// </summary>
    public PostSettlementType? SettlementType { get; set; }

    /// <summary>
    /// 工作日(1-7)
    /// </summary>
    public List<int> WorkingDays { get; set; } = new List<int>();

    /// <summary>
    /// 工作时间
    /// </summary>
    public List<PostWorkingHours> WorkingHours { get; set; } = new List<PostWorkingHours>();

    //实习生

    /// <summary>
    /// 最少实习月数
    /// </summary>
    public int? MinMonths { get; set; }

    /// <summary>
    /// 最少周到岗天数
    /// </summary>
    public int? DaysPerWeek { get; set; }

    //应届

    /// <summary>
    /// 毕业年份
    /// </summary>
    public int? GraduationYear { get; set; }

    /// <summary>
    /// 金额/人
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string> Tags { get; set; } = new List<string>();

    /// <summary>
    /// 职位亮点
    /// </summary>
    public List<string> Highlights { get; set; } = new List<string>();

    private List<WelfareModel> _welfare = new List<WelfareModel>();
    /// <summary>
    /// 福利
    /// </summary>
    public List<WelfareModel> Welfare
    {
        get { return _welfare; }
        set
        {
            _welfare = value;
            WelfareSearch = $".{string.Join('.', Welfare?.Select(s => s.Id)?.ToList() ?? new List<int>())}.";
        }
    }

    /// <summary>
    /// 福利自定义
    /// </summary>
    public List<string> WelfareCustom { get; set; } = new List<string>();

    /// <summary>
    /// 福利检索
    /// </summary>
    // private string _welfareSearch = string.Empty;
    public string WelfareSearch { get; set; } = string.Empty;

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 最小年龄
    /// </summary>
    public int MinAge { get; set; }

    /// <summary>
    /// 最大年龄
    /// </summary>
    public int MaxAge { get; set; }

    public bool Deleted { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 职位分数
    /// </summary>
    public int Score { get; set; } = 100;

    /// <summary>
    /// 审批驳回原因
    /// </summary>
    public string? RejectReason { get; set; }

    /// <summary>
    /// 审批人userid
    /// </summary>
    public string? AuditUserId { get; set; }

    /// <summary>
    /// 审批时间
    /// </summary>
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 领带类型
    /// </summary>
    public TieType TieType { get; set; } = TieType.蓝领;


    public Project Project { get; set; } = default!;
    public Post_Extend Post_Extend { get; set; } = default!;
    public Dic_Post Dic_Post { get; set; } = default!;
    public Dic_Region Dic_Region { get; set; } = default!;
}