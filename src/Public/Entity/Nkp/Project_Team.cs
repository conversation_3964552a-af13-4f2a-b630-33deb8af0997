﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// hr项目
/// </summary>
[Table("project_team")]
public class Project_Team
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string TeamProjectId { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 用户Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 原始项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 零工市场代填hrid
    /// </summary>
    public string? AgentHrId { get; set; }

    /// <summary>
    /// 类别
    /// </summary>
    public HrProjectType Type { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public HrProjectSource Source { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ProjectStatus Status { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Project Project { get; set; } = default!;
    public List<Post_Team> Post_Team { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
}