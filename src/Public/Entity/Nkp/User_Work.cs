﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 工作经历
/// </summary>
[Table("user_work")]
public class User_Work
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 公司
    /// </summary>
    public string Company { get; set; } = string.Empty;

    /// <summary>
    /// 职位
    /// </summary>
    public string Post { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateOnly BeginDate { get; set; } = default;

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateOnly EndDate { get; set; } = default;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }


    public User_Resume User_Resume { get; set; } = default!;
}
