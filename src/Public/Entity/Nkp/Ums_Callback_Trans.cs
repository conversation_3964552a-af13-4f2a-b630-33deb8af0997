﻿using Config.Enums.Nkp;
using Nest;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// 动账回调记录
/// </summary>
[Table("ums_callback_trans")]
public class Ums_Callback_Trans
{
    /// <summary>
    /// 外部流水号
    /// </summary>
    [Key]
    public string AppSsn { get; set; } = default!;

    /// <summary>   
    /// 应用记账日期:yyyymmdd
    /// </summary>
    public string AppAcctDate { get; set; } = default!;

    /// <summary>
    /// 请求日期:yyyymmdd
    /// </summary>
    public string ReqDate { get; set; } = default!;
    /// <summary>
    /// 请求时间:HHmmss 
    /// </summary>
    public string ReqTime { get; set; } = default!;
    /// <summary>
    /// 交易流水号
    /// </summary>
    public string TransSsn { get; set; } = default!;
    /// <summary>
    /// 账户记账日期:yyyymmdd 
    /// </summary>
    public string AcctDate { get; set; } = default!;
    /// <summary>
    /// 交易日期:yyyymmdd 
    /// </summary>
    public string TransDate { get; set; } = default!;
    /// <summary>
    /// 交易时间:HHmmss 
    /// </summary>
    public string TransTime { get; set; } = default!;
    /// <summary>
    /// 出金账户号
    /// </summary>
    public string OutAcctNo { get; set; } = default!;
    /// <summary>
    /// 入金账户号
    /// </summary>
    public string InAcctNo { get; set; } = default!;
    /// <summary>
    /// 交易金额，单位：分 
    /// </summary>
    public string TransAmt { get; set; } = default!;
    /// <summary>
    /// 平台手续费，单位：分 
    /// </summary>
    public string PltfmFee { get; set; } = default!;
    /// <summary>
    /// 机构手续费，单位：分 
    /// </summary>
    public string InstFee { get; set; } = default!;
    /// <summary>
    /// 银商手续费，单位：分
    /// </summary>
    public string Fee { get; set; } = default!;
    /// <summary>
    /// 交易附言
    /// </summary>
    public string? TransNote { get; set; }
    /// <summary>
    /// 交易类型，201636 单位支付账户上账
    /// </summary>
    public string? TransType { get; set; }
}
