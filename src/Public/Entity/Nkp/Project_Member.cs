﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// 项目成员
/// </summary>
[Table("project_member")]
public class Project_Member
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 证件类型
    /// </summary>
    public IdCardType IdentityCardType { get; set; } = IdCardType.身份证;

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdentityCard { get; set; } = string.Empty;

    /// <summary>
    /// 身份证姓名
    /// </summary>
    public string IdentityCardName { get; set; } = string.Empty;

    /// <summary>
    /// 用户Id
    /// </summary>
    public string? UserId { get; set; } = string.Empty;

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ConcurrencyCheck]
    public ProjectMemberStatus Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public ProjectMemberSource Source { get; set; }

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 用工形式
    /// </summary>
    public ProjectMemberEmploymentMode EmploymentMode { get; set; }

    /// <summary>
    /// 入职次数
    /// </summary>
    public int InductionTimes { get; set; }

    /// <summary>
    /// 试用期（月）
    /// </summary>
    public int ProbationMonth { get; set; }

    /// <summary>
    /// 合同数量
    /// </summary>
    public int ContractNum { get; set; }

    /// <summary>
    /// 应聘职位
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 入职部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 入职时间
    /// </summary>
    public DateOnly? EntryTime { get; set; }

    /// <summary>
    /// 离职时间
    /// </summary>
    public DateOnly? QuitTime { get; set; }

    /// <summary>
    /// 审批通过时间
    /// </summary>
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 离职状态
    /// </summary>
    [ConcurrencyCheck]
    public ProjectMemberQuitStatus QuitStatus { get; set; } = ProjectMemberQuitStatus.未离职;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EMail { get; set; }

    /// <summary>
    /// 描述（预留字段）
    /// </summary>
    public string? Describe { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Project Project { get; set; } = default!;
}
