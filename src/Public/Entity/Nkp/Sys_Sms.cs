﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 短信表
/// </summary>
[Table("sys_sms")]
public class Sys_Sms
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 短信类型
    /// </summary>
    public SMSType Type { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 可用
    /// </summary>
    public bool Active { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}