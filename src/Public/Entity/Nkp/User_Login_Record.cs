﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 用户登录记录
/// </summary>
[Table("user_login_record")]
public class User_Login_Record
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 类型
    /// </summary>
    public ClientType Type { get; set; }

    /// <summary>
    /// 登录客户端
    /// </summary>
    public string Client { get; set; } = string.Empty;

    /// <summary>
    /// 登录Ip
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// 登录时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

// public enum LoginType
// {
//     <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Interviewer
// }