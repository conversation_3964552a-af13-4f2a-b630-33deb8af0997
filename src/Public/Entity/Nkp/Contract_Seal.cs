﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// E签宝印章表
/// </summary>
[Table("contract_seal")]
public class Contract_Seal
{
    [Key]
    public string SealId { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 机构Id
    /// </summary>
    public string OrgId { get; set; } = null!;

    /// <summary>
    /// 印章名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 印章类型
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 印章宽度
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// 印章高度
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// 印章地址
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 是否默认印章
    /// </summary>
    public ulong Default { get; set; }

    /// <summary>
    /// E签宝印章ID
    /// </summary>
    public string EsealId { get; set; } = null!;

    /// <summary>
    /// E签宝印章fileKey
    /// </summary>
    public string EfileKey { get; set; } = null!;

    /// <summary>
    /// 删除
    /// </summary>
    public bool Deleted { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatedBy { get; set; } = null!;

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedBy { get; set; } = null!;

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
