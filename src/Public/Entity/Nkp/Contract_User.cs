﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// 合同个人账号表
/// </summary>
[Table("contract_user")]
public class Contract_User
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 身份证号
    /// </summary>
    public string IdNumber { get; set; } = null!;

    /// <summary>
    /// 身份证姓名
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 个人账号id
    /// </summary>
    public string AccountId { get; set; } = null!;

    /// <summary>
    /// 账号的唯一标识
    /// </summary>
    public string ThirdPartyUserId { get; set; } = null!;

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
