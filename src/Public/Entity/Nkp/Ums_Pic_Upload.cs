using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;
[Table("ums_pic_upload")]
public class Ums_Pic_Upload
{
    [Key]
    public string Id { get; set; } = Entity.EntityTools.SnowflakeId();
    
    public string? Base64 { get; set; }
    
    public string UserId { get; set; } = default!;

    public string EntId { get; set; } = default!;

    public string? EntName { get; set; }
    
    public string? Size { get; set; }

    public string? Path { get; set; }

    public string? Name { get; set; }

    public string? Type { get; set; }

    public DateTime? CreatedTime { get; set; } = DateTime.Now;
    
    public DateTime? UpdatedTime { get; set; } = DateTime.Now;

}