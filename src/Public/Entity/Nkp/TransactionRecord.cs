using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// 动账记录表实体
/// </summary>
[Table("settlement_transaction_record")]
public class TransactionRecord
{
    [Key]
    public string Id { get; set; } = Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 订单号（商户订单号）
    /// </summary>
    [Required]
    [StringLength(64)]
    public string OrderNo { get; set; } = default!;

    /// <summary>
    /// 外部流水号（第三方接口的AppSsn）
    /// </summary>
    [StringLength(64)]
    public string? AppSsn { get; set; }

    /// <summary>
    /// 交易流水号（第三方接口返回的TransSsn）
    /// </summary>
    [StringLength(64)]
    public string? TransSsn { get; set; }

    /// <summary>
    /// 出金账户号
    /// </summary>
    [Required]
    [StringLength(64)]
    public string? FromAccountNo { get; set; }

    /// <summary>
    /// 入金账户号
    /// </summary>
    [Required]
    [StringLength(64)]
    public string ToAccountNo { get; set; } = default!;

    /// <summary>
    /// 交易金额（单位：分）
    /// </summary>
    [Required]
    public long TransAmt { get; set; }

    /// <summary>
    /// 平台方收取的手续费（单位：分）
    /// </summary>
    public long PltfmFee { get; set; } = 0;

    /// <summary>
    /// 机构收取的手续费（单位：分）
    /// </summary>
    public long InstFee { get; set; } = 0;

    /// <summary>
    /// 交易类型（内部、外部、个人、平台）
    /// </summary>
    [Required]
    public int TransType { get; set; }

    /// <summary>
    /// 交易状态（0：失败，1：成功，2：处理中）
    /// </summary>
    [Required]
    [StringLength(1)]
    public string TransStatus { get; set; } = "2"; // 默认处理中

    /// <summary>
    /// 交易方式（OnePay：直接转账，TransferPay：通过诺聘平台中转）
    /// </summary>
    [Required]
    [StringLength(20)]
    public string TransMethod { get; set; } = default!;

    /// <summary>
    /// API类型（WithholdPay：子账户转账支付，PayAccountPay：单位支付账户转账， SubAccountRecharge：子账户充值）
    /// </summary>
    [StringLength(20)]
    public string? ApiType { get; set; }

    /// <summary>
    /// 应用记账日期（yyyyMMdd）
    /// </summary>
    [StringLength(8)]
    public string? AppAcctDate { get; set; }

    /// <summary>
    /// 账户记账日期（yyyyMMdd）
    /// </summary>
    [StringLength(8)]
    public string? AcctDate { get; set; }

    /// <summary>
    /// 交易日期（yyyyMMdd）
    /// </summary>
    [StringLength(8)]
    public string? TransDate { get; set; }

    /// <summary>
    /// 交易时间（HHmmss）
    /// </summary>
    [StringLength(6)]
    public string? TransTime { get; set; }

    /// <summary>
    /// 错误码
    /// </summary>
    [StringLength(32)]
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [StringLength(512)]
    public string? ErrorInfo { get; set; }

    /// <summary>
    /// 第三方接口响应信息（JSON格式）
    /// </summary>
    [Column(TypeName = "text")]
    public string? ApiResponse { get; set; }

    /// <summary>
    /// 交易备注
    /// </summary>
    [StringLength(512)]
    public string? TransNote { get; set; }

    /// <summary>
    /// 入金单位ID
    /// </summary>
    [StringLength(64)]
    public string? InEntId { get; set; }

    /// <summary>
    /// 入金单位名称
    /// </summary>
    [StringLength(256)]
    public string? InEntName { get; set; }

    /// <summary>
    /// 余额（第三方接口返回的账户余额）
    /// </summary>
    [StringLength(32)]
    public string? BalanceAmt { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;
}
