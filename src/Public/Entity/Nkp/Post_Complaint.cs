﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// 职位投诉
/// </summary>
[Table("post_complaint")]
public class Post_Complaint
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 投递Id
    /// </summary>
    public string DeliveryId { get; set; } = string.Empty;

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string SeekerId { get; set; } = string.Empty;

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string TeamPostId { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Describe { get; set; } = string.Empty;

    /// <summary>
    /// 投诉类型
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}