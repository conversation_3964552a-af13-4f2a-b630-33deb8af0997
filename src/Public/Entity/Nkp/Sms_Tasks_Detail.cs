﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 群发短信任务详情
/// </summary>
[Table("sms_tasks_detail")]
public class Sms_Tasks_Detail
{
    [Key]
    public int Id { get; set; }

    public int TaskId { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 短信模板编码
    /// </summary>
    public string TempCode { get; set; } = string.Empty;

    /// <summary>
    /// json内容数据
    /// </summary>
    public string JsonData { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public SmsTasksDetailStatus Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime? SendTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Sms_Tasks Sms_Tasks { get; set; } = default!;
}

public enum SmsTasksDetailStatus
{
    待处理, 成功 = 1, 失败 = 9
}