﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 校园经历
/// </summary>
[Table("user_campus")]
public class User_Campus
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 职位/奖项
    /// </summary>
    public string Award { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateOnly BeginDate { get; set; } = default;

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateOnly EndDate { get; set; } = default;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }


    public User_Resume User_Resume { get; set; } = default!;
}
