﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 项目扩展表
/// </summary>
[Table("project_extend")]
public class Project_Extend
{
    /// <summary>
    /// 项目Id
    /// </summary>
    [Key]
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 协同次数
    /// </summary>
    public int SyncNum { get; set; }

    /// <summary>
    /// 报名人数
    /// </summary>
    public int RegistrationNum { get; set; }

    /// <summary>
    /// 交付人数
    /// </summary>
    public int DeliveriesNum { get; set; }
}