﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// 合同模板组件
/// </summary>
[Table("contract_doctemplate_component")]
public class Contract_Doctemplate_Component
{
    [Key]
    public string TemplateId { get; set; } = null!;

    /// <summary>
    /// 系统组件
    /// </summary>
    public string SystemComponents { get; set; } = null!;

    /// <summary>
    /// 自定义组件
    /// </summary>
    public string CustomComponents { get; set; } = null!;
}
