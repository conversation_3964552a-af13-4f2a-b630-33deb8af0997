﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 关注的项目行业
/// </summary>
[Table("user_follow_industry")]
public class User_Follow_Industry
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 行业Id
    /// </summary>
    public ProjectIndustry Industry { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Hr User_Hr { get; set; } = default!;
    public User_Seeker User_Seeker { get; set; } = default!;
}