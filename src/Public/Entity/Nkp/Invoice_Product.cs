﻿using Config.Enums.Nkp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp
{
    /// <summary>
    /// 发票商品税收配置表
    /// </summary>
    [Table("invoice_product")]
    public class Invoice_Product
    {
        [Key]
        public string Id { get; set; } = Entity.EntityTools.SnowflakeId();
        /// <summary>
        /// 商户ID(UseScence=服务奖金发票时使用)
        /// </summary>
        public string MerchantId { get; set; } = default!;
        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; } = default!;
        /// <summary>
        /// 商品分类名称
        /// </summary>
        public string ProductTypeName { get; set; } = default!;
        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }
        /// <summary>
        /// 含税标识(1=含税，0=不含税)
        /// </summary>
        public InvoiceProductIsIncludedTax IsIncludedTax { get; set; }
        /// <summary>
        /// 是否享受优惠政策(1=享受，0=不享受)
        /// </summary>
        public InvoiceProductIsEnjoyPreferential IsEnjoyPreferential { get; set; }
        /// <summary>
        /// 优惠政策编码
        /// *存储时需从字典Config.Constants.InvoiceProduct_PreferentialDic中根据 政策名称 获取
        /// </summary>
        public string PreferentialCode { get; set; } = default!;
        /// <summary>
        /// 优惠政策名称
        /// </summary>
        public string PreferentialName { get; set; } = default!;
        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string TaxTypeCode { get; set; } = default!;
        /// <summary>
        /// 税收分类名称
        /// </summary>
        public string TaxTypeName { get; set; } = default!;
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}
