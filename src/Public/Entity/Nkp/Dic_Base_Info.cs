﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 职位字典
/// </summary>
[Table("dic_base_info")]
public class Dic_Base_Info
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// Code
    /// </summary>
    public string Code { get; set; } = default!;

    /// <summary>
    /// Value
    /// </summary>
    public string Value { get; set; } = default!;

    /// <summary>
    /// 类别
    /// </summary>
    public string Type { get; set; } = default!;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    public DateTime? CreatedTime { get; set; } = DateTime.Now;
    public DateTime? UpdatedTime { get; set; } = DateTime.Now;
    public string? CreatedUser { get; set; }
    public string? UpdatedUser { get; set; }
    public int Deleted { get; set; } = 0;

}