using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp
{
    /// <summary>
    /// 服务奖金发放记录表
    /// </summary>
    [Table("settlement_service_bonus_record")]
    public class Service_Bonus_Record
    {
        [Key]
        public string Id { get; set; } = Entity.EntityTools.SnowflakeId();

        /// <summary>
        /// 订单号（来自转账请求）
        /// </summary>
        public string OrderNumber { get; set; } = default!;
        
        /// <summary>
        /// 商户id 
        /// </summary>
        public string? MerchantId { get; set; }
        
        /// <summary>
        /// 合同编号
        /// </summary>
        public string ContractCode { get; set; } = default!;

        /// <summary>
        /// 经办人钉钉工号
        /// </summary>
        public string AgentHrNo { get; set; } = default!;

        /// <summary>
        /// 受益人钉钉工号
        /// </summary>
        public string BeneficiaryHrNo { get; set; } = default!;

        /// <summary>
        /// 发放金额（单位：分）
        /// </summary>
        public long IssuedAmount { get; set; }

        /// <summary>
        /// 说明备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 发起状态
        /// </summary>
        public ServiceBonusStatus Status { get; set; } = ServiceBonusStatus.待发起;

        /// <summary>
        /// 第三方系统响应码
        /// </summary>
        public int? ResponseCode { get; set; }

        /// <summary>
        /// 第三方系统响应消息
        /// </summary>
        public string? ResponseMessage { get; set; }

        /// <summary>
        /// 第三方系统返回的数据
        /// </summary>
        public string? ResponseData { get; set; }

        /// <summary>
        /// 发起时间
        /// </summary>
        public DateTime? InitiatedTime { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletedTime { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 最后错误信息
        /// </summary>
        public string? LastErrorMessage { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string? UpdatedBy { get; set; }
    }
}