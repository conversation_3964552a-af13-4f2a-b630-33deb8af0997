﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 提现
/// </summary>
[Table("user_withdraw")]
public class User_Withdraw
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    public string UserId { get; set; } = null!;

    /// <summary>
    /// hr还是求职者
    /// </summary>
    public SeekerOrHr UserType { get; set; }

    /// <summary>
    /// 提现类型
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ConcurrencyCheck]
    public WithdrawStatus Status { get; set; }

    /// <summary>
    /// 数值
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 提现账户
    /// </summary>
    public string? Account { get; set; }

    /// <summary>
    /// 提现账户名称
    /// </summary>
    public string? AccountName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    public string? Approver { get; set; }

    /// <summary>
    /// 审批通过时间
    /// </summary>
    public DateTime? ApprovalTime { get; set; }

    /// <summary>
    /// 到账时间
    /// </summary>
    public DateTime? WithdrawTime { get; set; }

    /// <summary>
    /// 提现时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Seeker User_Seeker { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
    public User User { get; set; } = default!;
}