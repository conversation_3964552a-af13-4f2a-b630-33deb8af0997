﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 简历表
/// </summary>
[Table("user_resume")]
public class User_Resume
{
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 职业
    /// </summary>
    public OccupationType? Occupation { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 学校
    /// </summary>
    public string? School { get; set; }

    /// <summary>
    /// 毕业日期
    /// </summary>
    public DateOnly? GraduationDate { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? Major { get; set; }

    /// <summary>
    /// 简历完善度
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 性格
    /// </summary>
    public List<string> Nature { get; set; } = new List<string>();

    /// <summary>
    /// 技能
    /// </summary>
    public List<string> Skill { get; set; } = new List<string>();

    /// <summary>
    /// 外貌
    /// </summary>
    public List<string> Appearance { get; set; } = new List<string>();

    /// <summary>
    /// 生日
    /// </summary>
    public DateOnly? Birthday { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EMail { get; set; }

    /// <summary>
    /// 微信号
    /// </summary>
    public string? WeChatNo { get; set; }

    /// <summary>
    /// qq号
    /// </summary>
    public string? Qq { get; set; }

    /// <summary>
    /// 显示
    /// </summary>
    public bool Show { get; set; }

    /// <summary>
    /// 匿名（暂不用）
    /// </summary>
    public bool Anonymous { get; set; }

    /// <summary>
    /// 证书
    /// </summary>
    public List<string> Certificate { get; set; } = new List<string>();

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;


    public User User { get; set; } = default!;
    public User_Seeker User_Seeker { get; set; } = default!;
    public List<User_Campus> User_Campus { get; internal set; } = default!;
    public List<User_Work> User_Work { get; internal set; } = default!;
}
