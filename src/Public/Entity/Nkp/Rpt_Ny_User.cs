﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 诺亚人员
/// </summary>
[Table("rpt_ny_user")]
public class Rpt_Ny_User
{
    [Key]
    public string 员工UserID { get; set; } = default!;

    public string? 姓名 { get; set; }
    public string? 工号 { get; set; }

    [Column("1级部门")]
    public string? 部门1 { get; set; }

    [Column("2级部门")]
    public string? 部门2 { get; set; }

    [Column("3级部门")]
    public string? 部门3 { get; set; }

    [Column("4级部门")]
    public string? 部门4 { get; set; }

    [Column("5级部门")]
    public string? 部门5 { get; set; }

    [Column("6级部门")]
    public string? 部门6 { get; set; }

    [Column("7级部门")]
    public string? 部门7 { get; set; }

    public string? 手机号 { get; set; }
    public string? 职位 { get; set; }
    public string? 岗位序列 { get; set; }
    public string? 直属主管 { get; set; }
    public string? 直属主管id { get; set; }
    public string? 人员账套 { get; set; }
}
