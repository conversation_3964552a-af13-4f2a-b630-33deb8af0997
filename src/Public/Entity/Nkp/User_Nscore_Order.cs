﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 诺积分
/// </summary>
[Table("user_nscore_order")]
public class User_Nscore_Order
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    public string GoodsId { get; set; } = null!;

    public string UserId { get; set; } = null!;

    /// <summary>
    /// 类型（预留）
    /// </summary>
    public int Type { get; set; }

    [ConcurrencyCheck]
    public NScoreOrderStatus Status { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 所需积分
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 商品价值
    /// </summary>
    public decimal Money { get; set; }

    public KdAddressInfo Address { get; set; } = new KdAddressInfo();

    /// <summary>
    /// 发货时间
    /// </summary>
    public DateTime? DeliveryTime { get; set; }

    public string Express { get; set; } = string.Empty;

    public string ExpressNo { get; set; } = string.Empty;

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedBy { get; set; } = string.Empty;

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Seeker User_Seeker { get; set; } = default!;
    public User_Nscore_Goods User_Nscore_Goods { get; set; } = default!;
}

public enum NScoreOrderStatus
{
    待发货, 已发货
}