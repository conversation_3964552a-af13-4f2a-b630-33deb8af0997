﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 组织架构人员
/// </summary>
[Table("rpt_szny_project")]
public class Rpt_Szny_Project
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 员工工号
    /// </summary>
    public string JobNumber { get; set; } = default!;

    /// <summary>
    /// 项目编码
    /// </summary>
    public string XM_CODE { get; set; } = default!;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string XM_NAME { get; set; } = default!;

    /// <summary>
    /// 产品编码
    /// </summary>
    public string XM_PRODUCTID { get; set; } = string.Empty;

    /// <summary>
    /// 产品名称
    /// </summary>
    public string XM_PRODUCTNAME { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime XM_SETDATE { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime XM_BEGTIME { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime XM_ENDTIME { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
