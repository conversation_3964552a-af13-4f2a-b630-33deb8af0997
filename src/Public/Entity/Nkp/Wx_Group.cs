﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Entity.Nkp;

/// <summary>
/// 微信群
/// </summary>
[Table("wx_group")]
public class Wx_Group
{
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// 群类型
    /// </summary>
    public GroupType GroupType { get; set; } = default!;

    /// <summary>
    /// 群名称
    /// </summary>
    public string GroupName { get; set; } = default!;

    /// <summary>
    /// 群描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 群人数
    /// </summary>
    public int GroupNumber { get; set; }

    /// <summary>
    /// 检测时间戳
    /// </summary>
    public long FindTime { get; set; }

    /// <summary>
    /// 二维码url
    /// </summary>
    public string? QRCode { get; set; }

    public DateTime? CreatedTime { get; set; } = DateTime.Now;
    public DateTime? UpdatedTime { get; set; } = DateTime.Now;

}

public enum GroupType
{
    微信群 = 1,
    企业微信群,
    其他 = 9
}

