﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 顾问渠道关系表
/// </summary>
[Table("qd_hr_channel")]
public class Qd_Hr_Channel
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 渠道用户Id
    /// </summary>
    public string ChannelUserId { get; set; } = null!;

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string HrId { get; set; } = null!;

    /// <summary>
    /// 类型
    /// </summary>
    public ChannelType? Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 是否默认顾问
    /// </summary>
    public bool IsDefaultHr { get; set; }

    /// <summary>
    /// 项目截止日期
    /// </summary>
    public DateTime? EndTime { get; set; } = Constants.DefaultFutureTime;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 群二维码
    /// </summary>
    public string? GroupQrCode { get; set; }

    // /// <summary>
    // /// 渠道二维码
    // /// </summary>
    // public string? ChannelQrCode { get; set; }

    // /// <summary>
    // /// 人才数量
    // /// </summary>
    // public int Talent { get; set; }

    // /// <summary>
    // /// 虚拟人才库数量
    // /// </summary>
    // public int VirtualTalent { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Seeker User_Seeker { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
}
