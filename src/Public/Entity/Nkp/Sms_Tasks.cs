﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 群发短信任务
/// </summary>
[Table("sms_tasks")]
public class Sms_Tasks
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime ExeTime { get; set; }

    /// <summary>
    /// 任务数(任务中手机号数量)
    /// </summary>
    public int Worker { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public SmsTasksStatus Status { get; set; }

    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 发送方及发送方式
    /// </summary>
    public SmsTasksSenderType SenderType { get; set; }

    /// <summary>
    /// 短信模板内容(页面输入的发送内容；SendType=庄点科技drondea批量时，Sms_Tasks_Detail表内就无需记录实际发送内容了(因与此字段一样))
    /// </summary>
    public string TaskTemplateContent { get; set; } = default!;
}

public enum SmsTasksStatus
{
    进行中, 失败 = 9, 已完成 = 10
}

/// <summary>
/// 发送方及发送方式
/// </summary>
public enum SmsTasksSenderType
{
    阿里云, 庄点科技drondea逐条 = 2, 庄点科技drondea批量 = 3
}