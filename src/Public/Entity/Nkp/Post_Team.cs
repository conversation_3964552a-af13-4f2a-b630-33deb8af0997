﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// hr职位
/// </summary>
[Table("post_team")]
public class Post_Team
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string TeamPostId { get; set; } =  Entity.EntityTools.SnowflakeId();

    public int AutoId { get; set; }

    /// <summary>
    /// Hr项目Id
    /// </summary>
    public string TeamProjectId { get; set; } = default!;

    // /// <summary>
    // /// （主创Id）
    // /// </summary>
    // public string HrId { get; set; } = default!;

    /// <summary>
    /// 原始职位Id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 类别（暂不用）
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public PostStatus Status { get; set; }

    /// <summary>
    /// 上架状态，计算得出
    /// </summary>
    public bool Show { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? AppletQrCode { get; set; }

    /// <summary>
    /// 短链接
    /// </summary>
    public string? AppletShortLink { get; set; }

    /// <summary>
    /// 零工市场小程序二维码
    /// </summary>
    public string? QuickJobAppletQrCode { get; set; }

    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? AppletShortLinkExp { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public string UpdatedBy { get; set; } = default!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 置顶时间
    /// </summary>
    public DateTime TopTime { get; set; } = Config.Constants.DefaultTime;


    public Post Post { get; set; } = default!;
    // public User_Hr User_Hr { get; set; } = default!;
    public List<Post_Team_Channel> Post_Team_Channel = default!;
    public Project_Team Project_Team { get; set; } = default!;
    public Post_Team_Extend Post_Team_Extend { get; set; } = default!;
    public Post_Excellent Post_Excellent { get; set; } = default!;
    public Post_Team_Third_Jobid_Rel Post_Team_Third_Jobid_Rel = default!;
}