﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;

namespace Entity.Nkp
{
    /// <summary>
    /// 用户业务短信记录表
    /// </summary>
    [Table("user_sms_record")]
    public class User_Sms_Record
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Key]
        public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

        /// <summary>
        /// 短信记录类型
        /// </summary>
        public UserSmsRecordType Type { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public string UserId { get; set; } = default!;

        /// <summary>
        /// 数量
        /// </summary>
        public int Amount { get; set; }

        /// <summary>
        /// 变更后余额
        /// </summary>
        public int Balance { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public string? Content { get; set; }

        /// <summary>
        /// 通用任务Id
        /// </summary>
        public string TaskId { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }
}
