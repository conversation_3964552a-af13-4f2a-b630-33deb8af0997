﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// 项目地区表
/// </summary>
[Table("project_region")]
public class Project_Region
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = default!;


    public Project Project { get; set; } = default!;

    public Dic_Region Dic_Region { get; set; } = default!;
}