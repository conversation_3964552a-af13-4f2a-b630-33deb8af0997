﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 诺积分
/// </summary>
[Table("user_nscore")]
public class User_NScore
{
    [Key]
    public string UserId { get; set; } = null!;

    /// <summary>
    /// 当前诺积分
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 历史累计诺积分
    /// </summary>
    public int ScoreTotal { get; set; }

    /// <summary>
    /// 求职者当前诺积分
    /// </summary>
    [ConcurrencyCheck]
    public int SeekerScore { get; set; }

    /// <summary>
    /// 求职者历史累计积分
    /// </summary>
    public int SeekerScoreTotal { get; set; }

    /// <summary>
    /// 求职者兑换现金
    /// </summary>
    public decimal SeekerMoney { get; set; }

    /// <summary>
    /// 求职者兑换奖品
    /// </summary>
    public int SeekerPrize { get; set; }

    /// <summary>
    /// hr当前诺积分
    /// </summary>
    public int HrScore { get; set; }

    /// <summary>
    /// hr历史累计诺积分
    /// </summary>
    public int HrScoreTotal { get; set; }

    /// <summary>
    /// 顾问兑换现金
    /// </summary>
    public decimal HrMoney { get; set; }

    /// <summary>
    /// 顾问兑换奖品
    /// </summary>
    public int HrPrize { get; set; }

    public User User { get; set; } = default!;
    public User_Seeker User_Seeker { get; set; } = default!;
}