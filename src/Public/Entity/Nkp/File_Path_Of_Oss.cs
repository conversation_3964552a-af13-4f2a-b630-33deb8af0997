﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

public class File_Path_Of_Oss
{
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// 所属项目类型
    /// </summary>
    public string Type { get; set; } = default!;

    /// <summary>
    /// 项目内唯一id
    /// </summary>
    public string PrimaryId { get; set; } = default!;

    /// <summary>
    /// url
    /// </summary>
    public string OssPath { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建人
    /// </summary>
    public int Deleted { get; set; } = 0;

    /// <summary>
    /// 简历用户Id
    /// </summary>
    public string UserId { get; set;} = default!;
}
