﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// 数字诺亚帐套表
/// </summary>
[Table("ndn_books")]
public class Ndn_Books
{
    /// <summary>
    /// 主键
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 账套ID
    /// </summary>
    public string BookCode { get; set; } = default!;

    /// <summary>
    /// 账套名称
    /// </summary>
    public string BookName { get; set; } = default!;

    /// <summary>
    /// 印章
    /// </summary>
    public string? ContractSeal { get; set; }

    /// <summary>
    /// 财务信息
    /// </summary>
    public NdnInvoiceInfo? InvoiceInfo { get; set; } = new NdnInvoiceInfo();

    /// <summary>
    ///  合同-诺聘对各个分公司开具的交易合同，用作数字诺亚发放服务奖金
    /// </summary>
    public string? ContractCode { get; set; }

    /// <summary>
    /// 操作人
    /// </summary>
    public string? OperatorId { get; set; }

    /// <summary>
    /// 操作人姓名
    /// </summary>
    public string? OperatorName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 发票
/// </summary>
public class NdnInvoiceInfo
{
    /// <summary>
    /// 发票类型
    /// </summary>
    public Config.Enums.Nkp.NovaPinInvoiceType? InvoiceType { get; set; }

    /// <summary>
    /// 发票抬头
    /// </summary>
    public string? InvoiceTitle { get; set; }

    /// <summary>
    /// 税号
    /// </summary>
    public string? TaxNumber { get; set; }

    /// <summary>
    /// 开户行
    /// </summary>
    public string? Bank { get; set; }

    /// <summary>
    /// 开户账号
    /// </summary>
    public string? BankAccount { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 开票电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 推送方式
    /// </summary>
    public Config.Enums.Nkp.NovaPinInvoicePushMode? PushMode { get; set; }

    /// <summary>
    /// 推送地址（选填)
    /// </summary>
    public string? PushAddress { get; set; }

    /// <summary>
    /// 开票人姓名
    /// </summary>
    public string? InvoiceOperatorName { get; set; }

    /// <summary>
    /// 开票人工号
    /// </summary>
    public string? InvoiceOperatorNo { get; set; }

    /// <summary>
    /// 合同人姓名
    /// </summary>
    public string? ContractOperatorName { get; set; }

    /// <summary>
    /// 合同人工号
    /// </summary>
    public string? ContractOperatorNo { get; set; }
}