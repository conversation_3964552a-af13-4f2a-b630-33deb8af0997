﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;
using NetTopologySuite.Geometries;

namespace Entity.Nkp;

/// <summary>
/// hr主表
/// </summary>
[Table("user_hr")]
public class User_Hr
{
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 诺聘Id(pk_eaid)
    /// </summary>
    public string NuoId { get; set; } = default!;

    /// <summary>
    /// 邀请码
    /// </summary>
    public string InviteCode { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }

    /// <summary>
    /// 角色Id
    /// </summary>
    public string? RoleId { get; set; }

    // /// <summary>
    // /// 微信小程序OpenId
    // /// </summary>
    // public string? WeChatAppletId { get; set; }

    /// <summary>
    /// 权限
    /// </summary>
    public List<string> Powers { get; set; } = new List<string>();

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Post { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? EMail { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 微信号
    /// </summary>
    public string? WeChatNo { get; set; }

    /// <summary>
    /// 企业微信二维码
    /// </summary>
    public string? EntWeChatQrCode { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? AppletQrCode { get; set; }

    // /// <summary>
    // /// 顾问邀约渠道二维码
    // /// </summary>
    // public string? ChannelInvQrCode { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public Sex? Sex { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 坐标
    /// </summary>
    public Point Location { get; set; } = new Point(0, 0);

    /// <summary>
    /// 注册源
    /// </summary>
    public RegisterSource Source { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    // [ConcurrencyCheck]
    public UserStatus Status { get; set; }

    /// <summary>
    /// Hr
    /// </summary>
    public int Score { get; set; }

    /// <summary>
    /// 申请开通零工时间
    /// </summary>
    public DateTime ApplicationTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 腾讯IM账号Id
    /// </summary>
    public string? TencentImId { get; set; }

    /// <summary>
    /// 公众号开关通知
    /// </summary>
    public bool H5Notice { get; set; } = true;

    /// <summary>
    /// 顾问标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User User { get; set; } = default!;
    public User_Extend User_Extend { get; set; } = default!;
    public User_Num User_Num { get; set; } = default!;
    public User_DingDing User_DingDing { get; set; } = default!;

    public User_Hr_Data User_Hr_Data { get; set; } = default!;
    public Enterprise Enterprise { get; set; } = default!;
    public Enterprise_Role Enterprise_Role { get; set; } = default!;
    public User_Sms User_Sms { get; set; } = default!;
    public List<Enterprise_Org_User> Enterprise_Org_User { get; set; } = default!;
    public List<User_Quick_Job_Audit> User_Quick_Job_Audit { get; set; } = default!;
    public List<Kuaishou_Hr_Talent_Relations> Kuaishou_Hr_Talent_Relations { get; set; } = default!;
}
