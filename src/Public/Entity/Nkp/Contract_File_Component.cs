﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// 合同文件组件
/// </summary>
[Table("contract_file_component")]
public class Contract_File_Component
{
    [Key]
    public string FileId { get; set; } = null!;

    /// <summary>
    /// 系统组件
    /// </summary>
    public string SystemComponents { get; set; } = null!;

    /// <summary>
    /// 自定义组件
    /// </summary>
    public string CustomComponents { get; set; } = null!;
}
