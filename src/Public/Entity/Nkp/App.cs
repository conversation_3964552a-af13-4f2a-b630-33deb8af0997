﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp
{
    /// <summary>
    /// app
    /// </summary>
    [Table("app")]
    public class App
    {
        [Key]
        public int Id { get; set; }

        public string AppId { get; set; } = default!;

        public string AppSecret { get; set; } = default!;

        public ClientType Type { get; set; } = default!;
        public string Describe { get; set; } = default!;
    }
}
