﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 人才库标签字典表（真实虚拟通用表）
/// </summary>
[Table("dic_talent_label")]
public class Dic_Talent_Label
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// hrid
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 标签名称
    /// </summary>
    public string LabelName { get; set; } = default!;
}

