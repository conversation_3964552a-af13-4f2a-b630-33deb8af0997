﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// hr审批记录表
/// </summary>
[Table("user_hr_audit")]
public class User_Hr_Audit
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    public string UserId { get; set; } = default!;

    public string EntId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 管理员Id
    /// </summary>
    public string AdminId { get; set; } = default!;

    /// <summary>
    /// 管理员Name
    /// </summary>
    public string AdminName { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Hr User_Hr { get; set; } = default!;
    public Enterprise Enterprise { get; set; } = default!;
}
