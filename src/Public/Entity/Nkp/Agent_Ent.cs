﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;
using NetTopologySuite.Geometries;

namespace Entity.Nkp;

/// <summary>
/// 代招企业
/// </summary>
[Table("agent_ent")]
public class Agent_Ent
{
    [Key]
    public string AgentEntId { get; set; } = EntityTools.SnowflakeId();
    
    /// <summary>
    /// 名字
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 企业简称
    /// </summary>
    public string Abbr { get; set; } = string.Empty;

    /// <summary>
    /// 展示项
    /// </summary>
    public EntDisplayType Display { get; set; } = EntDisplayType.企业名称;

    /// <summary>
    /// 展示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 企业Logo
    /// </summary>
    public string? LogoUrl { get; set; }

    /// <summary>
    /// 企业授权证明
    /// </summary>
    public string? AuthorizationUrl { get; set; } = string.Empty;

    /// <summary>
    /// 公司行业
    /// </summary>
    public int? Industry { get; set; } = 0;

    /// <summary>
    /// 企业性质
    /// </summary>
    public EnterpriseNature Nature { get; set; } = EnterpriseNature.个体工商户;

    /// <summary>
    /// 企业规模
    /// </summary>
    public EnterpriseScale Scale { get; set; } = EnterpriseScale.二十人以下;

    /// <summary>
    /// 融资阶段
    /// </summary>
    public EnterpriseCapital Capital { get; set; } = EnterpriseCapital.不需要融资;

    /// <summary>
    /// 状态
    /// </summary>
    public EnterpriseStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

   /// <summary>
    /// 坐标
    /// </summary>
    public Point Location { get; set; } = new Point(0, 0);

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
    
    
    public Tyc_Enterprise TycEnterprise { get; set; } = default!;
    
}