﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 虚拟人才库简历评论
/// </summary>
[Table("talent_virtual_comment")]
public class Talent_Virtual_Comment
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 虚拟人才库id
    /// </summary>
    public string VirtualId { get; set; } = default!;

    /// <summary>
    /// 评论内容
    /// </summary>
    public string Content { get; set; } = default!;

    /// <summary>
    /// HrId
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// HrName
    /// </summary>
    public string HrName { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Talent_Virtual Talent_Virtual { get; set; } = default!;

    public User_Hr User_Hr { get; set; } = default!;
}

