﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 钉钉部门
/// </summary>
[Table("dd_user")]
public class Dd_User
{
    [Key]
    public string DdUserId { get; set; } = default!;

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 职位
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 员工工号
    /// </summary>
    public string JobNumber { get; set; } = default!;

    /// <summary>
    /// 办公地点
    /// </summary>
    public string? WorkPlace { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public List<Dd_User_Dept> Dd_User_Dept { get; set; } = default!;
    public User User { get; set; } = default!;
}