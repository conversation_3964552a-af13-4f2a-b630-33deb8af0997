﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// 渠道置顶职位
/// </summary>
[Table("post_team_channel")]
public class Post_Team_Channel
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    public string TeamPostId { get; set; } = default!;

    public string ChannelId { get; set; } = default!;

    /// <summary>
    /// 置顶时间
    /// </summary>
    public DateTime? TopTime { get; set; }


    public Post_Team Post_Team { get; set; } = default!;
}