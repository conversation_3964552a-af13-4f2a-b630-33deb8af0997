﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// 项目协同分润表
/// </summary>
[Table("project_teambounty")]
public class Project_Teambounty
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// 公司协同
    /// </summary>
    public ProjectShareInternal? ShareInternal { get; set; } = ProjectShareInternal.不协同;

    /// <summary>
    /// 诺聘协同
    /// </summary>
    public ProjectSharePlatform? SharePlatform { get; set; } = ProjectSharePlatform.不协同;

    /// <summary>
    /// 平台协同
    /// </summary>
    public ProjectShareAnyone? ShareAnyone { get; set; } = ProjectShareAnyone.不协同;

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string TeamProjectId { get; set; } = default!;

    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; } = default!;

    /// <summary>
    /// 第三方简历池主键id
    /// </summary>
    public string? ResumeBufferId { get; set; }

    /// <summary>
    /// Hr职位Id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 原始职位Id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 求职者名称
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 求职者电话
    /// </summary>
    public string? SeekerMobile { get; set; }

    /// <summary>
    /// 主创Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 主创名称
    /// </summary>
    public string? HrName { get; set; }

    /// <summary>
    /// 渠道hrId
    /// </summary>
    public string? ChannelHrId { get; set; }

    /// <summary>
    /// 渠道商名称
    /// </summary>
    public string? ChannelHrName { get; set; }

    /// <summary>
    /// 协同HrId
    /// </summary>
    public string TeamHrId { get; set; } = default!;

    /// <summary>
    /// 协同名称
    /// </summary>
    public string? TeamHrName { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public ProjectPaymentNode? PaymentNode { get; set; }

    /// <summary>
    /// 打款天数
    /// </summary>
    public int? PaymentDays { get; set; }

    /// <summary>
    /// 打款方式
    /// </summary>
    public ProjectPaymentType? PaymentType { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ConcurrencyCheck]
    public ProjectTeambountyStatus Status { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public TeamBountySource Source { get; set; }

    /// <summary>
    /// 是否无效简历，默认0-否，1-是
    /// </summary>
    public int IfInValid { get; set; }

    /// <summary>
    /// 是否减库存，默认0-否，1-是
    /// </summary>
    public int IfStockOut { get; set; } = 0;

    /// <summary>
    /// 交付失败原因描述
    /// </summary>
    public string? Descriptioin { get; set; }

    /// <summary>
    /// 结算时间
    /// </summary>
    public DateTime? SettlementTime { get; set; }

    /// <summary>
    /// 结算金额
    /// </summary>
    public decimal? SettlementMoney { get; set; }

    /// <summary>
    /// 协同实收佣金
    /// </summary>
    public decimal? SettlementActualMoney { get; set; }

    /// <summary>
    /// 协同平台抽佣比例
    /// </summary>
    public decimal? PlatformSettlementRate { get; set; }

    /// <summary>
    /// 协同平台抽佣金额
    /// </summary>
    public decimal? PlatformSettlementMoney { get; set; }

    /// <summary>
    /// 渠道商结算金额
    /// </summary>
    public decimal? ChannelSettlementMoney { get; set; }

    /// <summary>
    /// 渠道商结算比例
    /// </summary>
    public decimal? ChannelSettlementRate { get; set; }

    /// <summary>
    /// 结算表主键id
    /// </summary>
    public string? SettlementId { get; set; }

    /// <summary>
    /// 渠道商合同关联id
    /// </summary>
    public string? ChannelContractId { get; set; }

    /// <summary>
    /// 结算状态
    /// </summary>
    public SettlementType SettlementStatus { get; set; } = SettlementType.待结算;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 渠道关系Id
    /// </summary>
    public string? ChannelId { get; set; }

    public Project Project { get; set; } = default!;
    public Recruit Recruit { get; set; } = default!;
    public Project_Team Project_Team { get; set; } = default!;
    public User_Seeker User_Seeker { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
    public User_Hr Team_User_Hr { get; set; } = default!;
    public Post Post { get; set; } = default!;
    public Post_Team Post_Team { get; set; } = default!;
}