﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 用户余额记录表
/// </summary>
[Table("user_balance_record")]
public class User_Balance_Record
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 变更后余额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 变更值
    /// </summary>
    public decimal Increment { get; set; }

    /// <summary>
    /// 用户类型
    /// </summary>
    public SeekerOrHr UserType { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public BalanceRecordType Type { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 内容json
    /// </summary>
    public string? Data { get; set; }

    /// <summary>
    /// 触发时间
    /// </summary>
    public DateTime EventTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Seeker User_Seeker { get; set; } = default!;
}