﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 快手租户表
/// </summary>
[Table("kuaishou_tenant")]
public class Kuaishou_Tenant
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 租户openid
    /// </summary>
    public string TenantOpenId { get; set; } = default!;

    /// <summary>
    /// 租户名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 创建人
    /// </summary>
    public string Creator { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
