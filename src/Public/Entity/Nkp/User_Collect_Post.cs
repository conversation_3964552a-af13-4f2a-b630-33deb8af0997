﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 收藏的职位
/// </summary>
[Table("user_collect_post")]
public class User_Collect_Post
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string TeamPostId { get; set; } = default!;

    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public User_Hr User_Hr { get; set; } = default!;
    public User_Seeker User_Seeker { get; set; } = default!;
    public Post_Team Post_Team { get; set; } = default!;
}