﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 职位字典
/// </summary>
[Table("dic_post")]
public class Dic_Post
{
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 父Id
    /// </summary>
    public int? ParentId { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    public string Level { get; set; } = default!;

    /// <summary>
    /// 标签json
    /// </summary>
    public string? Tags { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    public Dic_Post Parent { get; set; } = default!;
}