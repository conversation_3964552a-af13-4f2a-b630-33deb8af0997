﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 组织架构人员
/// </summary>
[Table("rpt_szny_contract")]
public class Rpt_Szny_Contract
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 员工工号
    /// </summary>
    public string JobNumber { get; set; } = default!;

    /// <summary>
    /// 合同编码
    /// </summary>
    public string SJHT_CODE { get; set; } = default!;

    /// <summary>
    /// 合同名称
    /// </summary>
    public string SJHT_NAME { get; set; } = string.Empty;

    /// <summary>
    /// 受益人编码
    /// </summary>
    public string SJHT_SY_RYCODE { get; set; } = string.Empty;

    /// <summary>
    /// 销帮帮合同编码
    /// </summary>
    public string SJHT_XBBBM { get; set; } = string.Empty;

    /// <summary>
    /// 产品编码
    /// </summary>
    public string SJHT_CPID { get; set; } = string.Empty;

    /// <summary>
    /// 产品名称
    /// </summary>
    public string SJHT_CPMC { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime SJHT_SETDATE { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime SJHT_BEGINDATE { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime SJHT_ENDDATE { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    public DateTime CreatedTime { get; set; } = DateTime.Now;
}
