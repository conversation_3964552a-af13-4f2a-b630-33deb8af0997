using Config.Enums.Nkp;

namespace Entity.Nkp;

public class QuickJobData
{
    /// <summary>
    /// 顾问Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 地区
    /// </summary>
    public string? RegionId { get; set; }

    /// <summary>
    /// 小程序二维码
    /// </summary>
    public string? AppletQrCode { get; set; }

    /// <summary>
    /// B端小程序二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// C端小程序Id
    /// </summary>
    public string? AppletAppId { get; set; }

    /// <summary>
    /// app名称
    /// </summary>
    public string? AppName { get; set; }
}

public class NScoreGoodsContent
{
    /// <summary>
    /// 缩略图
    /// </summary>
    public string? Thumbnail { get; set; }
}

public enum NScoreType
{
    注册, 每日登录, 首次报名, 分享, 首次入职, 分享好友登录, 分享好友注册, 分享好友报名, 分享好友入职,
    发布职位 = 50,
    协同项目,
    兑换商品 = 101
}

public class IntentionJob
{
    public int id { get; set; }
    public string? content { get; set; }
}

public class IntentionCity
{
    public string? cityCode { get; set; }
    public string? cityName { get; set; }
}

public class LocationCity
{
    public string? latitude { get; set; }
    public string? longitude { get; set; }
    public string? countryName { get; set; }
    public string? provinceName { get; set; }
    public string? cityName { get; set; }
    public string? cityLevel { get; set; }
    public string? countyName { get; set; }
    public string? townName { get; set; }
    public string? townType { get; set; }
    public string? address { get; set; }
    public string? adCode { get; set; }
    public bool? china { get; set; }
    public bool? municipality { get; set; }
    public bool? specialAdministrativeRegion { get; set; }
}

/// <summary>
/// 简历质量标签
/// </summary>
public class DataQualityLabel
{
    public string? Age { get; set; }
    public string? IntentionCity { get; set; }
    public string? IntentionJobCategory { get; set; }
    public string? MultiApplyRecently { get; set; }
    public string? LiveCity { get; set; }
}

/// <summary>
/// 投递时的动态简历信息
/// </summary>
public class DataDynamicResumeInfo
{
    /// <summary>
    /// 工作方式
    /// </summary>
    public string? WorkWay { get; set; }
    /// <summary>
    /// 最高学历
    /// </summary>
    public string? HighestDegree { get; set; }
    /// <summary>
    /// 工作经历
    /// </summary>
    public string? WorkExperience { get; set; }
    /// <summary>
    /// 驾驶经历
    /// </summary>
    public string? DriveExperience { get; set; }
    /// <summary>
    /// 基本情况
    /// </summary>
    public string? BasicSituation { get; set; }
    /// <summary>
    /// 技能
    /// </summary>
    public string? Skill { get; set; }
    /// <summary>
    /// 意向城市
    /// </summary>
    public string? IntentionCity { get; set; }
    /// <summary>
    /// 意向岗位
    /// </summary>
    public string? IntentionJobCategory { get; set; }
    /// <summary>
    /// 求职状态
    /// </summary>
    public string? JobHuntingStatus { get; set; }
    /// <summary>
    /// 证书
    /// </summary>
    public string? Certificate { get; set; }
}

/// <summary>
/// 求职期望
/// </summary>
public class ResumeBufferHopes
{
    /// <summary>
    /// 期望城市
    /// </summary>
    public string? HopeCity { get; set; }

    /// <summary>
    /// 期望行业名称集合
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal? MinSalary { get; set; }

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal? MaxSalary { get; set; }
}

/// <summary>
/// 教育经历
/// </summary>
public class ResumeBufferEducations
{
    /// <summary>
    /// 教育经历id
    /// </summary>
    public string? EduId { get; set; }

    /// <summary>
    /// 学校名称
    /// </summary>
    public string? SchoolName { get; set; }

    /// <summary>
    /// 是否全日制
    /// </summary>
    public bool? IsFullTime { get; set; }

    /// <summary>
    /// 学校logo
    /// </summary>
    public string? SchoolLogo { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation? Education { get; set; }

    /// <summary>
    /// 学历解释
    /// </summary>
    public string? EducationName { get; set; }

    /// <summary>
    /// 专业
    /// </summary>
    public string? MajorName { get; set; }

    /// <summary>
    /// 学校描述
    /// </summary>
    public string? SchoolRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 项目经历
/// </summary>
public class ResumeBufferProjects
{
    /// <summary>
    /// 项目经历id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 项目logo
    /// </summary>
    public string? ProjectLogo { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? ProjectRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 工作经历
/// </summary>
public class ResumeBufferWorks
{
    /// <summary>
    /// 工作经历id
    /// </summary>
    public string? WorkId { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// 公司部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 公司Logo
    /// </summary>
    public string? CompanyLogo { get; set; }

    /// <summary>
    /// 行业名称
    /// </summary>
    public string? IndustryName { get; set; }

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal? MinSalary { get; set; }

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal? MaxSalary { get; set; }

    /// <summary>
    /// 公司描述
    /// </summary>
    public string? CompanyRemarks { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 技能分析
/// </summary>
public class ResumeBufferSkill
{
    /// <summary>
    /// 专业技能集合
    /// </summary>
    public List<string>? Professional { get; set; }

    /// <summary>
    /// IT技能集合
    /// </summary>
    public List<string>? IT { get; set; }

    /// <summary>
    /// 商业技能集合
    /// </summary>
    public List<string>? Business { get; set; }
}

public class WelfareModel
{
    public int Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Logo
    /// </summary>
    public string? Logo { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }
}

public class SchoolModel
{
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 类别,0=专科，1=本科，2=成人
    /// </summary>
    public SchoolType Type { get; set; }

    /// <summary>
    /// logo
    /// </summary>
    public string? Logo { get; set; }
}

public class TokenInfo
{
    /// <summary>
    /// 有效期（秒）
    /// </summary>
    public int TokenExpiresTime { get; set; }

    /// <summary>
    /// 访问token
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// 刷新token
    /// </summary>
    public string? RefreshToken { get; set; }

    public string? UserId { get; set; }
}

public class CityModel
{
    /// <summary>
    /// 行政Id
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// 乡镇
    /// </summary>
    public string? TownName { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public string? CountyName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public string? CityName { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public string? ProvinceName { get; set; }
}

public enum AppApplyStatus
{
    未申请, 审核中, 审核通过, 无法申请, 审核拒绝
}
public class GetQuickJobAppsInfo
{
    /// <summary>
    /// 零工市场数据
    /// </summary>
    public QuickJobData? QuickJobData { get; set; }

    public string? Name { get; set; }
}

public class PostWorkingHours
{
    /// <summary>
    /// 工作时间段起始
    /// </summary>
    public string Begin { get; set; } = string.Empty;

    /// <summary>
    /// 工作时间段截止
    /// </summary>
    public string End { get; set; } = string.Empty;
}

public class KdAddressInfo
{
    /// <summary>
    /// 姓名
    /// </summary>
    public string User { get; set; } = default!;

    /// <summary>
    /// 电话
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 区
    /// </summary>
    public string County { get; set; } = default!;

    /// <summary>
    /// 市
    /// </summary>
    public string City { get; set; } = default!;

    /// <summary>
    /// 省
    /// </summary>
    public string Province { get; set; } = default!;

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; } = default!;
}

/// <summary>
/// 零工市场业务字段
/// </summary>
public class QuickJobInfo
{
    /// <summary>
    /// 项目行业 = 项目执行/发布项目（项目行业）/ 所属产业
    /// </summary>
    public ProjectIndustry? Industry { get; set; }

    /// <summary>
    /// 公司所属行业
    /// </summary>
    public int? CompanyIndustry { get; set; }

    /// <summary>
    /// 联系人姓名 = 项目执行 /编辑名片（姓名）
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 担任职位
    /// </summary>
    public string? PositionName { get; set; }

    /// <summary>
    /// 招聘职位名称 - name
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 职位类别 - id
    /// </summary>
    public int Category { get; set; } = 1043;// 其他

    /// <summary>
    /// 联系人手机 = 项目执行 /编辑名片（有手机号码不可修改）
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 联系人电子邮箱 = 项目执行 /编辑名片（邮箱）
    /// </summary>
    public string Email { get; set; } = default!;

    /// <summary>
    /// 对接单位
    /// </summary>
    public string? ContactCompany { get; set; }

    /// <summary>
    /// 推荐码
    /// </summary>
    public string? ReferralCode { get; set; }

    /// <summary>
    /// 营业执照
    /// </summary>
    public string? CompanyLicense { get; set; }

    /// <summary>
    /// 公司介绍
    /// </summary>
    public string? CompanyInfo { get; set; }

    /// <summary>
    /// 纳税人类型
    /// </summary>
    public TaxpayerType TaxpayerType { get; set; }

    /// <summary>
    /// 企业logo
    /// </summary>
    public string LogoUrl { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// 公司简称
    /// </summary>
    public string CompanyAbbr { get; set; } = string.Empty;

    /// <summary>
    /// 工作地址
    /// </summary>
    public string CompanyAddress { get; set; } = default!;

    /// <summary>
    /// 工作地址坐标 new Point(model.Lat, model.Lng);
    /// </summary>
    public double? Lat { get; set; }
    public double? Lng { get; set; }

    /// <summary>
    /// 企业性质
    /// </summary>
    public EnterpriseNature Nature { get; set; } = EnterpriseNature.个体工商户;

    /// <summary>
    /// 企业规模
    /// </summary>
    public EnterpriseScale Scale { get; set; } = EnterpriseScale.二十人以下;

    /// <summary>
    /// 融资阶段
    /// </summary>
    public EnterpriseCapital Capital { get; set; }

    /// <summary>
    /// 地区ID
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 职位信息
    /// </summary>
    public PostInfo? PostInfo { get; set; }
}

/// <summary>
/// 职位信息
/// </summary>
public class PostInfo
{
    /// <summary>
    /// 职位描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 职位性质
    /// </summary>
    public PostWorkNature? WorkNature { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public EducationType? Education { get; set; }

    /// <summary>
    /// 薪资最小值
    /// </summary>
    public int? MinSalary { get; set; }

    /// <summary>
    /// 薪资最大值
    /// </summary>
    public int? MaxSalary { get; set; }

    /// <summary>
    /// 几薪
    /// </summary>
    public int? Salary { get; set; }

    /// <summary>
    /// 福利（更新只提交Id即可）
    /// </summary>
    public List<WelfareModel>? Welfare { get; set; } = new List<WelfareModel>();

    /// <summary>
    /// 自定义福利:只传name
    /// </summary>
    public List<string> Welfarecustom { get; set; } = new List<string> { };

    /// <summary>
    /// 招聘人数
    /// </summary>
    public int RecruitNumber { get; set; }

    /// <summary>
    /// 结算方式
    /// </summary>
    public PostSalaryType SalaryType { get; set; } = PostSalaryType.月薪;

    /// <summary>
    /// 结算方式
    /// </summary>
    public PostSettlementType SettlementType { get; set; } = PostSettlementType.月结;

    /// <summary>
    /// 是否发布
    /// </summary>
    public bool Published { get; set; } = false;
}

public enum HrMsgType
{
    职位报名, 面试日程, 入职信息, 协同交付, 归档消息
}

public enum MsgNotifyType
{
    职位报名, 入职信息, 协同交付, 面试官日程, 面试用户日程, 归档,
    职位优选面试, 职位优选入职, 自流转入职提醒, 自流转归档提醒, 协同归档,
    诺聘发布职位 = 101, 诺聘投递简历
}

/// <summary>
/// 消息基类
/// </summary>
public class MsgNotifyModel
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public MsgNotifyType? Type { get; set; }

    /// <summary>
    /// 主创Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 协同Id
    /// </summary>
    public string? TeamHrId { get; set; }

    /// <summary>
    /// 时间
    /// </summary>
    public DateTime EventTime { get; set; }

    /// <summary>
    /// 数据
    /// </summary>
    public object Data { get; set; } = default!;
}

// /// <summary>
// /// Im单聊消息通知
// /// </summary>
// public class MsgNotifyImC2CMsg
// {
//     /// <summary>
//     /// Im账号Id
//     /// </summary>
//     public string? ImId { get; set; }

//     /// <summary>
//     /// To_Account 未读的单聊消息总数量（包含所有的单聊会话）。若该条消息下发失败（例如被脏字过滤），该字段值为-1
//     /// </summary>
//     public int UnreadMsgNum { get; set; }
// }

/// <summary>
/// 招聘流程模型
/// </summary>
public class MsgNotifyRecruit
{
    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 代招企业名称
    /// </summary>
    public string? AgentEntName { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }
}

/// <summary>
/// 协同交付
/// </summary>
public class MsgNotifyTeamDelivery
{
    /// <summary>
    /// 主创Id
    /// </summary>
    public string? HrId { get; set; }

    /// <summary>
    /// 招聘流程Id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 协同Id
    /// </summary>
    public string? TeamHrId { get; set; }

    /// <summary>
    /// 协同职位Id
    /// </summary>
    public string? TeamPostId { get; set; }

    /// <summary>
    /// 协同项目Id
    /// </summary>
    public string? TeamProjectId { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string? SeekerName { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Money { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ProjectTeambountyStatus Status { get; set; }
}

/// <summary>
/// 项目结束
/// </summary>
public class MsgNotifyProjectEnd
{
    /// <summary>
    /// 项目Id
    /// </summary>
    public string? ProjectId { get; set; }

    // /// <summary>
    // /// 协同项目Id
    // /// </summary>
    // public string? TeamProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// 顾问名称
    /// </summary>
    public string? HrName { get; set; }
}

/// <summary>
/// 诺聘发布职位
/// </summary>
public class MsgNotifyNpCreatePost
{
    /// <summary>
    /// 职位Id
    /// </summary>
    public string PostId { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = default!;

    /// <summary>
    /// 企业名称
    /// </summary>
    public string EntName { get; set; } = default!;
}

/// <summary>
/// 诺聘投递简历
/// </summary>
public class MsgNotifyNpDeliver
{
    /// <summary>
    /// 投递Id
    /// </summary>
    public string DeliverId { get; set; } = default!;

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string UserName { get; set; } = default!;

    /// <summary>
    /// 用户手机号
    /// </summary>
    public string UserMobile { get; set; } = default!;

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string EntId { get; set; } = default!;

    /// <summary>
    /// 企业名称
    /// </summary>
    public string EntName { get; set; } = default!;
}

/// <summary>
/// 职位优选
/// </summary>
public class MsgExcellentPost
{
    /// <summary>
    /// 职位名称
    /// </summary>
    public string? PostName { get; set; }

    /// <summary>
    /// 招聘id
    /// </summary>
    public string? RecruitId { get; set; }

    /// <summary>
    /// 求职者姓名
    /// </summary>
    public string? SeekerName { get; set; }
}

/// <summary>
/// 面试日程
/// </summary>
public class MsgNotifyInterview
{
}