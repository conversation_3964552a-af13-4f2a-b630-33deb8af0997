﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 用户访问职位
/// </summary>
[Table("user_post_visit")]
public class User_Post_Visit
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 当前顾问（暂不用）
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 职位Id
    /// </summary>
    public int Post { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Dic_Post Dic_Post { get; set; } = default!;
}