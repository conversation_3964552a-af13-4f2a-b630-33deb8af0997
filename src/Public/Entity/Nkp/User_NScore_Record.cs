﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 诺积分记录
/// </summary>
[Table("user_nscore_record")]
public class User_NScore_Record
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    public string UserId { get; set; } = null!;

    /// <summary>
    /// 当前值
    /// </summary>
    public int Amount { get; set; }

    /// <summary>
    /// 增量
    /// </summary>
    public int Increment { get; set; }

    /// <summary>
    /// hr还是求职者
    /// </summary>
    public SeekerOrHr UserType { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public NScoreType Type { get; set; }

    /// <summary>
    /// 记录内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 数据json
    /// </summary>
    public string? Data { get; set; }

    /// <summary>
    /// 发生时间
    /// </summary>
    public DateTime EventTime { get; set; } = DateTime.Now;

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Seeker User_Seeker { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
    public User User { get; set; } = default!;
}