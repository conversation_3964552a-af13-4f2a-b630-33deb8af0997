﻿using Config.Enums.Nkp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp
{
    /// <summary>
    /// 快手投递简历表
    /// </summary>
    [Table("kuaishou_talent_infos")]
    public class Kuaishou_Talent_Infos
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Key]
        public string ApplicationId { get; set; } = default!;
        /// <summary>
        /// 绑定的租户 id
        /// </summary>
        public string OpenTenantId { get; set; } = default!;
        /// <summary>
        /// 简历id
        /// </summary>
        public string ResumeId { get; set; } = default!;
        /// <summary>
        /// 平台类型0
        /// </summary>
        public ThirdPlatType PlatType { get; } = ThirdPlatType.快招工;
        /// <summary>
        /// 姓名
        /// </summary>
        public string? Name { get; set; }
        /// <summary>
        /// 性别编码
        /// </summary>
        public int? GenderCode { get; set; }
        /// <summary>
        /// 性别名称
        /// </summary>
        public string? GenderName { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        public string? Phone { get; set; }
        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }
        /// <summary>
        /// 职位id
        /// </summary>
        public string JobId { get; set; } = default!;
        /// <summary>
        /// 职位名称
        /// </summary>
        public string? JobName { get; set; }
        /// <summary>
        /// 最终推送职位id
        /// </summary>
        public string? SendTeamPostId { get; set; }
        /// <summary>
        /// 最终推送职位名称
        /// </summary>
        public string? SendTeamPostName { get; set; }
        /// <summary>
        /// 最终推送职位postid
        /// </summary>
        public string? SendPostId { get; set; }
        /// <summary>
        /// 来源渠道（简历来源，直播、短视频等）
        /// </summary>
        public string? ChannelName { get; set; }
        /// <summary>
        /// 推荐人（来源主播昵称）
        /// </summary>
        public string? Recommender { get; set; }
        /// <summary>
        /// 推荐人（来源主播uid）
        /// </summary>
        public string? RecommenderId { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime ApplyTime { get; set; }
        /// <summary>
        /// 企业id
        /// </summary>
        public string? CompanyId { get; set; }
        /// <summary>
        /// 企业中文
        /// </summary>
        public string? CompanyBusinessName { get; set; }
        /// <summary>
        /// 企业工商code
        /// </summary>
        public string? CompanyBusinessCode { get; set; }
        /// <summary>
        /// 0 - 未知的 1 - 自招职位投递 2 - 分销职位投递
        /// </summary>
        public int Platform { get; set; }
        /// <summary>
        /// 来源渠道 英文
        /// </summary>
        public string? DataChannelSourceCode { get; set; }
        /// <summary>
        /// 来源渠道 中文
        /// </summary>
        public string? DataChannelSourceName { get; set; }
        /// <summary>
        /// 简历质量标签
        /// </summary>
        public DataQualityLabel DataQualityLabel { get; set; } = new DataQualityLabel();
        /// <summary>
        /// 投递时的动态简历信息
        /// </summary>
        public DataDynamicResumeInfo DataDynamicResumeInfo { get; set; } = new DataDynamicResumeInfo();
        /// <summary>
        /// 动态信息原文信息
        /// </summary>
        public string? DynamicResumeInfoOriginal { get; set; }
        /// <summary>
        /// 流量来源
        /// </summary>
        public string? DataTrafficSources { get; set; }
        /// <summary>
        /// 用户所在城市
        /// </summary>
        public string? DataUserCity { get; set; }
        /// <summary>
        /// 位置信息
        /// </summary>
        public LocationCity DataLocationCitys { get; set; } = new LocationCity();
        /// <summary>
        /// 年龄范围的最小值
        /// </summary>
        public int? ExtInfoAgeMin { get; set; }
        /// <summary>
        /// 年龄范围的最大值
        /// </summary>
        public int? ExtInfoAgeMax { get; set; }
        /// <summary>
        /// 意向职位
        /// </summary>
        public List<IntentionJob> ExtInfoIntentionJob { get; set; } = new List<IntentionJob>();
        /// <summary>
        /// 意向城市
        /// </summary>
        public List<IntentionCity> ExtInfoIntentionCity { get; set; } = new List<IntentionCity>();
        /// <summary>
        /// 在职状态
        /// </summary>
        public int? ExtInfoJobHuntingStatus { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 是否删除 1 - 已删除
        /// </summary>
        public int Deleted { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedUser { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string? UpdatedUser { get; set; }

        //public List<Kuaishou_Hr_Talent_Relations> Kuaishou_Hr_Talent_Relations { get; set; } = default!;
        public Kuaishou_Hr_Talent_Relations Kuaishou_Hr_Talent_Relations { get; set; } = default!;

        public Post_Team_Third_Jobid_Rel Post_Team_Third_Jobid_Rel { get; set; } = default!;

        public Post_Team Post_Team { get; set; } = default!;
    }

}
