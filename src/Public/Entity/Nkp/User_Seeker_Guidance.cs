﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 用户的顾问列表
/// </summary>
[Table("user_seeker_guidance")]
public class User_Seeker_Guidance
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 求职者Id
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 顾问Id
    /// </summary>
    public string AdviserId { get; set; } = default!;

    /// <summary>
    /// 可沟通时间类型
    /// </summary>
    public FreeTimeType? FreeTimeType { get; set; }

    /// <summary>
    /// 可沟通时间
    /// </summary>
    public DateTime? FreeTime { get; set; }

    /// <summary>
    /// 可电话沟通
    /// </summary>
    public bool? CanPhone { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Hr User_Hr { get; set; } = default!;
    public User_Seeker User_Seeker { get; set; } = default!;
}