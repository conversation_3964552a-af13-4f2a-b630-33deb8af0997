using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Entity.Nkp;

/// <summary>
/// 子账户实体
/// </summary>
[Table("settletment_sub_account")]
public class SubAccount
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 渠道号
    /// </summary>
    [Required]
    [StringLength(64)]
    public string ChannelNo { get; set; } = default!;

    /// <summary>
    /// 平台级商户号
    /// </summary>
    [Required]
    [StringLength(36)]
    public string MerchantNo { get; set; } = default!;

    /// <summary>
    /// 外部用户号 (项目id)
    /// </summary>
    [Required]
    [StringLength(64)]
    public string ExternalUserNo { get; set; } = default!;

    /// <summary>
    /// 外部流水号
    /// </summary>
    [Required]
    [StringLength(64)]
    public string AppSerialNumber { get; set; } = default!;
    
    /// <summary>
    /// 手机号 (SM4 ECB加密)
    /// </summary>
    [StringLength(512)]
    public string? Mobile { get; set; }

    /// <summary>
    /// 用户类型 (01:自然人, 02:个体工商户, 03:企业商户, 04:实体卡)
    /// </summary>
    [Required]
    [StringLength(2)]
    public string UserType { get; set; } = default!;

    /// <summary>
    /// 子账户类型
    /// </summary>
    [StringLength(2)]
    public string? SubAccountType { get; set; }

    /// <summary>
    /// 产品类型 (二级子产品类型)
    /// </summary>
    [StringLength(20)]
    public string? SubProductCode { get; set; }

    /// <summary>
    /// 上级账户号
    /// </summary>
    [StringLength(64)]
    public string? ParentAccountNo { get; set; }

    /// <summary>
    /// 商户名称 (SM4 ECB加密)
    /// </summary>
    [StringLength(512)]
    public string? MerchantName { get; set; }

    /// <summary>
    /// 证件有效期起始日期 (yyyyMMdd)
    /// </summary>
    [StringLength(8)]
    public string? CertificateValidDateBegin { get; set; }

    /// <summary>
    /// 证件有效期截至日期 (yyyyMMdd, 永久填********)
    /// </summary>
    [StringLength(8)]
    public string? CertificateValidDateEnd { get; set; }

    /// <summary>
    /// 企业证件类型
    /// </summary>
    [StringLength(2)]
    public string? CorporateCertificateType { get; set; }

    /// <summary>
    /// 企业证件号码 (SM4 ECB加密)
    /// </summary>
    [StringLength(512)]
    public string? CorporateCertificateNo { get; set; }

    /// <summary>
    /// 企业证件有效期 (yyyyMMdd)
    /// </summary>
    [StringLength(8)]
    public string? MerchantCertificateValidDate { get; set; }
    
    /// <summary>
    /// 风险信息 (SM4 ECB加密)
    /// </summary>
    [StringLength(2048)]
    public string? RiskInfoEncrypted { get; set; }

    /// <summary>
    /// 加密key (使用账户公钥SM2加密)
    /// </summary>
    [StringLength(512)]
    public string? Key { get; set; }

    /// <summary>
    /// 商户保留域
    /// </summary>
    [StringLength(512)]
    public string? MerchantReserved { get; set; }

    // === 响应相关字段 ===

    /// <summary>
    /// 账户号 (客户身份识别码) 账户号转账使用
    /// </summary>
    [StringLength(64)]
    public string? AccountNumber { get; set; }

    /// <summary>
    /// 开户状态
    /// </summary>
    public SubAccountOpenStatus OpenStatus { get; set; } = SubAccountOpenStatus.审核中;

    /// <summary>
    /// 失败原因
    /// </summary>
    [StringLength(256)]
    public string? FailReason { get; set; }

    /// <summary>
    /// 错误码
    /// </summary>
    [StringLength(32)]
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 错误码描述
    /// </summary>
    [StringLength(512)]
    public string? ErrorInfo { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建人
    /// </summary>
    [StringLength(64)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    [StringLength(64)]
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(512)]
    public string? Remark { get; set; }

    /// <summary>
    /// 是否删除
    /// </summary>
    public bool IsDeleted { get; set; } = false;
    
    /// <summary>
    /// 金额 单位分
    /// </summary>
    [ConcurrencyCheck]
    public long? Amount { get; set; } = 0;
}

/// <summary>   
/// 子账户开户状态枚举
/// </summary>
public enum SubAccountOpenStatus
{
    /// <summary>
    /// 审核中
    /// </summary>
    审核中 = 0,
    
    /// <summary>
    /// 成功
    /// </summary>
    成功 = 1,
    
    /// <summary>
    /// 失败
    /// </summary>
    失败 = 2,
    
    未查询到入网信息 = 3,
    
    未激活 = 4,
}
