﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Entity.Nkp;

/// <summary>
/// 人才转移
/// </summary>
[Table("talent_transfer")]
public class Talent_Transfer
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    public string HrId { get; set; } = default!;

    public string ToHrId { get; set; } = default!;
    public string? Creator { get; set; } = string.Empty;
    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public User_Hr User_Hr { get; set; } = default!;
    public User_Hr To_User_Hr { get; set; } = default!;
    public List<Talent_Transfer_Record> Talent_Transfer_Record { get; set; } = default!;
}