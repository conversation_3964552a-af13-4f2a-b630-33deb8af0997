﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.CommonModel;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 零工市场审批记录表
/// </summary>
[Table("user_quick_job_audit")]
public class User_Quick_Job_Audit
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    public string UserId { get; set; } = default!;

    public string QuickJobHrId { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public AppApplyStatus Status { get; set; }

    /// <summary>
    /// 业务信息json
    /// </summary>
    public QuickJobInfo Info { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    /// 管理员Id
    /// </summary>
    public string AdminId { get; set; } = default!;

    /// <summary>
    /// 管理员Name
    /// </summary>
    public string? AdminName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    public string? CompanyName { get; set; }

    public User_Hr User_Hr { get; set; } = default!;
    public User_Hr QuickJobHr { get; set; } = default!;

    //public Enterprise Enterprise { get; set; } = default!;
}

