﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 协同职位扩展表
/// </summary>
[Table("post_team_extend")]
public class Post_Team_Extend
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string TeamPostId { get; set; } = default!;

    /// <summary>
    /// 投递人数累计去重
    /// </summary>
    public int DeliveryNum { get; set; }

    /// <summary>
    /// 简历初筛人数
    /// </summary>
    public int HrScreeningNum { get; set; }

    /// <summary>
    /// 面试官筛选人数
    /// </summary>
    public int InterviewerScreeningNum { get; set; }

    /// <summary>
    /// 面试人数
    /// </summary>
    public int InterviewNum { get; set; }

    /// <summary>
    /// Offer人数
    /// </summary>
    public int OfferNum { get; set; }

    /// <summary>
    /// 入职人数
    /// </summary>
    public int InductionNum { get; set; }

    /// <summary>
    /// 签约人数
    /// </summary>
    public int ContractNum { get; set; }

    /// <summary>
    /// 结算人数
    /// </summary>
    public int SettlementNum { get; set; }
}