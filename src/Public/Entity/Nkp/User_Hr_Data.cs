﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 顾问数据
/// </summary>
[Table("user_hr_data")]
public class User_Hr_Data
{
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 历史累计发布项目数量
    /// </summary>
    public int MyProjectTotal { get; set; }

    /// <summary>
    /// 历史累计协同项目数量
    /// </summary>
    public int TeamProjectTotal { get; set; }

    /// <summary>
    /// 进行中的项目（主创）
    /// </summary>
    public int MyProjectOnline { get; set; }

    /// <summary>
    /// 进行中的项目（协同）
    /// </summary>
    public int TeamProjectOnline { get; set; }

    /// <summary>
    /// 历史累计职位数量（主创）
    /// </summary>
    public int MyPostTotal { get; set; }

    /// <summary>
    /// 历史累计职位数量（协同）
    /// </summary>
    public int TeamPostTotal { get; set; }

    /// <summary>
    /// 进行中职位数量（主创）
    /// </summary>
    public int MyPostOnline { get; set; }

    /// <summary>
    /// 进行中职位数量（协同）
    /// </summary>
    public int TeamPostOnline { get; set; }

    /// <summary>
    /// 招聘中的人数（主创）
    /// </summary>
    /// </summary>
    public int MyRecruitOnline { get; set; }

    /// <summary>
    /// 招聘中的人数（协同）
    /// </summary>
    public int TeamRecruitOnline { get; set; }

    /// <summary>
    /// 累计投递用户数
    /// </summary>
    public int RecruitDelivery { get; set; }

    /// <summary>
    /// Hr初筛
    /// </summary>
    public int RecruitHrScreening { get; set; }

    /// <summary>
    /// 面试官筛选
    /// </summary>
    public int RecruitInterviewerScreening { get; set; }

    /// <summary>
    /// 面试
    /// </summary>
    public int RecruitInterview { get; set; }

    /// <summary>
    /// Offer
    /// </summary>
    public int RecruitOffer { get; set; }

    /// <summary>
    /// 入职
    /// </summary>
    public int RecruitInduction { get; set; }

    /// <summary>
    /// 签约
    /// </summary>
    public int RecruitContract { get; set; }

    /// <summary>
    /// 归档
    /// </summary>
    public int RecruitFileAway { get; set; }

    /// <summary>
    /// 累计招聘人数
    /// </summary>
    public int RecruitTotal { get; set; }

    /// <summary>
    /// 累计面试
    /// </summary>
    public int RecruitInterviewTotal { get; set; }

    /// <summary>
    /// 累计入职
    /// </summary>
    public int RecruitInductionTotal { get; set; }

    public User_Hr User_Hr { get; set; } = default!;
}
