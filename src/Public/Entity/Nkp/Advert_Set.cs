﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp
{
    /// <summary>
    /// 广告管理
    /// </summary>
    [Table("advert_set")]
    public class Advert_Set
    {
        [Key]
        public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();
        /// <summary>
        /// 轮播图
        /// </summary>
        public string? ImageUrl { get; set; }

        /// <summary>
        /// 链接地址
        /// </summary>
        public string? LinkUrl { get; set; }

        /// <summary>
        /// 链接方式
        /// </summary>
        public Advert_LinkType LinkType { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public Advert_State State { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        public DateTime UpdatedTime { get; set; }
        public DateTime CreatedTime { get; set; }
    }

    /// <summary>
    /// 广告链接方式
    /// </summary>
    public enum Advert_LinkType
    {
        H5网址 = 1,
        小程序 = 2
    }

    /// <summary>
    /// 广告状态
    /// </summary>
    public enum Advert_State
    {
        已上架 = 1,
        已下架 = 2
    }
}
