﻿using Config.Enums.Nkp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;
/// <summary>
/// 虚拟人才库教育经历
/// </summary>
[Table("talent_virtual_edu")]
public class Talent_Virtual_Edu
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 虚拟人才库id
    /// </summary>
    public string VirtualId { get; set; } = default!;

    /// <summary>
    /// 学校名称
    /// </summary>
    public string SchoolName { get; set; } = default!;

    /// <summary>
    /// 是否全日制
    /// </summary>
    public bool IsFullTime { get; set; }

    /// <summary>
    /// 学历
    /// </summary>
    public TalentVirtualEducation Education { get; set; } = TalentVirtualEducation.Junior;

    /// <summary>
    /// 专业名称
    /// </summary>
    public string MajorName { get; set; } = default!;

    /// <summary>
    /// 教育开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 教育结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 学校注释
    /// </summary>
    public string SchoolRemarks { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Talent_Virtual Talent_Virtual { get; set; } = default!;
}

