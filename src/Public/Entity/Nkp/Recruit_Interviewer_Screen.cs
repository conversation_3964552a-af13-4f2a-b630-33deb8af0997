﻿using Config.Enums.Nkp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 面试官筛选表
/// </summary>
[Table("recruit_interviewer_screen")]
public class Recruit_Interviewer_Screen
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 招聘Id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 求职者Id，冗余
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 面试官Id
    /// </summary>
    public string? InterviewerId { get; set; } = string.Empty;

    /// <summary>
    /// 推荐理由
    /// </summary>
    public string Recommend { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public RecruitInterviewerScreenStatus Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remarks { get; set; } = string.Empty;

    /// <summary>
    /// 面试官反馈时间
    /// </summary>
    public DateTime? TodoTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Recruit Recruit { get; set; } = default!;

    public Project_Interviewer Project_Interviewer { get; set; } = default!;

    public User_Seeker User_Seeker { get; set; } = default!;
}

