﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 人才库对应标签表
/// </summary>
[Table("talent_label")]
public class Talent_Label
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 虚拟人才库id
    /// </summary>
    public string? VirtualId { get; set; } = string.Empty;

    /// <summary>
    /// 真实人才库id
    /// </summary>
    public string? PlatformId { get; set; } = string.Empty;

    /// <summary>
    /// 标签id
    /// </summary>
    public string DicLabelId { get; set; } = default!;

    public Talent_Virtual Talent_Virtual { get; set; } = default!;

    public Talent_Platform Talent_Platform { get; set; } = default!;

    public Dic_Talent_Label Dic_Talent_Label { get; set; } = default!;
}

