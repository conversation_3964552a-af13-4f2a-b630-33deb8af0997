﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 职位结算单
/// </summary>
[Table("post_settlement")]
public class Post_Settlement
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// Post_Bounty_Stage表主键id
    /// </summary>
    public string BountyStageId { get; set; } = default!;

    /// <summary>
    /// 结算状态
    /// </summary>
    [ConcurrencyCheck]
    public PostSettleStatus Status { get; set; } = PostSettleStatus.进行中;

    /// <summary>
    /// 审批状态
    /// </summary>
    [ConcurrencyCheck]
    public PostSettleApprovalStatus ApprovalStatus { get; set; } = PostSettleApprovalStatus.项目经理审核;

    /// <summary>
    /// 结算量
    /// </summary>
    public decimal SettlementAmount { get; set; }

    /// <summary>
    /// 结算金额
    /// </summary>
    public decimal SettlementMoney { get; set; }

    /// <summary>
    /// 项目经理确认人
    /// </summary>
    public string? ManagerConfirmUser { get; set; }

    /// <summary>
    /// 项目经理确认时间
    /// </summary>
    public DateTime? ManagerConfirmTime { get; set; }

    /// <summary>
    /// 平台确认人
    /// </summary>
    public string? PlatformConfirmUser { get; set; }

    /// <summary>
    /// 平台确认时间
    /// </summary>
    public DateTime? PlatformConfirmTime { get; set; }

    /// <summary>
    /// 财务确认人
    /// </summary>
    public string? FinanceConfirmUser { get; set; }

    /// <summary>
    /// 财务确认时间
    /// </summary>
    public DateTime? FinanceConfirmTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 结算开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结算截止时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}
