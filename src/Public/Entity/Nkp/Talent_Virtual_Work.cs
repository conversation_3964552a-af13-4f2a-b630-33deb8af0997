﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 虚拟人才库工作经历
/// </summary>
[Table("talent_virtual_work")]
public class Talent_Virtual_Work
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 虚拟人才库id
    /// </summary>
    public string VirtualId { get; set; } = default!;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// 公司部门
    /// </summary>
    public string Department { get; set; } = default!;

    /// <summary>
    /// 行业名称
    /// </summary>
    public string IndustryName { get; set; } = default!;

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string PostName { get; set; } = default!;

    /// <summary>
    /// 最低薪资
    /// </summary>
    public decimal MinSalary { get; set; } = 0;

    /// <summary>
    /// 最高薪资
    /// </summary>
    public decimal MaxSalary { get; set; } = 0;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 公司描述
    /// </summary>
    public string CompanyRemarks { get; set; } = default!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Talent_Virtual Talent_Virtual { get; set; } = default!;
}
