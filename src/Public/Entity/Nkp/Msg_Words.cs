﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 消息常用语
/// </summary>
[Table("msg_words")]
public class Msg_Words
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 用户Id
    /// </summary>
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 类型
    /// </summary>
    public MsgWordsType Type { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string? Content { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;
}