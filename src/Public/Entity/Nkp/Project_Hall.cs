﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// 项目大厅
/// </summary>
[Table("project_hall")]
public class Project_Hall
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 项目Id
    /// </summary>
    public string ProjectId { get; set; } = default!;

    /// <summary>
    /// 用户Id
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 类型
    /// </summary>
    public ProjectHallType Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Project Project { get; set; } = default!;
    public User_Hr User_Hr { get; set; } = default!;
}
