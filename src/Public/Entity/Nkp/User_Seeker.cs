﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;
using NetTopologySuite.Geometries;

namespace Entity.Nkp;

/// <summary>
/// 求职者主表
/// </summary>
[Table("user_seeker")]
public class User_Seeker
{
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 诺聘Id(pk_uaid)
    /// </summary>
    public string NuoId { get; set; } = string.Empty;

    // /// <summary>
    // /// 微信小程序OpenId
    // /// </summary>
    // public string? WeChatAppletId { get; set; }

    // /// <summary>
    // /// 面试官小程序OpenId
    // /// </summary>
    // public string? WeChatInterviewerAppletId { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName { get; set; } = string.Empty;

    /// <summary>
    /// 地区Id
    /// </summary>
    public string? RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 坐标
    /// </summary>
    public Point Location { get; set; } = new Point(0, 0);

    /// <summary>
    /// 注册源
    /// </summary>
    public RegisterSource Source { get; set; }

    /// <summary>
    /// 渠道来源
    /// </summary>
    public long? ChannelSource { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 公众号开关通知
    /// </summary>
    public bool H5Notice { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 腾讯IM账号Id
    /// </summary>
    public string? TencentImId { get; set; }

    /// <summary>
    /// hr小程序简历二维码
    /// </summary>
    public string? HrAppletQrCode { get; set; }

    /// <summary>
    /// 面试官所在公司
    /// </summary>
    public string? InterviewerEnt { get; set; }


    public User User { get; set; } = default!;
    public User_Num User_Num { get; set; } = default!;
    public User_Extend User_Extend { get; set; } = default!;
    public User_Channel_Source User_Channel_Source { get; set; } = default!;
    public User_Resume User_Resume { get; set; } = default!;
    public User_Resume_Attach User_Resume_Attach { get; set; } = default!;
    public User_NScore User_NScore { get; set; } = default!;
    public List<User_NScore_Record> User_NScore_Record { get; set; } = default!;
    public List<User_Nscore_Order> User_Nscore_Order { get; set; } = default!;
    public User_Balance User_Balance { get; set; } = default!;
    public List<User_Withdraw> User_Withdraw { get; set; } = default!;
    public List<User_Balance_Record> User_Balance_Record { get; set; } = default!;
    public List<Qd_Hr_Channel> Qd_Hr_Channel { get; set; } = default!;
}
