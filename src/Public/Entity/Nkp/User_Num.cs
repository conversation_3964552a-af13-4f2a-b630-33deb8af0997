﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;

namespace Entity.Nkp;

/// <summary>
/// 用户各种数量表
/// </summary>
[Table("user_num")]
public class User_Num
{
    [Key]
    public string UserId { get; set; } = default!;

    /// <summary>
    /// 人才数量
    /// </summary>
    public int Talent { get; set; }

    /// <summary>
    /// 虚拟人才库数量
    /// </summary>
    public int VirtualTalent { get; set; }

    /// <summary>
    /// 用户顾问数量
    /// </summary>
    public int Adviser { get; set; }

    /// <summary>
    /// 点赞数量
    /// </summary>
    public int Likes { get; set; }


    public User User { get; set; } = default!;
}