﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp
{
    /// <summary>
    /// 虚拟人才库导入记录子表
    /// </summary>
    [Table("talent_upload_recordsub")]
    public class Talent_Upload_Recordsub
    {
        [Key]
        public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

        /// <summary>
        /// 导入记录id
        /// </summary>
        public string RecordId { get; set; } = default!;

        /// <summary>
        /// 虚拟人才库id
        /// </summary>
        public string VirtualId { get; set; } = default!;

        /// <summary>
        /// 是否入库
        /// </summary>
        public bool IsWarehousing { get; set; }

        /// <summary>
        /// 是否重复
        /// </summary>
        public bool IsRepeat { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; } = default!;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        public Talent_Upload_Record Talent_Upload_Record { get; set; } = default!;

        public Talent_Virtual Talent_Virtual { get; set; } = default!;
    }
}
