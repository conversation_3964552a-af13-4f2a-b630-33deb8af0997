﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;

namespace Entity.Nkp
{
    /// <summary>
    /// 人才库简历表
    /// </summary>
    [Table("talent_platform")]
    public class Talent_Platform
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Key]
        public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

        /// <summary>
        /// HrId
        /// </summary>
        public string HrId { get; set; } = default!;

        /// <summary>
        /// 求职者id
        /// </summary>
        public string? SeekerId { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public HrProjectSource Source { get; set; }

        /// <summary>
        /// 用户等级
        /// </summary>
        public TalentPlatformLevel Level { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public ActiveStatus Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = default!;

        /// <summary>
        /// 渠道Id
        /// </summary>
        public string? ChannelId { get; set; }

        /// <summary>
        /// 删除
        /// </summary>
        public bool Deleted { get; set; }

        /// <summary>
        /// 求职者访问时间
        /// </summary>
        public DateTime SeekerVisitTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;


        public User_Hr User_Hr { get; set; } = default!;
        public User_Seeker User_Seeker { get; set; } = default!;
        public List<Talent_Label> Talent_Label = default!;
    }
}
