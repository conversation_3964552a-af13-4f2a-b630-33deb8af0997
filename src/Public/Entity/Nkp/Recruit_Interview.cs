﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;
using Yitter.IdGenerator;

namespace Entity.Nkp;

/// <summary>
/// 招聘面试表
/// </summary>
[Table("recruit_interview")]
public class Recruit_Interview
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string InterviewId { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 招聘id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// HrId，冗余
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 求职者Id，冗余
    /// </summary>
    public string SeekerId { get; set; } = default!;

    /// <summary>
    /// 面试官Id
    /// </summary>
    public string? InterviewerId { get; set; } = string.Empty;

    /// <summary>
    /// 面试过程
    /// </summary>
    public RecruitInterviewProcess Process { get; set; }

    /// <summary>
    /// 面试结果
    /// </summary>
    public RecruitInterviewOutcome Outcome { get; set; }

    /// <summary>
    /// 面试形式
    /// </summary>
    public RecruitInterviewForms Forms { get; set; }

    /// <summary>
    /// 面试用户反馈
    /// </summary>
    public RecruitInterviewUserFeedBack UserFeedBack { get; set; }

    /// <summary>
    /// 面试时间
    /// </summary>
    public DateTime InterviewTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = string.Empty;

    /// <summary>
    /// 面试地点地址
    /// </summary>
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// 面试地点纬度
    /// </summary>
    public decimal Lat { get; set; }

    /// <summary>
    /// 面试地点经度
    /// </summary>
    public decimal Lng { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 短信内容
    /// </summary>
    public string MobileContent { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string Mail { get; set; } = string.Empty;

    /// <summary>
    /// 邮件内容
    /// </summary>
    public string MailContent { get; set; } = string.Empty;

    /// <summary>
    /// 备注
    /// </summary>
    public string Remarks { get; set; } = string.Empty;

    /// <summary>
    /// 取消面试用户名
    /// </summary>
    public string? CancelName { get; set; }

    /// <summary>
    /// 面试官反馈时间
    /// </summary>
    public DateTime? TodoTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public User_Seeker User_Seeker { get; set; } = default!;

    public Project_Interviewer Project_Interviewer { get; set; } = default!;

    public Recruit Recruit { get; set; } = default!;
}

