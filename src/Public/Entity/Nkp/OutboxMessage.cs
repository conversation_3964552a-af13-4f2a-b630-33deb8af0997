using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Entity.Nkp;

/// <summary>
/// 本地消息表
/// </summary>
[Table("outbox_message")]
public class OutboxMessage
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    /// <summary>
    /// 消息类型
    /// </summary>
    public string MessageType { get; set; } = default!;

    /// <summary>
    /// 消息内容（JSON格式）
    /// </summary>
    public string Content { get; set; } = default!;

    /// <summary>
    /// 目标API类型
    /// </summary>
    public string ApiType { get; set; } = default!;

    /// <summary>
    /// 消息状态
    /// </summary>
    public OutboxMessageStatus Status { get; set; } = OutboxMessageStatus.Pending;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 下次重试时间
    /// </summary>
    public DateTime? NextRetryTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime? ProcessedTime { get; set; }
    
    /// <summary>
    /// 订单号
    /// </summary>
    public string? OrderNo { get; set; }
}

/// <summary>
/// 消息状态枚举
/// </summary>
public enum OutboxMessageStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 处理中
    /// </summary>
    Processing = 1,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 2,

    /// <summary>
    /// 失败
    /// </summary>
    Failed = 3
}
