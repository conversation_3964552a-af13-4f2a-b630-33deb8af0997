﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 钉钉用户部门关系
/// </summary>
[Table("dd_user_dept")]
public class Dd_User_Dept
{
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 钉钉用户Id
    /// </summary>
    public string DdUserId { get; set; } = default!;

    /// <summary>
    /// 钉钉部门Id
    /// </summary>
    public long? DdDeptId { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Dd_User Dd_User { get; set; } = default!;
    public Dd_Dept Dd_Dept { get; set; } = default!;
}