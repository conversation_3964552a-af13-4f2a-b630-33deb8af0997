﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 行政地区
/// </summary>
[Table("dic_region")]
public class Dic_Region
{
    [Key]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 上级Id
    /// </summary>
    public string ParentId { get; set; } = string.Empty;

    /// <summary>
    /// 区
    /// </summary>
    public string County { get; set; } = default!;

    /// <summary>
    /// 市
    /// </summary>
    public string City { get; set; } = default!;

    /// <summary>
    /// 省
    /// </summary>
    public string Province { get; set; } = default!;

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }
}