﻿using Config.Enums.Nkp;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 虚拟人才库导入记录
/// </summary>
[Table("talent_upload_record")]
public class Talent_Upload_Record
{
    [Key]
    public string Id { get; set; }=  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// HrId
    /// </summary>
    public string HrId { get; set; } = default!;

    /// <summary>
    /// 文件地址
    /// </summary>
    public string FileUrl { get; set; }=default!;

    /// <summary>
    /// 文件名称
    /// </summary>
    public string FileName { get; set; } = default!;

    /// <summary>
    /// 文件扩展名
    /// </summary>
    public TalentUpLoadFileExtension FileExtension { get; set; }

    /// <summary>
    /// 重复类型
    /// </summary>
    public TalentUpLoadRepeatType RepeatType { get; set; }

    /// <summary>
    /// 导入状态
    /// </summary>
    public TalentUpLoadStatus UpLoadStatus { get; set; }

    /// <summary>
    /// 简历总数量
    /// </summary>
    public int TotalNumber { get; set; } = 0;

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailNumber { get; set; } = 0;

    /// <summary>
    /// 合格数量
    /// </summary>
    public int QualifiedNumber { get; set; } = 0;

    /// <summary>
    /// 重复数量
    /// </summary>
    public int RepeatNumber { get; set; } = 0;

    /// <summary>
    /// 入库数量
    /// </summary>
    public int WarehousingNumber { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public User_Hr User_Hr { get; set; } = default!;
}

