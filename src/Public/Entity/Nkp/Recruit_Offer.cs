﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entity.Nkp;

/// <summary>
/// 招聘流程Offer表
/// </summary>
[Table("recruit_offer")]
public class Recruit_Offer
{
    /// <summary>
    /// 主键id
    /// </summary>
    [Key]
    public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 招聘id
    /// </summary>
    public string RecruitId { get; set; } = default!;

    /// <summary>
    /// 入职薪资
    /// </summary>
    public int InductionSalary { get; set; }

    /// <summary>
    /// 预期入职时间
    /// </summary>
    public DateTime InductionTime { get; set; }

    /// <summary>
    /// 地区Id
    /// </summary>
    public string RegionId { get; set; } = default!;

    /// <summary>
    /// 入职地点地址
    /// </summary>
    public string Address { get; set; } = default!;

    /// <summary>
    /// 入职地点纬度
    /// </summary>
    public decimal Lat { get; set; }

    /// <summary>
    /// 入职地点经度
    /// </summary>
    public decimal Lng { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; } = default!;

    /// <summary>
    /// 短信内容
    /// </summary>
    public string MobileContent { get; set; } = default!;

    /// <summary>
    /// 是否已短信通知
    /// </summary>
    public bool IsMobileNotice { get; set; } = default!;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    public string Mail { get; set; } = default!;

    /// <summary>
    /// 邮件内容
    /// </summary>
    public string MailContent { get; set; } = default!;

    /// <summary>
    /// 通知主体id
    /// </summary>
    public string? NoticeSubjectId { get; set; }

    /// <summary>
    /// 通知主体名称
    /// </summary>
    public string? NoticeSubjectName { get; set; }

    /// <summary>
    /// 通知书内容
    /// </summary>
    public string? NoticeContent { get; set; }

    /// <summary>
    /// 携带资料集合
    /// </summary>
    public string? CarryInformation { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Recruit Recruit { get; set; } = default!;
}

