﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

[Table("ums_bnfnamelistqry")]
public class Ums_BnfNameListQry
{
    /// <summary>
    /// 统一社会信用代码
    /// </summary>
    [Key]
    public string ProtocolNo { get; set; } = default!;

    /// <summary>
    /// 控股股东名称列表,List<string>的JSON字符串
    /// </summary>
    public List<string> ShareholderNameList { get; set;} = default!;

    /// <summary>
    /// 受益人名称列表,List<string>的JSON字符串
    /// </summary>
    public List<string> BnfNameList { get; set; } = default!;

    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public DateTime UpdatedTime { get; set; } = DateTime.Now;
}
