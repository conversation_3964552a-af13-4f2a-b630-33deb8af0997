﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp
{
    /// <summary>
    /// admin
    /// </summary>
    [Table("admin")]
    public class Admin
    {
        [Key]
        public string Id { get; set; } =  Entity.EntityTools.SnowflakeId();

        public string Account { get; set; } = default!;

        public string Password { get; set; } = default!;
        public List<string> Powers { get; set; } = new List<string>();
        public string? Name { get; set; }

        public ActiveStatus Status { get; set; } = default!;
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }
}
