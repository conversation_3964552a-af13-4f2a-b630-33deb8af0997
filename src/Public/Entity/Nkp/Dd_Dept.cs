﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 钉钉部门
/// </summary>
[Table("dd_dept")]
public class Dd_Dept
{
    [Key]
    public long DdDeptId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 父Id
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    public string Level { get; set; } = string.Empty;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    public DateTime UpdatedTime { get; set; } = DateTime.Now;

    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Dd_Dept? Parent { get; set; }
}