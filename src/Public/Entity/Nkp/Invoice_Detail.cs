﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Nkp;

/// <summary>
/// 开发票关联的动账记录明细表
/// </summary>
[Table("invoice_detail")]
public class Invoice_Detail
{
    [Key]
    public string Id { get; set; } = Entity.EntityTools.SnowflakeId();

    /// <summary>
    /// 记录ID
    /// </summary>
    public string RecordId { get; set; } = default!;

    /// <summary>
    /// 动账记录ID
    /// </summary>
    public string UmsTransId { get; set; } = default!;

    public Invoice_Record Invoice_Record { get; set; } = default!;

    public Ums_Callback_Trans Ums_Callback_Trans { get; set; } = default!;
}
