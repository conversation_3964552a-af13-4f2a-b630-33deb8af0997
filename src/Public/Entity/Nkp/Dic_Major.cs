﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Nkp;

/// <summary>
/// 学校专业
/// </summary>
[Table("dic_major")]
public class Dic_Major
{
    [Key]
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// Level2Name
    /// </summary>
    public string Level2Name { get; set; } = string.Empty;

    /// <summary>
    /// Level3Name
    /// </summary>
    public string CiLevel3Namey { get; set; } = string.Empty;

    /// <summary>
    /// 类别,0=专科，1=本科
    /// </summary>
    public MajorType Type { get; set; }

    /// <summary>
    /// 几年
    /// </summary>
    public string Year { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public ActiveStatus Status { get; set; }
}


public enum MajorType
{
    专科, 本科
}