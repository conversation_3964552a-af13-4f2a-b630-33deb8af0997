using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 工作组用户
/// </summary>
public class WorkGroupUser
{
    /// <summary>
    /// 关联ID
    /// </summary>
    [Key]
    public string PK_WGSLID { get; set; } = default!;
    /// <summary>
    /// 工作组ID
    /// </summary>
    public string? PK_WGID { get; set; }
    /// <summary>
    /// 求职者ID
    /// </summary>
    public string? PK_JHID { get; set; }
    /// <summary>
    /// 用户账号ID
    /// </summary>
    public string? PK_AID { get; set; }
    /// <summary>
    /// 企业id
    /// </summary>
    public string? PK_EAID { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    public string? UserName { get; set; }
    /// <summary>
    /// 身份证
    /// </summary>
    public string? IDCard { get; set; }
    /// <summary>
    /// 联系电话
    /// </summary>
    public string? TelePhone { get; set; }
    /// <summary>
    /// 入职时间
    /// </summary>
    public DateTime? EntryTime { get; set; }
    /// <summary>
    /// 离职时间
    /// </summary>
    public DateTime? LeaveTime { get; set; }
    /// <summary>
    /// 员工状态(等待审核=0, 拒绝加入=1, 已加入=2, 拒绝入职=3, 在职=4, 离职=5)
    /// </summary>
    public int? State { get; set; }
    /// <summary>
    /// 入职审核结果(驳回的原因)
    /// </summary>
    public string? AuditResult { get; set; }
    /// <summary>
    /// 员工来源
    /// </summary>
    public int? FromSource { get; set; }
    /// <summary>
    /// E签宝的账号ID
    /// </summary>
    public string? ESignAccountId { get; set; }
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    public WorkGroup WorkGroup { get; set; } = default!;

    public JobHunter JobHunter { get; set; } = default!;
}