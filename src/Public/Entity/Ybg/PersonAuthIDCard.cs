using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 用户IdCard
/// </summary>
public class PersonAuthIDCard
{
    /// <summary>
    /// Id
    /// </summary>
    [Key]
    public string PK_PCAID { get; set; } = default!;
    /// <summary>
    /// 
    /// </summary>
    public string? PK_JHID { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? IDCardName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? IDCardNo { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? IDCardImageFront { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? IDCardImageBack { get; set; }
    
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
}