using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 工作组
/// </summary>
public class WorkGroup
{
    /// <summary>
    /// 工作组ID
    /// </summary>
    [Key]
    public string PK_WGID { get; set; } = default!;
    /// <summary>
    /// 项目编号 (直接使用数字诺亚项目编号)
    /// </summary>
    public string? WGNumber { get; set; }
    /// <summary>
    /// 用户账号ID
    /// </summary>
    public string? PK_AID { get; set; }
    /// <summary>
    /// 企业id
    /// </summary>
    public string? PK_EAID { get; set; }
    /// <summary>
    /// 分公司id (合同模板和签章)
    /// </summary>
    public string? PK_WGFID { get; set; }
    /// <summary>
    /// 用工单位 (数字诺亚用工单位)
    /// </summary>
    public string? WGName { get; set; }
    /// <summary>
    /// 二级单位
    /// </summary>
    public string? Department { get; set; }
    /// <summary>
    /// 合同名称 (数字诺亚合同名称-用于区分同一用工单位的工作组)
    /// </summary>
    public string? ContractName { get; set; }
    /// <summary>
    /// 组状态（0=正常 1=结束）
    /// </summary>
    public int? WGState { get; set; }
    /// <summary>
    /// 关联项目编号 (数字诺亚项目编号)
    /// </summary>
    public string? RelateNumber { get; set; }
    /// <summary>
    /// 服务专员电话
    /// </summary>
    public string? ServicePhone { get; set; }
    /// <summary>
    /// 是否需要入职审核 (默认都需要-1)
    /// </summary>
    public int? IsEntryAudit { get; set; }
    /// <summary>
    /// 是否可以扫码进入 (默认都可以-1)
    /// </summary>
    public int? IsScanCode { get; set; }
    /// <summary>
    /// 同步数据到其它平台 (默认都同步-1)
    /// </summary>
    public int? IsSyncToOther { get; set; }
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 类型 1全日制 0非全日制
    /// </summary>
    public int Type { get; set; }
    /// <summary>
    /// 自动离职 0否1是
    /// </summary>
    public int AutoQuit { get; set; }
}