using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 分公司
/// </summary>
public class WorkGroupFiliale
{
    /// <summary>
    /// Id
    /// </summary>
    [Key]
    public string PK_WGFID { get; set; } = default!;
    /// <summary>
    /// 分公司名称
    /// </summary>
    public string? EName { get; set; }
        /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
}