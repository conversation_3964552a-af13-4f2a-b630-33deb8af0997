using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 账号表
/// </summary>
public class AccountSystem
{
    /// <summary>
    /// Id
    /// </summary>
    [Key]
    public string PK_AID { get; set; } = default!;

    /// <summary>
    /// 姓名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string? NickName { get; set; }
    public string? LoginName { get; set; }
    public string? LoginPhone { get; set; }
    public string? LoginPwd { get; set; }
    public string? PK_PAID { get; set; }
    public string? PK_EAID { get; set; }

    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
}