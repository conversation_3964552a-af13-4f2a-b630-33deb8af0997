using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 
/// </summary>
public class WorkGroupEntry
{
    /// <summary>
    /// 入职信息的ID
    /// </summary>
    [Key]
    public string PK_WGEID { get; set; } = default!;
    /// <summary>
    /// 关联加入工作组的ID--User表
    /// </summary>
    public string? PK_WGSLID { get; set; }
    /// <summary>
    /// 所属工作组
    /// </summary>
    public string? PK_WGID { get; set; }
    /// <summary>
    /// 人员ID
    /// </summary>
    public string? PK_JHID { get; set; }
    /// <summary>
    /// 用户账号ID
    /// </summary>
    public string? PK_AID { get; set; }
    /// <summary>
    /// 入职告知书标题
    /// </summary>
    public string? EntryLetterTitle { get; set; }
    /// <summary>
    /// 入职告知书
    /// </summary>
    public string? EntryLetter { get; set; }
    /// <summary>
    /// 性别
    /// </summary>
    public int? Sex { get; set; }
    /// <summary>
    /// 民族
    /// </summary>
    public string? Nation { get; set; }
    /// <summary>
    /// 政治面貌
    /// </summary>
    public int? Politic { get; set; }
    /// <summary>
    /// 生日
    /// </summary>
    public DateTime? Birthday { get; set; }
    /// <summary>
    /// 省
    /// </summary>
    public string? ProvinceNo { get; set; }
    /// <summary>
    /// 省名称
    /// </summary>
    public string? ProvinceName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? CityNo { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? CityName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? DistrictNo { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? DistrictName { get; set; }
    /// <summary>
    /// 地址详情
    /// </summary>
    public string? Address { get; set; }
    /// <summary>
    /// 邮编
    /// </summary>
    public string? ZipCode { get; set; }
    /// <summary>
    /// 最高学历
    /// </summary>
    public string? PK_EID { get; set; }
    /// <summary>
    /// 毕业时间
    /// </summary>
    public DateTime? GraduationDate { get; set; }
    /// <summary>
    /// 学校名称
    /// </summary>
    public string? SchoolName { get; set; }
    /// <summary>
    /// 所学专业
    /// </summary>
    public string? SchoolSpecialty { get; set; }
    /// <summary>
    /// 家庭成员信息json数组
    /// </summary>
    public string? Family { get; set; }
    /// <summary>
    /// 户口性质
    /// </summary>
    public int? HouseholdType { get; set; }
    /// <summary>
    /// 入职资料审核状态
    /// </summary>
    public int? Status { get; set; }
    /// <summary>
    /// 签名的图片
    /// </summary>
    public string? UserSign { get; set; }
    /// <summary>
    /// 资料提交时间
    /// </summary>
    public DateTime? DatumSubmitTime { get; set; }
    /// <summary>
    /// 删除标识
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public DateTime? CreateTime { get; set; }
}