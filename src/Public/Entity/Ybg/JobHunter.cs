using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 求职者
/// </summary>
public class JobHunter
{
    /// <summary>
    /// Id
    /// </summary>
    [Key]
    public string PK_JHID { get; set; } = default!;
    /// <summary>
    /// 账号(手机号)
    /// </summary>
    public string? Phone { get; set; }
    /// <summary>
    /// 密码
    /// </summary>
    public string? PassWord { get; set; }
    /// <summary>
    /// 昵称
    /// </summary>
    public string? JHNickName { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    public string? JHName { get; set; }
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IDCard { get; set; }
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
}