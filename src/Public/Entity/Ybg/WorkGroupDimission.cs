using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 
/// </summary>
public class WorkGroupDimission
{
    /// <summary>
    /// 离职的ID
    /// </summary>
    [Key]
    public string PK_WGDID { get; set; } = default!;
    /// <summary>
    /// 关联加入工作组的ID--User表
    /// </summary>
    public string? PK_WGSLID { get; set; }
    /// <summary>
    /// 所属工作组
    /// </summary>
    public string? PK_WGID { get; set; }
    /// <summary>
    /// 人员ID
    /// </summary>
    public string? PK_JHID { get; set; }
    /// <summary>
    /// 用户账号ID
    /// </summary>
    public string? PK_AID { get; set; }
    /// <summary>
    /// 合同模板信息ID
    /// </summary>
    public string? PK_WGCID { get; set; }
    /// <summary>
    /// 离职告知书是否已发送：0未发送、1已发送
    /// </summary>
    public int? IsLetterSend { get; set; }
    /// <summary>
    /// 离职告知书标题
    /// </summary>
    public string? DimissionLetterTitle { get; set; }
    /// <summary>
    /// 离职告知书内容
    /// </summary>
    public string? DimissionLetter { get; set; }
    /// <summary>
    /// 离职办理状态：正在办理、已完成
    /// </summary>
    public int? Status { get; set; }
    /// <summary>
    /// 签名的图片
    /// </summary>
    public string? UserSign { get; set; }
    /// <summary>
    /// 签署时间
    /// </summary>
    public DateTime? SignTime { get; set; }
    /// <summary>
    /// 参加工作时间
    /// </summary>
    public DateTime? WorkTime { get; set; }
    /// <summary>
    /// 合同签订日期
    /// </summary>
    public DateTime? ContractSignTime { get; set; }
    /// <summary>
    /// 合同解除日期
    /// </summary>
    public DateTime? ContractExpireTime { get; set; }
    /// <summary>
    /// 在本单位工作年限
    /// </summary>
    public string? StandardTime { get; set; }
    /// <summary>
    /// 单位工种或专业
    /// </summary>
    public string? Production { get; set; }
    /// <summary>
    /// 经济补偿金
    /// </summary>
    public string? Compensation { get; set; }
    /// <summary>
    /// 合同终止原因
    /// </summary>
    public string? DimissionReason { get; set; }
    /// <summary>
    /// 其他证明情况
    /// </summary>
    public string? OtherSupport { get; set; }
    /// <summary>
    /// 单位意见
    /// </summary>
    public string? CompanyComment { get; set; }
    /// <summary>
    /// 离职合同信息是否已完善
    /// </summary>
    public int? IsInfoPerfect { get; set; }
    /// <summary>
    /// 资料提交时间
    /// </summary>
    public DateTime? DatumSubmitTime { get; set; }
    /// <summary>
    /// 合同签署状态：0未签署、1已签署、5未发送
    /// </summary>
    public int? ContractStatus { get; set; }
    /// <summary>
    /// E签宝流程ID
    /// </summary>
    public string? FlowId { get; set; }
    /// <summary>
    /// 合同的地址
    /// </summary>
    public string? ContractUrl { get; set; }
    /// <summary>
    /// 数字诺亚同步状态 0=未同步,1=已同步
    /// </summary>
    public int? NoahSyncStatus { get; set; }
    /// <summary>
    /// 是否最近期一份合同：1是，0否
    /// </summary>
    public int? IsLatest { get; set; }
    /// <summary>
    /// 是否线下合同(否=0, 是=1)
    /// </summary>
    public int? IsOffline { get; set; }
    /// <summary>
    /// 删除标识
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public DateTime? CreateTime { get; set; }

    public WorkGroupUser WorkGroupUser { get; set; } = default!;

    public WorkGroupContractTemplate WorkGroupContractTemplate { get; set; } = default!;
}