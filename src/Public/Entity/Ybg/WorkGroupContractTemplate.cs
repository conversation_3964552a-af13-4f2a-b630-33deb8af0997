using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 合同模板
/// </summary>
public class WorkGroupContractTemplate
{
    /// <summary>
    /// ID
    /// </summary>
    [Key]
    public string PK_WGCID { get; set; } = default!;

    /// <summary>
    /// 分公司信息ID
    /// </summary>
    public string? PK_WGFID { get; set; }

    /// <summary>
    /// 合同类型：入职、续签、离职
    /// </summary>
    public WorkGroupContractTemplateCType? CType { get; set; }
    /// <summary>
    /// 合同模板名称
    /// </summary>
    public string? CName { get; set; }
    /// <summary>
    /// 合同文件名称（带后缀）
    /// </summary>
    public string? ContractFile { get; set; }
    /// <summary>
    /// 合同地址
    /// </summary>
    public string? UploadUrl { get; set; }
    /// <summary>
    /// 删除(0=正常 1=删除)
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    public WorkGroupFiliale WorkGroupFiliale { get; set; } = default!;
}

/// <summary>
/// 合同模板类型
/// </summary>
public enum WorkGroupContractTemplateCType
{
    入职 = 1,
    续签 = 2,
    离职 = 3
}