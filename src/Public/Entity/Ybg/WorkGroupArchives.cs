using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 
/// </summary>
public class WorkGroupArchives
{
    /// <summary>
    /// 档案保险ID
    /// </summary>
    [Key]
    public string PK_WGIRID { get; set; } = default!;
    /// <summary>
    /// 关联加入工作组的ID
    /// </summary>
    public string? PK_WGSLID { get; set; }
    /// <summary>
    /// 所属工作组
    /// </summary>
    public string? PK_WGID { get; set; }
    /// <summary>
    /// 人员ID
    /// </summary>
    public string? PK_JHID { get; set; }
    /// <summary>
    /// 用户账号ID
    /// </summary>
    public string? PK_AID { get; set; }
    /// <summary>
    /// 档案情况（是否转移等）
    /// </summary>
    public int? Situation { get; set; }
    /// <summary>
    /// 原档案存地
    /// </summary>
    public string? OriginalRecordSite { get; set; }
    /// <summary>
    /// 原档案说明
    /// </summary>
    public string? OriginalRecordExplain { get; set; }
    /// <summary>
    /// 养老保险状态
    /// </summary>
    public int? YangLaoSituation { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? OriginalYangLaoRecordSite { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? OriginalYangLaoRecordExplain { get; set; }
    /// <summary>
    /// 医疗保险是否需要转移
    /// </summary>
    public int? MedicalSituation { get; set; }
    /// <summary>
    /// 医疗保险类型
    /// </summary>
    public int? MedicalType { get; set; }
    /// <summary>
    /// 原保险地
    /// </summary>
    public string? OriginalMedicalRecordSite { get; set; }
    /// <summary>
    /// 说明
    /// </summary>
    public string? OriginalMedicalRecordExplain { get; set; }
    /// <summary>
    /// 公积金办理类型
    /// </summary>
    public int? ProvidentSituation { get; set; }
    /// <summary>
    /// 原保险地
    /// </summary>
    public string? OriginalProvidentRecordSite { get; set; }
    /// <summary>
    /// 说明
    /// </summary>
    public string? OriginalProvidentRecordExplain { get; set; }
    /// <summary>
    /// 是否是退役军人
    /// </summary>
    public int? IsSoldier { get; set; }
    /// <summary>
    /// 退役军人证件
    /// </summary>
    public string? SoldierVoucher { get; set; }
    /// <summary>
    /// 是否是残疾证
    /// </summary>
    public int? IsDisability { get; set; }
    /// <summary>
    /// 残疾证件
    /// </summary>
    public string? DisabilityVoucher { get; set; }
    /// <summary>
    /// 是否有就业失业证
    /// </summary>
    public int? IsEmployment { get; set; }
    /// <summary>
    /// 就业失业证图片url
    /// </summary>
    public string? EmploymentVoucher { get; set; }
    /// <summary>
    /// 工资账号
    /// </summary>
    public string? SalaryAccount { get; set; }
    /// <summary>
    /// 工资银行卡银行名称
    /// </summary>
    public string? SalaryAccountBankName { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int? Status { get; set; }
    /// <summary>
    /// 删除标识
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public DateTime? CreateTime { get; set; }
}