using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums.Nkp;

namespace Entity.Ybg;

/// <summary>
/// 
/// </summary>
public class WorkGroupEmployeeContract
{
    /// <summary>
    /// 员工合同信息ID
    /// </summary>
    [Key]
    public string PK_WGECID { get; set; } = default!;
    /// <summary>
    /// 关联加入工作组的ID--User表
    /// </summary>
    public string? PK_WGSLID { get; set; }
    /// <summary>
    /// 所属工作组
    /// </summary>
    public string? PK_WGID { get; set; }
    /// <summary>
    /// 用户账号ID
    /// </summary>
    public string? PK_AID { get; set; }
    /// <summary>
    /// 人员ID
    /// </summary>
    public string? PK_JHID { get; set; }
    /// <summary>
    /// 职务
    /// </summary>
    public string? Duty { get; set; }
    /// <summary>
    /// 合同持续类型：固定期限、不固定期限
    /// </summary>
    public int? ContractDurationType { get; set; }
    /// <summary>
    /// 合同签订日期
    /// </summary>
    public DateTime? ContractSignTime { get; set; }
    /// <summary>
    /// 合同到期日期
    /// </summary>
    public DateTime? ContractExpireTime { get; set; }
    /// <summary>
    /// 试用期
    /// </summary>
    public int? TrialPeriod { get; set; }
    /// <summary>
    /// 用人单位-实际用工单位=工作组的名称
    /// </summary>
    public string? Organization { get; set; }
    /// <summary>
    /// 用人部门-用人单位的具体部门
    /// </summary>
    public string? Department { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? WorkSiteProvinceNo { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public string? WorkSiteCityNo { get; set; }
    /// <summary>
    /// 工作地点省
    /// </summary>
    public string? WorkSiteProvinceName { get; set; }
    /// <summary>
    /// 工作地点市
    /// </summary>
    public string? WorkSiteCityName { get; set; }
    /// <summary>
    /// 工作地点 (手动输入时忽略省市)
    /// </summary>
    public string? WorkSite { get; set; }
    /// <summary>
    /// 合同信息是否已完善 (未完善=0, 已完善=1)
    /// </summary>
    public int? IsInfoPerfect { get; set; }
    /// <summary>
    /// 合同签署状态：0未签署、1已签署、5未发送
    /// </summary>
    public int? ContractStatus { get; set; }
    /// <summary>
    /// 签署时间
    /// </summary>
    public DateTime? SignTime { get; set; }
    /// <summary>
    /// E签宝流程ID
    /// </summary>
    public string? FlowId { get; set; }
    /// <summary>
    /// 合同的地址
    /// </summary>
    public string? ContractUrl { get; set; }
    /// <summary>
    /// 数字诺亚同步状态
    /// </summary>
    public int? NoahSyncStatus { get; set; }
    /// <summary>
    /// 合同类型：入职、续签
    /// </summary>
    public int? CType { get; set; }
    /// <summary>
    /// 续签次数：0为入职
    /// </summary>
    public int? CNumber { get; set; }
    /// <summary>
    /// 是否最近一次的合同：1是，0否
    /// </summary>
    public int? IsLastest { get; set; }
    /// <summary>
    /// 是否线下合同(否=0, 是=1)
    /// </summary>
    public int? IsOffline { get; set; }

    /// <summary>
    /// 报酬方式
    /// </summary>
    public string? RewardType { get; set; }

    /// <summary>
    /// 报酬信息
    /// </summary>
    public string? RewardValue { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? Sort { get; set; }
    /// <summary>
    /// 删除标识
    /// </summary>
    public int? IsRemove { get; set; }
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 合同模板信息ID
    /// </summary>
    public string? PK_WGCID { get; set; }

    public WorkGroupUser WorkGroupUser { get; set; } = default!;


    public WorkGroupContractTemplate WorkGroupContractTemplate { get; set; } = default!;

    public AccountSystem AccountSystem { get; set; } = default!;
}