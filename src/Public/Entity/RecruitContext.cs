﻿using System;
using Entity;
using Microsoft.EntityFrameworkCore;

namespace Entity;

public partial class RecruitContext : DbContext
{
    public RecruitContext(DbContextOptions<RecruitContext> options)
        : base(options)
    {

    }

    public virtual DbSet<WorkPosition> WorkPosition { get; set; } = default!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
    }
}