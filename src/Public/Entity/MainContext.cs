using Entity.Main;
using EntityFrameworkCore.AutoHistory.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using ServiceStack.Text;
using Yitter.IdGenerator;

namespace Entity;

public partial class MainContext : DbContext
{
    public MainContext(DbContextOptions<MainContext> options)
        : base(options)
    {

    }

    public virtual DbSet<Token_Refresh> Token_Refresh { get; set; } = default!;
    public virtual DbSet<Token_Access> Token_Access { get; set; } = default!;
    public virtual DbSet<Admin> Admin { get; set; } = default!;
    public virtual DbSet<Admin_Record> Admin_Record { get; set; } = default!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        //启用AutoHistory
        modelBuilder.EnableAutoHistory();

        // modelBuilder.Entity<xxx>().HasQueryFilter(b => !b.Deleted);

        modelBuilder.Entity<Token_Access>()
        .HasOne(x => x.Token_Refresh)
        .WithMany()
        .HasForeignKey(x => x.RefreshTokenId);

        modelBuilder.Entity<Admin_Record>()
        .HasOne(x => x.Admin)
        .WithMany()
        .HasForeignKey(x => x.UserId);

        PropertConvert(modelBuilder);
    }

    private void PropertConvert(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Admin>()
        .Property(x => x.Powers)
        .HasJsonConversion();
    }
}

public class EntityTools
{
    public static string SnowflakeId()
    {
        return YitIdHelper.NextId().ToString();
    }
}

public static partial class EntityFrameworkExtend
{
    public static PropertyBuilder<T> HasJsonConversion<T>(this PropertyBuilder<T> propertyBuilder) where T : class, new()
    {
        ValueConverter<T, string> converter = new ValueConverter<T, string>
        (
            v => JsonSerializer.SerializeToString(v) ?? string.Empty,
            v => JsonSerializer.DeserializeFromString<T>(v) ?? new T()
        );
        ValueComparer<T> comparer = new ValueComparer<T>
        (
            (l, r) => JsonSerializer.SerializeToString(l) == JsonSerializer.SerializeToString(r),
            v => v == null ? 0 : JsonSerializer.SerializeToString(v).GetHashCode(),
            v => JsonSerializer.DeserializeFromString<T>(JsonSerializer.SerializeToString(v))
        );
        propertyBuilder.HasConversion(converter);
        propertyBuilder.Metadata.SetValueConverter(converter);
        propertyBuilder.Metadata.SetValueComparer(comparer);
        // propertyBuilder.HasColumnType("jsonb");
        return propertyBuilder;
    }

    public static PropertyBuilder<T?> HasJsonConversionNullable<T>(this PropertyBuilder<T?> propertyBuilder) where T : class, new()
    {
        var converter = new ValueConverter<T, string>
        (
            v => JsonSerializer.SerializeToString(v) ?? string.Empty,
            v => JsonSerializer.DeserializeFromString<T>(v) ?? new T()
        );
        var comparer = new ValueComparer<T>
        (
            (l, r) => JsonSerializer.SerializeToString(l) == JsonSerializer.SerializeToString(r),
            v => v == null ? 0 : JsonSerializer.SerializeToString(v).GetHashCode(),
            v => JsonSerializer.DeserializeFromString<T>(JsonSerializer.SerializeToString(v))
        );
        propertyBuilder.HasConversion(converter!);
        propertyBuilder.Metadata.SetValueConverter(converter);
        propertyBuilder.Metadata.SetValueComparer(comparer);
        // propertyBuilder.HasColumnType("jsonb");
        return propertyBuilder;
    }
}