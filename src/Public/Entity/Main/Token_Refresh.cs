using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.Enums;

namespace Entity.Main;

/// <summary>
/// 刷新token
/// </summary>
[Table("token_refresh")]
public class Token_Refresh
{
    [Key]
    public long Id { get; set; }

    public string UserId { get; set; } = default!;

    public TokenType Type { get; set; }

    public ClientType? Client { get; set; }

    public string Token { get; set; } = default!;

    public DateTime ExpirationTime { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;
}