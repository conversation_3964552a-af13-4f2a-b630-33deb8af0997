using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config;
using Config.Enums;

namespace Entity.Main;

/// <summary>
/// accesstoken
/// </summary>
[Table("token_access")]
public class Token_Access
{
    [Key]
    public long Id { get; set; }

    public long RefreshTokenId { get; set; }

    public string UserId { get; set; } = default!;

    public TokenType Type { get; set; }

    public ClientType? Client { get; set; }

    public string Token { get; set; } = default!;

    public DateTime ExpirationTime { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;


    public Token_Refresh Token_Refresh { get; set; } = default!;
}