using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Config.Enums;

namespace Entity.Main;

/// <summary>
/// 管理员操作记录
/// </summary>
[Table("admin_record")]
public class Admin_Record
{
    [Key]
    public string Id { get; set; } = EntityTools.SnowflakeId();

    public string UserId { get; set; } = default!;

    /// <summary>
    /// 类型
    /// </summary>
    public AdminRecordType Type { get; set; } = default!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Describe { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.Now;

    public Admin Admin { get; set; } = default!;
}

public enum AdminRecordType
{
    登录,
    删除云办公用户 = 201,
    删除云办公合同 = 202,
    修改云办公合同 = 203,
    云办公项目转移 = 204
}