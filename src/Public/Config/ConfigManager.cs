﻿using System;
namespace Config;

public class ConfigManager
{
    /// <summary>
    /// redis
    /// </summary>
    public string RedisAddress { get; set; } = default!;

    /// <summary>
    /// 公共redis，解决测试环境与正式环境互刷微信token问题
    /// </summary>
    public string PublicRedisAddress { get; set; } = default!;

    /// <summary>
    /// 是否启用请求日志
    /// </summary>
    public bool RequestLog { get; set; }

    /// <summary>
    /// 腾讯云
    /// </summary>
    public TencentOptions? TencentOptions { get; set; }

    /// <summary>
    /// 测试手机号
    /// </summary>
    public string[]? TestMobiles { get; set; }

    /// <summary>
    /// 验签key
    /// </summary>
    public string? SignKey { get; set; }

    /// <summary>
    /// es配置
    /// </summary>
    public ElasticSearchConfig? ElasticSearch { get; set; }

    /// <summary>
    /// e签宝
    /// </summary>
    public ESignConfig? ESign { get; set; }

    /// <summary>
    /// 阿里云
    /// </summary>
    public AliyunConfig? Aliyun { get; set; }

    /// <summary>
    /// 服务器报警配置
    /// </summary>
    public WarningConfig? Warning { get; set; }

    /// <summary>
    /// 月斌服务器地址
    /// </summary>
    public string? DataRdsServer { get; set; }
    
    

    /// <summary>
    /// 月宾Kafka地址
    /// </summary>
    public string? DataKafkaServer { get; set; }

    /// <summary>
    /// Kafka topic
    /// </summary>
    public string? DataKafkaTopic { get; set; }

    /// <summary>
    /// 服务通信密钥
    /// </summary>
    public List<string> ServiceKeys { get; set; } = new List<string>();
    /// <summary>
    /// 银联商务签约url
    /// </summary>
    public string ChinaumsUrl { get; set; } = default!;
    /// <summary>
    /// 银联商务签约key
    /// </summary>
    public string ChinaumsKey { get; set; } = default!;
    /// <summary>
    /// 银联商务签约接入id
    /// </summary>
    public string ChinaumsAccesserId { get; set; } = default!;
    /// <summary>
    /// 诺快聘鉴权地址
    /// </summary>
    public string? StaffingServer { get; set; }
    
    /// <summary>
    /// NovaPin服务配置
    /// </summary>
    public NovaPinConfig? NovaPin { get; set; }
    
    /// <summary>
    /// 服务奖金税点配置
    /// </summary>
    public ServiceBonusTaxConfig? ServiceBonusTax { get; set; }
    
    /// <summary>
    /// 服务鉴权token
    /// </summary>
    public string StaffingToken  { get; set; } = default!;
    
    
    
    
    
    /// <summary>
    /// 银联商务天满 渠道号
    /// </summary>
    public string OpenChinaumsChannelNo { get; set; } = default!;
    
    /// <summary>
    /// 银联商务天满appId
    /// </summary>
    public string OpenChinaumsAppId { get; set; } = default!;
    
    /// <summary>
    /// 银联商务天满appId
    /// </summary>
    public string OpenChinaumsAppKey { get; set; } = default!;
    
    
    /// <summary>
    /// 银联商务天满url
    /// </summary>
    public string OpenChinaumsBaseUrl { get; set; } = default!;
    
    /// <summary>
    /// 银联商务天满平台商户号
    /// </summary>
    public string OpenChinaumsMchntNo { get; set; } = default!;
    
    /// <summary>
    /// 银联商务天满平台sm4key
    /// </summary>
    public string OpenChinaumsSM2PublicKey { get; set; } = default!;

    /// <summary>
    /// 银联商务天满平台sm4key
    /// </summary>
    public string OpenChinaumsSM2PublicKeyHex { get; set; } = default!;
    
    /// <summary>
    /// 子产品编号 银联商务要求固定死
    /// </summary>
    public string OpenChinaumsSubProdCode { get; set; } = default!;
    
    /// <summary>
    /// 诺聘平台的支付账户账户号
    /// </summary>
    public string NuoPinAccountNo { get; set; } = default!;
    
    /// <summary>
    /// 开户行名称
    /// </summary>
    public string BankName { get; set; } = default!;

    /// <summary>
    /// 分行代码
    /// </summary>
    public string BranchCode { get; set; } = default!;
 
    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; } = default!;
    
    /// <summary>
    /// 诺聘管理员工号
    /// *申请发票时需有个员工工号，服务奖金使用谁的呢
    /// </summary>
    public string NuopinAdminEmployeNo { get; set; } = default!;
    /// <summary>
    /// 诺聘管理员工姓名
    /// *申请发票时需有个员工姓名，服务奖金使用谁的呢
    /// </summary>
    public string NuopinAdminEmployeName { get; set; } = default!;
}

public class RedisServer
{
    public string? Address { get; set; }
}

public class WeChatApp
{
    public string? AppId { get; set; }
    public string? Secret { get; set; }
}

public class ESignConfig
{
    public string? Domain { get; set; }
    public string? AppId { get; set; }
    public string? Secret { get; set; }
}

public class WarningConfig
{
    public int SendInterval { get; set; }

    public List<string>? EMails { get; set; }

    /// <summary>
    /// 发件人账号
    /// </summary>
    public string? SendMail { get; set; }

    /// <summary>
    /// 发件人key
    /// </summary>
    public string? SendMailKey { get; set; }

    /// <summary>
    /// 发件人Smtp
    /// </summary>
    public string? SendMailSmtp { get; set; }
}

public class TencentOptions
{
    public string FaceIdSecretId { get; set; } = default!;
    public string FaceIdSecretKey { get; set; } = default!;

    //im
    public string ImAppId { get; set; } = default!;
    public string ImAppKey { get; set; } = default!;
}

public class AliyunConfig
{
    //public string? AccessKeyId { get; set; }
    //public string? AccessKeySecret { get; set; }

    //public AliyunOssConfig? Oss { get; set; }
    public AliyunFullAccess? FullAccess { get; set; }
    public AliyunSMS? AliSMS { get; set; }
    public AliyunOss? Oss { get; set; }
    //public AliyunAfsConfig? Afs { get; set; }
}

//public class AliyunOssConfig
//{
//    public string? AccessKeyId { get; set; }
//    public string? AccessKeySecret { get; set; }
//    public string? Bucket { get; set; }
//    public string? Endpoint { get; set; }
//    public string? Host { get; set; }
//    public int ExpirationSeconds { get; set; }
//    public string? Dir { get; set; }
//    public string? SystemDir { get; set; }
//    public long MaxSize { get; set; }
//}

public class AliyunFullAccess
{
    public string? AccessKeyId { get; set; }
    public string? AccessKeySecret { get; set; }
}

public class AliyunOss
{
    public string? Dir { get; set; }
}

public class AliyunSMS
{
    public bool Active { get; set; }
    public string? AccessKeyId { get; set; }
    public string? AccessKeySecret { get; set; }
    public string? Domain { get; set; }
    public string? Version { get; set; }
    public string? Action { get; set; }
    public string? SignName { get; set; }
    public string? TemplateCode { get; set; }
    public string? OfferTemplateCode { get; set; }
    public string? InterviewTemplateCode { get; set; }
    //public string? FacingInterviewTemplateCode { get; set; }
    //public string? UnFacingInterviewTemplateCode { get; set; }

    /// <summary>
    /// 过期时间(秒)
    /// </summary>
    /// <value>The expire seconds.</value>
    public int ExpireSeconds { get; set; }

    /// <summary>
    /// 发送间隔(秒)
    /// </summary>
    /// <value>The send interval.</value>
    public int SendInterval { get; set; }

    public int MaxEveryDay { get; set; }
}

/// <summary>
/// es配置
/// </summary>
public class ElasticSearchConfig
{
    public string? Address { get; set; }
    public string? UserName { get; set; }
    public string? Password { get; set; }
}

//public class AliyunAfsConfig
//{
//    public string? AppKey { get; set; }
//}

/// <summary>
/// NovaPin服务配置
/// </summary>
public class NovaPinConfig
{
    /// <summary>
    /// OAuth认证URL
    /// </summary>
    public string? OAuthUrl { get; set; }
    
    /// <summary>
    /// API基础URL
    /// </summary>
    public string? ApiBaseUrl { get; set; }
    
    /// <summary>
    /// 服务奖金发起流程路径
    /// </summary>
    public string? InitiateServiceBonusPath { get; set; }
    
    /// <summary>
    /// 开票流程发起路径
    /// </summary>
    public string? InitiateInvoicePath { get; set; }
}

/// <summary>
/// 服务奖金税点配置
/// </summary>
public class ServiceBonusTaxConfig
{
    /// <summary>
    /// 服务奖金税点扣除比例（小数形式，如0.06表示6%）
    /// </summary>
    public decimal TaxDeductionRate { get; set; } = 0.06m;
    
    /// <summary>
    /// 是否启用服务奖金税点扣除
    /// </summary>
    public bool EnableTaxDeduction { get; set; } = true;
    
    /// <summary>
    /// 最小税点扣除金额（分）
    /// </summary>
    public long MinTaxDeductionAmount { get; set; } = 0;
    
    /// <summary>
    /// 最大税点扣除金额（分，0表示无限制）
    /// </summary>
    public long MaxTaxDeductionAmount { get; set; } = 0;
}

