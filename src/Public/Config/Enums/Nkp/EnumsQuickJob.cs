﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Config.Enums.Nkp
{
    /// <summary>
    /// 纳税人类型
    /// </summary>
    public enum TaxpayerType
    {
        一般纳税人, 小规模纳税人
    }

    // public enum Region
    // {
    //     张家口 = 101,
    //     高邑
    // }

    // public class QuickJobRedionMap
    // {
    //     public static Dictionary<string,string> regionMap = new Dictionary<string, string>() 
    //     {
    //         { ((int)Region.张家口) + "" ,"1307" },
    //         { ((int)Region.高邑) + "" ,"130121" },
    //     };
    // }
}
