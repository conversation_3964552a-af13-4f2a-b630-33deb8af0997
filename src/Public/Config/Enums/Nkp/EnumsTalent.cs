﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Config.Enums.Nkp;

public enum EnumsTalent
{

}

/// <summary>
/// 简历人才库用户注册状态
/// </summary>
public enum TalentVirtualStatus
{
    /// <summary>
    /// 未注册
    /// </summary>
    [Description("未注册")]
    UnRegistered = 0,

    /// <summary>
    /// 已注册
    /// </summary>
    [Description("已注册")]
    Registered = 1,


    /// <summary>
    /// 实名注册
    /// </summary>
    [Description("实名注册")]
    RealName = 2
}

/// <summary>
/// 简历人才库用户学历
/// </summary>
public enum TalentVirtualEducation
{
    /// <summary>
    /// 初中及以下
    /// </summary>
    [Description("初中及以下")]
    Junior = 0,

    /// <summary>
    /// 中专/中技
    /// </summary>
    [Description("中专/中技")]
    Specialized = 1,

    /// <summary>
    /// 高中
    /// </summary>
    [Description("高中")]
    Senior = 2,

    /// <summary>
    /// 大专
    /// </summary>
    [Description("大专")]
    Professional = 3,

    /// <summary>
    /// 本科
    /// </summary>
    [Description("本科")]
    Undergraduate = 4,

    /// <summary>
    /// 硕士
    /// </summary>
    [Description("硕士")]
    Master = 5,

    /// <summary>
    /// 博士
    /// </summary>
    [Description("博士")]
    Doctor = 6

}

/// <summary>
/// 简历人才库用户渠道
/// </summary>
public enum TalentVirtualChannel
{
    /// <summary>
    /// 简历上传
    /// </summary>
    [Description("简历上传")]
    ResumeUpload = 0,

    /// <summary>
    /// 第三方导入
    /// </summary>
    [Description("第三方导入")]
    ThirdPartyImport = 1,

    /// <summary>
    /// 渠道商
    /// </summary>
    [Description("渠道商")]
    Channel = 2
}

/// <summary>
/// 简历人才库导入记录重复类型
/// </summary>
public enum TalentUpLoadRepeatType
{
    /// <summary>
    /// 跳过
    /// </summary>
    [Description("跳过")]
    Skip = 0,

    /// <summary>
    /// 覆盖
    /// </summary>
    [Description("覆盖")]
    Cover = 1,

    /// <summary>
    /// 新增
    /// </summary>
    [Description("新增")]
    Added = 2,
}

/// <summary>
/// 简历人才库导入记录状态
/// </summary>
public enum TalentUpLoadStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    [Description("待处理")]
    Untreated = 0,

    /// <summary>
    /// 已完成
    /// </summary>
    [Description("已完成")]
    Complete = 1,
}

/// <summary>
/// 简历人才库导入记录文件扩展名
/// </summary>
public enum TalentUpLoadFileExtension
{
    /// <summary>
    /// Word
    /// </summary>
    [Description("Word")]
    Docx = 0,

    /// <summary>
    /// PDF
    /// </summary>
    [Description("PDF")]
    PDF = 1,

    /// <summary>
    /// Excel
    /// </summary>
    [Description("Excel")]
    Xlsx = 2,

    /// <summary>
    /// TXT
    /// </summary>
    [Description("TXT")]
    TXT = 3,

    /// <summary>
    /// JPG
    /// </summary>
    [Description("JPG")]
    JPG = 4
}



