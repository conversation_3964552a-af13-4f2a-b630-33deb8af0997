﻿using System.Text.Json.Serialization;

namespace Config.Enums.Nkp;

/// <summary>
/// 开票类型枚举
/// </summary>
public enum NovaPinInvoiceType
{
    /// <summary>
    /// 专票
    /// </summary>
    [JsonPropertyName("4")]
    专票 = 4,
    /// <summary>
    /// 普票
    /// </summary>
    [JsonPropertyName("5")]
    普票 = 5
}

/// <summary>
/// 开票推送方式枚举
/// </summary>
public enum NovaPinInvoicePushMode
{
    [JsonPropertyName("-1")]
    不推送 = -1,

    [JsonPropertyName("0")]
    邮箱 = 0,

    /// <summary>
    /// 默认
    /// </summary>
    [JsonPropertyName("1")]
    手机 = 1,

    [JsonPropertyName("2")]
    邮箱和手机 = 2
}

/// <summary>
/// 开票是否显示开户行及账号枚举
/// </summary>
public enum NovaPinInvoiceShowBankAccountType
{
    [JsonPropertyName("0")]
    都不显示 = 0,

    [JsonPropertyName("1")]
    备注仅显示销方开户行及账号 = 1,

    [JsonPropertyName("2")]
    备注仅显示购方开户行及账号 = 2,

    [JsonPropertyName("3")]
    购销方开户行及账号都显示 = 3
}

/// <summary>
/// 是否显示地址及电话
/// </summary>
public enum NovaPinInvoiceShowAddressTelType
{
    [JsonPropertyName("0")]
    都不显示 = 0,

    [JsonPropertyName("1")]
    备注仅显示销方地址及电话 = 1,

    [JsonPropertyName("2")]
    备注仅显示购方地址及电话 = 2,

    /// <summary>
    /// 此字段仅在数电普票和数电专票下生效
    /// </summary>
    [JsonPropertyName("3")]
    购销方地址及电话都显示 = 3
}

/// <summary>
/// 是否展示收款人和复核人
/// </summary>
public enum NovaPinInvoiceShowCheckerType
{
    [JsonPropertyName("0")]
    不显示 = 0,

    [JsonPropertyName("1")]
    显示 = 1,
}

/// <summary>
/// 发票记录状态
/// </summary>
public enum InvoiceRecordStatus
{
    [JsonPropertyName("0")]
    尚未申请 = 0,

    [JsonPropertyName("1")]
    已申请 = 1,

    [JsonPropertyName("2")]
    处理完成 = 2,

    [JsonPropertyName("3")]
    处理失败 = 3,

    [JsonPropertyName("4")]
    已退票 = 4,
}

/// <summary>
/// 发票应用类型
/// </summary>
public enum InvoiceRecordUseScence
{
    [JsonPropertyName("1")]
    项目发票 = 1,

    [JsonPropertyName("2")]
    服务奖金 = 2
}


#region 发票商品枚举
/// <summary>
/// 含税标识
/// </summary>
public enum InvoiceProductIsIncludedTax
{
    [JsonPropertyName("1")]
    含税 = 1,

    [JsonPropertyName("0")]
    不含税 = 0
}

/// <summary>
/// 是否享受优惠政策
/// </summary>
public enum InvoiceProductIsEnjoyPreferential
{
    [JsonPropertyName("1")]
    享受 = 1,

    [JsonPropertyName("0")]
    不享受 = 0
}
#endregion