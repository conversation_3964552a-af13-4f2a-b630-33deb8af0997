﻿using System;
using System.ComponentModel;
using System.Runtime.Serialization;

namespace Config.Enums.Nkp;

//仅仅诺聘token验证，登录注册用
public class OnlyNuoAuth : Attribute
{
}

//零工市场
public class QuickJobAuth : Attribute
{
}

//零工市场
public class QuickJobAdminAuth : Attribute
{
}

//身份验证
public class MyAuthorizeAttribute : Attribute
{
}

// //不需要审核通过可访问
// public class NoAudit : Attribute
// {
// }

//内部通信服务验证
public class TransitAuth : Attribute
{
}

//权限验证
public class Powers : Attribute
{
    public string Power { get; set; } = default!;
}

// // 快招工系统权限
// public class KuaishouPowersAuth : Attribute
// {

// }

public enum ActiveStatus
{
    [Description("不可用")]
    Inactive,

    [Description("正常")]
    Active
}

//在线状态
public enum OnlineStatus
{
    离线 = 0,
    刚刚活跃,
    最近活跃
}

//常用语类型
public enum MsgWordsType
{
    求职者, 顾问
}

public enum EnterpriseStatus
{
    Inactive, Active
}

public enum ResumeLanguage
{
    中文, 英文
}

public enum EnterpriseGroupType
{
    [Description("普通公司")]
    Ordinary,

    [Description("从属集团")]
    Group,

    [Description("主集团")]
    MainGroup
}

//企业名称展示
public enum EntDisplayType
{
    企业名称, 匿名展示
}

//可沟通时间类型
public enum FreeTimeType
{
    随时, 选择时间
}

//用户状态
public enum UserStatus
{
    [Description("未审核")]
    UnReviewed,

    [Description("正常")]
    Active,

    // [Description("禁用")]
    // Disabled,

    [Description("审核拒绝")]
    Rejected = 7,

    Ignore = 99,
}

//注册来源
public enum RegisterSource
{
    [Description("微信小程序")]
    Applet,

    [Description("Web")]
    Web,

    [Description("快手小程序")]
    KsApplet,

    [Description("面试官")]
    Interviewer,

    [Description("抖音小程序")]
    SeekerDyApplet,

    [Description("零工市场")]
    JobMarket = 9,

    [Description("快招工简历")]
    KsTalent = 10,

    [Description("诺聘")]
    NuoPin = 11,

    [Description("其他")]
    Other = 99
}

public enum HrRecommendEntSource
{
    诺聘平台,
    诺聘网点 = 2,
    Act腾讯活动 = 90001,
    Act诺优考落地页 = 90002
}

public enum HrRecommendEntStatus
{
    已成单 = 1, 需求确认, 初步跟进,
    [Description("方案/报价")]
    方案报价,
    废弃 = 9
}

public enum HrRecommendEntLevel
{
    不重要, 一般重要, 非常重要
}

public enum SMSType
{
    LoginOrRegister, ChangePassword, ChangePhone, Activity
}

/// <summary>
/// 短信发送方
/// </summary>
public enum SMSSender
{
    阿里云, 庄点科技drondea
}

public enum OAuthClientType
{
    JobSeeker, Hr
}

public enum TokenType
{
    平台端 = 0,
    用户端 = 1,
    企业端 = 2,
    系统内部服务 = 30
}

//证件类型
public enum IdCardType
{
    [Description("身份证")]
    身份证
}

public enum Sex
{
    女 = 0,
    男 = 1
}

public enum PoliticsStatus
{
    群众 = 0,
    团员 = 1,
    党员 = 2
}

//户口性质
public enum HouseholdType
{
    农村 = 0,
    城镇 = 1
}

// //token类型
// public enum TokenType
// {
//     // 求职者, 顾问, 内部服务 = 8, 平台端
//     平台端 = 0,
//     用户端 = 1,
//     企业端 = 2,
//     系统内部服务 = 30
// }

//是否残疾人
public enum ArchivesIsDisability
{
    否 = 0,
    是 = 1
}

public enum ClientType
{
    [Description("求职者小程序")]
    SeekerApplet,

    [Description("面试官小程序")]
    InterviewerApplet,

    [Description("Hr小程序")]
    HrApplet,

    [Description("HrWeb端")]
    HrWeb,

    [Description("快手小程序")]
    SeekerKsApplet,

    [Description("抖音小程序")]
    SeekerDyApplet,

    [Description("求职者网页登录")]
    SeekerWeb,

    [Description("诺聘")]
    NuoPin,

    [Description("导入")]
    Import,

    [Description("零工市场求职端")]
    JobMarketForSeeker = 9,

    [Description("零工市场业务端")]
    JobMarketForHr = 10,

    [Description("快招工")]
    Kzg = 20,

    [Description("其他")]
    Other = 99
}

//学历
public enum EducationType
{
    [Description("不限")]
    不限 = 0,

    [Description("高中/职高/技校")]
    高中 = 1,

    [Description("大专")]
    大专,

    [Description("本科")]
    本科,

    [Description("硕士")]
    硕士,

    [Description("博士")]
    博士,

    [Description("其他")]
    其他 = 9
}

//职业
public enum OccupationType
{
    [Description("学生党")]
    学生党 = 1,

    [Description("上班族")]
    上班族 = 2,

    [Description("自由职业")]
    自由职业 = 3,

    [Description("待业")]
    待业 = 4
}

//工作经验
public enum PostWorkTime
{
    [Description("不限经验")]
    不限 = 0,

    [Description("应届毕业生")]
    应届毕业生 = 1,

    [Description("1年以下")]
    一年以下 = 2,

    [Description("1-3年")]
    一到三年 = 3,

    [Description("3-5年")]
    三到五年 = 4,

    [Description("5-7年")]
    五到七年 = 5,

    [Description("7-10年")]
    七到十年 = 6,

    [Description("10年以上")]
    十年及以上 = 7,
}

//小程序分享类型
public enum AppletShareType
{
    首页 = 0,
    职位 = 1,
    求职者分享职位 = 2,
    渠道商二维码 = 3,
    渠道商职位二维码 = 4
}

public enum ChannelType
{
    网格员, 渠道伙伴, 异业合作, 特许
}

//企业融资阶段
public enum EnterpriseCapital
{
    [Description("不需要融资")]
    不需要融资 = 0,

    [Description("未融资")]
    未融资 = 1,

    [Description("天使轮")]
    天使轮 = 2,

    [Description("A轮")]
    A轮 = 3,

    [Description("B轮")]
    B轮 = 4,

    [Description("C轮")]
    C轮 = 5,

    [Description("D轮及以上")]
    D轮及以上 = 6,

    [Description("已上市")]
    已上市 = 10,
}

//用户短信类型
public enum UserSmsRecordType
{
    充值, 职位邀约
}

/// <summary>
/// 企业规模
/// </summary>
public enum EnterpriseScale
{
    [Description("20人以下")]
    二十人以下 = 0,

    [Description("20-99人")]
    二十至一百 = 1,

    [Description("100-499人")]
    一百至五百 = 2,

    [Description("500-999人")]
    五百至一千 = 3,

    [Description("1000-9999人")]
    一千至一万 = 4,

    [Description("10000人以上")]
    一万以上 = 5
}

// 企业性质
public enum EnterpriseNature
{
    [Description("个体工商户")]
    个体工商户 = 0,

    [Description("民营/私营企业")]
    私营 = 1,

    [Description("集体")]
    集体 = 2,

    [Description("国有企业")]
    国企 = 3,

    [Description("政府/事业单位")]
    政府事业单位 = 4,

    [Description("中外合资")]
    中外合资 = 5,

    [Description("港澳台企业")]
    港澳台企业 = 6,

    [Description("外企独资")]
    外企独资 = 7,

    [Description("非盈利组织")]
    非盈利组织 = 8,

    [Description("股份制企业")]
    股份制企业 = 9,

    [Description("律师事务所")]
    律师事务所 = 10,

    [Description("基金会")]
    基金会 = 11,

    [Description("其他")]
    其他 = 99,
}

public enum UserQrcodeType
{
    顾问渠道小程序二维码 = 5
}

public enum SchoolType
{
    专科, 本科, 成人
}

public enum SeekerOrHr
{
    Seeker,
    Hr
}

public enum BalanceRecordType
{
    提现 = 99,
    提现退还 = 100,
    兑换红包 = 101,
}

public enum WithdrawStatus
{
    审核中, 提现中, 提现完成, 提现失败 = 9, 退还 = 10
}

//数据大屏播报类型
public enum AdvTalRankingType
{
    人才储备累计, 昨日新增人才储备
}

public enum UpdatePostShowType
{
    协同职位, 原始职位, 项目
}

public enum UpdateHrDataType
{
    项目, 职位, 招聘
}

public enum AgeGroup
{
    [Description("16岁以下")]
    十六岁以下 = 0,

    [Description("16至24岁")]
    十六至二十四岁 = 1,

    [Description("25至34岁")]
    二十五至三十四岁 = 2,

    [Description("35至44岁")]
    三十五至四十四岁 = 3,

    [Description("45岁以上")]
    四十五岁以上 = 4,

    [Description("其他")]
    其他 = 9
}

/// <summary>
/// 服务奖金发放状态
/// </summary>
public enum ServiceBonusStatus
{
    [Description("待发起")]
    待发起 = 0,

    [Description("发起成功")]
    发起成功 = 1,

    [Description("发起失败")]
    发起失败 = 2,

    [Description("处理中")]
    处理中 = 3,

    [Description("已完成")]
    已完成 = 4,

    [Description("已取消")]
    已取消 = 5,
    
    [Description("被驳回")]
    被驳回 = 6
}