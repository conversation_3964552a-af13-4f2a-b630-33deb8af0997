﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Config.Enums.Nkp
{
    /// <summary>
    /// 交易类型
    /// </summary>
    public enum UmsTransType
    {
        [JsonPropertyName("201741")]
        子账户充值上账 = 201741,   
        
        [JsonPropertyName("201746")]
        子账户转账支付 = 201746,   
        
        [JsonPropertyName("201711")]
        支付账户转账支付 = 201711,
        
        [JsonPropertyName("201697")]
        提现token模式 = 201697,
        
        [JsonPropertyName("201644")]
        提现api模式 = 201644,
        
        [JsonPropertyName("201745")]
        子账户资金回收 = 201745
    }
}
