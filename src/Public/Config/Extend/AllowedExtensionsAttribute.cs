﻿using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Config.Extend
{
    public class AllowedExtensionsAttribute : ValidationAttribute
    {
        private readonly string[] _extensions;
        public AllowedExtensionsAttribute(string[] extensions, string? errorMessage = null)
        {
            _extensions = extensions;
            ErrorMessage = errorMessage;
        }

        protected override ValidationResult? IsValid(object? value, ValidationContext context)
        {
            if (value != null)
            {
                if (value is IFormFile singleFile)
                    return ValidateSingleFile(singleFile, context);
                else if (value is IEnumerable<IFormFile> files)
                    return ValidateMultipleFiles(files, context);
            }
            return ValidationResult.Success;
        }

        /// <summary>
        /// 验证单个文件
        /// </summary>
        /// <param name="file"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        private ValidationResult? ValidateSingleFile(IFormFile file, ValidationContext context)
        {
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (string.IsNullOrEmpty(extension) || !_extensions.Contains(extension))
            {
                if (string.IsNullOrEmpty(ErrorMessage))
                    return new ValidationResult($"文件'{context.DisplayName}'的格式'{extension}'无效。允许的格式：{string.Join(", ", _extensions)}");
                else
                    return new ValidationResult(FormatErrorMessage(context.DisplayName));
            }
            return ValidationResult.Success;
        }

        /// <summary>
        /// 验证多个文件
        /// </summary>
        /// <param name="files"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        private ValidationResult? ValidateMultipleFiles(IEnumerable<IFormFile> files, ValidationContext context)
        {
            var invalidFiles = files
                .Where(file =>
                {
                    var ext = Path.GetExtension(file.FileName).ToLowerInvariant();
                    return string.IsNullOrEmpty(ext) || !_extensions.Contains(ext);
                })
                .ToList();

            if (invalidFiles.Any())
            {
                var invalidNames = string.Join(", ", invalidFiles.Select(f => f.FileName));
                return new ValidationResult(
                    $"以下文件格式无效：{invalidNames}。{FormatErrorMessage(context.DisplayName)}");
            }
            return ValidationResult.Success;
        }
    }
}
