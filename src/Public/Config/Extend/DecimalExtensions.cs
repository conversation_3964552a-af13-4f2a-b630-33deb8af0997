﻿namespace Config.Extend;
public static class DecimalExtensions
{
    /// <summary>
    /// 将以分为单位的金额转换为以元为单位
    /// </summary>
    /// <param name="fen">分单位的金额</param>
    /// <returns>元单位的金额</returns>
    public static decimal RMB_FenToYuan(this decimal fen)
    {
        return fen / 100m;
    }

    /// <summary>
    /// 将以元为单位的金额转换为以分为单位
    /// </summary>
    /// <param name="yuan">元单位的金额</param>
    /// <returns>分单位的金额</returns>
    public static decimal RMB_YuanToFen(this decimal yuan)
    {
        return yuan * 100m;
    }
}
