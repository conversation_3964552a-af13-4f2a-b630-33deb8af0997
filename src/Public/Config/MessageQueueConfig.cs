namespace Config;

/// <summary>
/// 消息队列配置
/// </summary>
public class MessageQueueConfig
{
    /// <summary>
    /// 队列配置
    /// </summary>
    public Dictionary<string, QueueConfig> Queues { get; set; } = new();

    /// <summary>
    /// 默认重试次数
    /// </summary>
    public int DefaultMaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 默认处理超时时间（秒）
    /// </summary>
    public int DefaultTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 后台服务处理间隔（秒）
    /// </summary>
    public int ProcessingIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 清理已完成消息的间隔（小时）
    /// </summary>
    public int CleanupIntervalHours { get; set; } = 1;

    /// <summary>
    /// 保留已完成消息的天数
    /// </summary>
    public int RetentionDays { get; set; } = 7;
}

/// <summary>
/// 队列配置
/// </summary>
public class QueueConfig
{
    /// <summary>
    /// 队列名称
    /// </summary>
    public string Name { get; set; } = default!;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 处理超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 消息队列常量
/// </summary>
public static class MessageQueueConstants
{
    /// <summary>
    /// Redis键前缀
    /// </summary>
    public const string REDIS_KEY_PREFIX = "settlement:mq:";

    /// <summary>
    /// 队列前缀
    /// </summary>
    public const string QUEUE_PREFIX = REDIS_KEY_PREFIX + "queue:";

    /// <summary>
    /// 延迟队列前缀
    /// </summary>
    public const string DELAY_QUEUE_PREFIX = REDIS_KEY_PREFIX + "delay:";

    /// <summary>
    /// 处理锁前缀
    /// </summary>
    public const string PROCESSING_LOCK_PREFIX = REDIS_KEY_PREFIX + "lock:";

    /// <summary>
    /// 统计信息键
    /// </summary>
    public const string STATS_KEY = REDIS_KEY_PREFIX + "stats";
}
