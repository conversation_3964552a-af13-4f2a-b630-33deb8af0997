﻿using System;
namespace Config.CommonModel;

public class QueryRequest
{
    private int _limit;
    private int _offset;
    public QueryRequest()
    {

        Limit = 20;
        Offset = 0;
    }

    /// <summary>
    /// 每次查询多少个
    /// </summary>
    public int Limit
    {
        get { return _limit; }
        set { _limit = value; }
    }

    /// <summary>
    /// 偏移量
    /// </summary>
    public int Offset
    {
        get { return _offset; }
        set { _offset = value < 0 ? 0 : value; }
    }
}

public class QueryResponse
{
    public MetaData MetaData { get; set; } = new MetaData();
}

public class MetaData
{
    /// <summary>
    /// 总记录数
    /// </summary>
    public int Total { get; set; }
}


public class EmptyResponse
{

}

public class ErrorType
{
    public string? code { get; set; }
    public string? msg { get; set; }
}

