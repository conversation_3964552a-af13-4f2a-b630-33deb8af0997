﻿using System;
using Config.Enums;
using Config.Enums.Nkp;
using ClientType = Config.ClientType;
using TokenType = Config.TokenType;

public class RequestContext
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 账号
    /// </summary>
    public string? Account { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    public List<string>? Powers { get; set; }

    public string RequestId { get; set; } = string.Empty;
    public string ConnectionId { get; set; } = string.Empty;
    public string? RequestIp { get; set; }
    public string? UserAgent { get; set; }
    public string? ClientId { get; set; }

    public string? AccessToken { get; set; }
    
    /// <summary>
    /// 诺聘EntId
    /// </summary>
    public string NuoEntId { get; set; } = default!;

    /// <summary>
    /// 企业Id
    /// </summary>
    public string? EntId { get; set; }
}


public class ShortUserInfo
{
    public string? UserId { get; set; }
    public string? EntId { get; set; }
    public string? GroupEntId { get; set; }
    public string? NuoPinId { get; set; }
    public string? NuoEntId { get; set; }
    public ClientType? ClientType { get; set; }
    public UserStatus Status { get; set; }
    public EnterpriseGroupType GroupType { get; set; }
    public string NickName { get; set; } = default!;
    public string Mobile { get; set; } = default!;
    /// <summary>
    /// app类型
    /// </summary>
    public TokenType? AppType { get; set; }
    public List<string> Powers { get; set; } = new List<string>();
}
