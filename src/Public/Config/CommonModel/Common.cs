﻿using Config.Enums;
using System;
namespace Config.CommonModel;

public class TokenInfo
{
    /// <summary>
    /// 有效期（秒）
    /// </summary>
    public int TokenExpiresTime { get; set; }

    /// <summary>
    /// 访问token
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// 刷新token
    /// </summary>
    public string? RefreshToken { get; set; }

    public string? UserId { get; set; }
}

public class RefreshTokenRequest
{
    /// <summary>
    /// 刷新token
    /// </summary>
    public string RefreshToken { get; set; } = default!;
}

public class GeneralDic
{
    /// <summary>
    /// 编号
    /// </summary>
    public string Id { get; set; } = default!;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = default!;
}