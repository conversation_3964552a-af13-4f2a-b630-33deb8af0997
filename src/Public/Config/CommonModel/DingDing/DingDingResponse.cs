﻿namespace Config.CommonModel.DingDing;

/// <summary>
/// 钉钉统一返回模型
/// </summary>
/// <typeparam name="T"></typeparam>
public class DingDingResponse<T>
{
    /// <summary>
    /// 错误码
    /// </summary>
    public int errcode { get; set; }

    /// <summary>
    /// 错误提示
    /// </summary>
    public string? errmsg { get; set; }

    /// <summary>
    /// 返回实体
    /// </summary>
    public T? result { get; set; }

    /// <summary>
    /// 请求ID
    /// </summary>
    public string? request_id { get; set; }
}

/// <summary>
/// 钉钉获取token返回模型
/// </summary>
public class DingDingAccountToken
{
    /// <summary>
    /// 错误码
    /// </summary>
    public int errcode { get; set; }

    /// <summary>
    /// token内容
    /// </summary>
    public string? access_token { get; set; }

    /// <summary>
    /// 错误提示
    /// </summary>
    public string? errmsg { get; set; }

    /// <summary>
    /// access_token的过期时间，单位秒
    /// </summary>
    public int expires_in { get; set; }
}

public class DingDingErrorResponse
{
    /// <summary>
    /// 错误码
    /// </summary>
    public string? code { get; set; }

    /// <summary>
    /// 错误描述
    /// </summary>
    public string? message { get; set; }

    /// <summary>
    /// 请求Id
    /// </summary>
    public string? requestid { get; set; }
}