﻿namespace Config.CommonModel.DingDing;

public class GetDepartmentInfo
{
    /// <summary>
    /// 部门Id
    /// </summary>
    public long dept_id { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>

    public string? name { get; set; }

    /// <summary>
    /// 父部门Id
    /// </summary>

    public long parent_id { get; set; }
}

public class GetDeptUsers
{
    /// <summary>
    /// 部门Id
    /// </summary>
    public long dept_id { get; set; }

    /// <summary>
    /// 分页查询的游标，最开始传0，后续传返回参数中的next_cursor值。
    /// </summary>
    public int? cursor { get; set; }

    /// <summary>
    /// 分页大小。
    /// </summary>
    public int? size { get; set; }
}

public class GetDeptUsersResponse
{
    /// <summary>
    /// 错误码
    /// </summary>
    public int errcode { get; set; }

    /// <summary>
    /// 下一次分页的游标
    /// </summary>
    public long next_cursor { get; set; }

    /// <summary>
    /// 是否还有更多的数据
    /// </summary>
    public bool? has_more { get; set; }

    public List<DdDeptUserInfo>? list { get; set; }
}

public class DdDeptUserInfo
{
    /// <summary>
    /// 钉钉用户Id
    /// </summary>
    public string? userid { get; set; }

    /// <summary>
    /// 成员名称
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? mobile { get; set; }

    /// <summary>
    /// 部门信息
    /// </summary>
    public List<long>? dept_id_list { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? title { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? avatar { get; set; }

    /// <summary>
    /// 员工工号
    /// </summary>
    public string? job_number { get; set; }

    /// <summary>
    /// 办公地点
    /// </summary>
    public string? work_place { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? remark { get; set; }

    /// <summary>
    /// 是否为企业的管理员
    /// </summary>
    public bool isAdmin { get; set; }
}