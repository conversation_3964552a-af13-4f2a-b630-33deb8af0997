﻿namespace Config.CommonModel.DingDing;

public class GetDingDingUserDetailsByUserId
{
}

/// <summary>
/// 根据userid获取用户详情请求模型
/// </summary>
public class GetDingDingUserDetailsByUserIdRequest
{
    /// <summary>
    /// 钉钉userid
    /// </summary>
    public string? userid { get; set; }
}

/// <summary>
/// 根据userid获取用户详情返回模型
/// </summary>
public class GetDingDingUserDetailsByUserIdResponse
{
    /// <summary>
    /// 是否激活了钉钉
    /// </summary>
    public bool active { get; set; }

    /// <summary>
    /// 是否为企业的管理员
    /// </summary>
    public bool admin { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? avatar { get; set; }

    /// <summary>
    /// 是否为企业的老板
    /// </summary>
    public bool boss { get; set; }

    /// <summary>
    /// 所属部门id列表
    /// </summary>
    public List<int>? dept_id_list { get; set; }

    /// <summary>
    /// 员工在对应的部门中的排序
    /// </summary>
    public List<dept_order_listModel>? dept_order_list { get; set; }

    /// <summary>
    /// 是否为专属帐号
    /// </summary>
    public bool exclusive_account { get; set; }

    /// <summary>
    /// 是否号码隐藏
    /// </summary>
    public bool hide_mobile { get; set; }

    /// <summary>
    /// 入职时间，Unix时间戳，单位毫秒
    /// </summary>
    public long hired_date { get; set; }

    /// <summary>
    /// 员工工号
    /// </summary>
    public string? job_number { get; set; }

    /// <summary>
    /// 员工所在部门信息及是否是领导
    /// </summary>
    public List<leader_in_deptModel>? leader_in_dept { get; set; }

    /// <summary>
    /// 员工的直属主管userid
    /// </summary>
    public string? manager_userid { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? mobile { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 是否完成了实名认证
    /// </summary>
    public bool real_authed { get; set; }

    /// <summary>
    /// 角色列表
    /// </summary>
    public List<role_listModel>? role_list { get; set; }

    /// <summary>
    /// 是否为企业的高管
    /// </summary>
    public bool senior { get; set; }

    /// <summary>
    /// 国际电话区号
    /// </summary>
    public string? state_code { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? title { get; set; }

    /// <summary>
    /// 员工在当前开发者企业账号范围内的唯一标识
    /// </summary>
    public string? unionid { get; set; }

    /// <summary>
    /// 员工的userId
    /// </summary>
    public string? userid { get; set; }
}

public class dept_order_listModel
{
    /// <summary>
    /// 部门id
    /// </summary>
    public int dept_id { get; set; }

    /// <summary>
    /// 员工在部门中的排序
    /// </summary>
    public int order { get; set; }
}

public class leader_in_deptModel
{
    /// <summary>
    /// 部门ID
    /// </summary>
    public int dept_id { get; set; }

    /// <summary>
    /// 是否是领导
    /// </summary>
    public bool leader { get; set; }
}

public class role_listModel
{
    /// <summary>
    /// 角色ID
    /// </summary>
    public string? group_name { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public int id { get; set; }

    /// <summary>
    /// 角色组名称
    /// </summary>
    public string? name { get; set; }
}

public class DdCreateDepartment
{
    /// <summary>
    /// 部门名称。长度限制为1~64个字符，不允许包含字符"-"","以及","。
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 父部门ID，根部门ID为1。
    /// </summary>
    public long parent_id { get; set; }

    /// <summary>
    /// 是否隐藏本部门：true：隐藏部门，隐藏后本部门将不会显示在公司通讯录中；false（默认值）：显示部门
    /// </summary>
    public bool? hide_dept { get; set; }

    /// <summary>
    /// 指定可以查看本部门的其他部门列表，总数不能超过50。当hide_dept为true时，则此值生效。
    /// </summary>
    public string? dept_permits { get; set; }

    /// <summary>
    /// 指定可以查看本部门的人员userId列表，总数不能超过50。当hide_dept为true时，则此值生效。
    /// </summary>
    public string? user_permits { get; set; }

    /// <summary>
    /// 是否限制本部门成员查看通讯录：true：开启限制。开启后本部门成员只能看到限定范围内的通讯录；false（默认值）：不限制
    /// </summary>
    public bool? outer_dept { get; set; }

    /// <summary>
    /// 本部门成员是否只能看到所在部门及下级部门通讯录：true：只能看到所在部门及下级部门通讯录；false：不能查看所有通讯录，在通讯录中仅能看到自己。当outer_dept为true时，此参数生效。
    /// </summary>
    public bool? outer_dept_only_self { get; set; }

    /// <summary>
    /// 指定本部门成员可查看的通讯录用户userId列表，总数不能超过50。当outer_dept为true时，此参数生效。
    /// </summary>
    public string? outer_permit_users { get; set; }

    /// <summary>
    /// 指定本部门成员可查看的通讯录部门ID列表，总数不能超过50。当outer_dept为true时，此参数生效。
    /// </summary>
    public string? outer_permit_depts { get; set; }

    /// <summary>
    /// 是否创建一个关联此部门的企业群，默认为false即不创建。
    /// </summary>
    public bool? create_dept_group { get; set; }

    /// <summary>
    /// 是否默认同意加入该部门的申请：true：表示加入该部门的申请将默认同意；false：表示加入该部门的申请需要有权限的管理员同意
    /// </summary>
    public bool? auto_approve_apply { get; set; }

    /// <summary>
    /// 在父部门中的排序值，order值小的排序靠前。
    /// </summary>
    public int? order { get; set; }

    /// <summary>
    /// 部门标识字段，开发者可用该字段来唯一标识一个部门，并与钉钉外部通讯录里的部门做映射。
    /// </summary>
    public string? source_identifier { get; set; }
}

public class DdCreateDepartmentResponse
{
    /// <summary>
    /// 部门Id
    /// </summary>
    public string? dept_id { get; set; }
}


/// <summary>
/// 根据部门id获取部门名称返回模型
/// </summary>
public class GetGetDingBranchByBranchIdResponse
{
    /// <summary>
    /// 部门ID
    /// </summary>
    public int? dept_id { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? name { get; set; }

    /// <summary>
    /// 父部门ID
    /// </summary>
    public int? parent_id { get; set; }

    /// <summary>
    /// 部门标识字段(第三方企业应用不返回该参数)
    /// </summary>
    public string? source_identifier { get; set; }

    /// <summary>
    /// 是否同步创建一个关联此部门的企业群(true：创建,false：不创建)
    /// </summary>
    public bool? create_dept_group { get; set; }

    /// <summary>
    /// 当部门群已经创建后，是否有新人加入部门会自动加入该群(true：自动加入群,false：不会自动加入群)
    /// </summary>
    public bool? auto_add_user { get; set; }

    /// <summary>
    /// 是否默认同意加入该部门的申请(true：表示加入该部门的申请将默认同意,false：表示加入该部门的申请需要有权限的管理员同意)
    /// </summary>
    public bool? auto_approve_apply { get; set; }

    /// <summary>
    /// 部门是否来自关联组织(true：是,false：不是,第三方企业应用不返回该参数)
    /// </summary>
    public bool? from_union_org { get; set; }

    /// <summary>
    /// 教育部门标签(campus：校区,period：学段,grade：年级,class：班级)
    /// </summary>
    public string? tags { get; set; }

    /// <summary>
    /// 在父部门中的次序值
    /// </summary>
    public int? order { get; set; }

    /// <summary>
    /// 部门群ID
    /// </summary>
    public string? dept_group_chat_id { get; set; }

    /// <summary>
    /// 部门群是否包含子部门(true：包含,false：不包含)
    /// </summary>
    public bool? group_contain_sub_dept { get; set; }

    /// <summary>
    /// 企业群群主userId
    /// </summary>
    public string? org_dept_owner { get; set; }
}

