﻿using System;
namespace Config;

public class RedisKey
{
    //后台任务心跳检测
    public const string WorkerKeyPre = "no:woker:";

    //更新登录时间
    public const string UserLoginTime = "no:login:";

    //用户的token缓存key
    public const string UserTokenKey = "no:usrtkkey:";

    //用户登录失败次数
    public const string UserLoginMiss = "usr:lgms";

    public class Dic
    {
        //地区
        public const string Region = "no:dic:region";

        //地区tree
        public const string RegionTree = "no:dic:regiontree";

        //AppId
        public const string AppIds = "no:dic:appid";
    }

    public class Ybg
    {
        //每天删除用户数
        public const string DeleteUserDay = "ybg:deleteuserday";
    }
}

public class Constants
{
    
    
    /// <summary>
    /// 项目Id前缀
    /// </summary>
    public const string ProjectIdPre = "9";


    /// <summary>
    /// 线索Id前缀
    /// </summary>
    public const string CluePre = "8";

    /// <summary>
    /// 职位Id前缀
    /// </summary>
    public const string PostIdPre = "1";
    
    
    public class ApiGroup
    {
        public const string InternalApi = "internal";
        public const string SettlementApi = "settlement";
        public const string AdminApi = "admin";
    }

    /// <summary>
    /// mysql版本
    /// </summary>
    public readonly static Version MySqlVersion = new Version(8, 0, 21);

    public const string JdPostIdTest = "331989267825946374";
    public const string JdPostIdProd = "332128612248106886";

    /// <summary>
    /// AccessToken有效期
    /// </summary>
    public static int AccessTokenExpire = 15552000;

    /// <summary>
    /// RefreshToken有效期
    /// </summary>
    public static int RefreshTokenExpire = 15552000;

    ///// <summary>
    ///// 系统Id
    ///// </summary>
    //public static int SystemId = 0;

    /// <summary>
    /// 短信有效期
    /// </summary>
    public const int SmsExpireSeconds = 300;

    /// <summary>
    /// 系统报警发送间隔(防止错误轰炸)
    /// </summary>
    public static int SystemWarningInterval = 300;

    /// <summary>
    /// 默认时间
    /// </summary>
    public readonly static DateTime DefaultTime = new DateTime(1970, 1, 1);

    /// <summary>
    /// 默认时间
    /// </summary>
    public readonly static DateTime DefaultFutureTime = new DateTime(2099, 12, 31);

    /// <summary>
    /// 钉钉域名
    /// </summary>
    public const string DingDingDomain = "https://oapi.dingtalk.com";

    //钉钉诺快聘系统应用
    public const string DingDingAppKey = "dingisrgpurarbzjgh2a";
    public const string DingDingAppSecret = "783Zc96mBFKAMWxR7rpqsnIXAh2L4u_IzBdYb0yfeVG6IrpTaDlaoAuuaPyQwyfD";
    // public const string DingDingAppKey = "dinglk7ui1pmjdo6a9x0";
    // public const string DingDingAppSecret = "g1Kzk4nTyYHjJtzX-RPLAo4jinShWIJZZedvH4ljFnel-0rFofNEEGi6rJXkcV7O";
    public const string DingDingAgent = "2876531899";

    /// <summary>
    /// token前缀
    /// </summary>
    public const string TokenPrefix = "noahopen";

    /// <summary>
    /// 诺快聘
    /// </summary>
    public const int NkpRedisDatabase = 8;

    public class TencentCloud
    {
        public const string CosAppId = "xxx";
        public const string CosSecretId = "xxx";
        public const string CosSecretKey = "xxx";
        public const string CosBucket = "xxx";
        public const string CosRegin = "ap-beijing";
        public const string CosDomain = "https://xxx.cos.ap-beijing.myqcloud.com/";
    }

    /// <summary>
    /// 发票商品优惠政策及编码字典
    /// </summary>
    public Dictionary<string, string> InvoiceProduct_PreferentialDic = new Dictionary<string, string>()
    {
        { "01","简易征收"},
        { "02","稀土产品"},
        { "03","免税"},
        { "04","不征税"},
        { "05","先征后退"},
        { "06","100%先征后退"},
        { "07","50%先征后退"},
        { "08","按3%简易征收"},
        { "09","按5%简易征收"},
        { "10","按5%简易征收减按1.5%计征"},
        { "11","即征即退30%"},
        { "12","即征即退50%"},
        { "13","即征即退70%"},
        { "14","即征即退100%"},
        { "15","超税负3%即征即退"},
        { "16","超税负8%即征即退"},
        { "17","超税负12%即征即退"},
        { "18","超税负6%即征即退"}
    };
}

public class Power
{
    public const string Admin = "Admin";
    public const string Manager = "Manager";
    public const string Employee = "Employee";

    public static bool CheckPower(List<string> powers, string needPower)
    {
        return powers.Any(a => a.ToLower() == needPower.ToLower());
    }

    public static List<string> GetAllPowers()
    {
        var powers = new List<string>
        {
            Admin,
            Manager,
            Employee
        };
        return powers;
    }

    //管理员角色Id
    public const string AdminRoleId = "1";

    //主管角色Id
    public const string LeaderRoleId = "2";

    //员工角色Id
    public const string EmployeeRoleId = "99";
}

public class ApiMeta
{
    public ApiAuthType AuthType { get; set; } = ApiAuthType.None;
}

public enum ApiAuthType
{
    None, Internal = 8, Admin = 9,Nkp = 10
}

public enum TokenType
{
    管理端 = 0,
    用户端 = 1,
    企业端 = 2,
    内部服务 = 30
}

public enum ClientType
{
    管理端Web
}

