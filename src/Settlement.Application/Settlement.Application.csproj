﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Settlement.Domain\Settlement.Domain.csproj" />
    <ProjectReference Include="..\Settlement.Infrastructure\Settlement.Infrastructure.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\net8.0\Settlement.Application.xml</DocumentationFile>
    <NoWarn>1701;1702;1591;NU1803;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="LinqKit.Core" Version="1.2.8" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="ServiceStack.Text" Version="8.0.0" />
  </ItemGroup>

</Project>
