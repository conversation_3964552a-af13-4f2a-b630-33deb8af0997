using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ServiceStack.Text;
using Settlement.Application.Service;
using Settlement.Domain.Invoice;
using Settlement.Domain.Messages;
using Settlement.Infrastructure.Model;
using Settlement.Infrastructure.Proxy;

namespace Settlement.Application.ApiHandlers;

/// <summary>
/// NovaPin API处理器
/// </summary>
[Service(ServiceLifetime.Scoped)]
public class NovaPinApiHandler : ApiHandlerBase
{
    private readonly NovaPinApi _novaPinApi;
    private readonly IInvoiceService _invoiceService;
    private readonly ILogger<NovaPinApiHandler> _logger;

    public NovaPinApiHandler(NovaPinApi novaPinApi, IInvoiceService invoiceService, ILogger<NovaPinApiHandler> logger)
    {
        _novaPinApi = novaPinApi;
        _invoiceService = invoiceService;
        _logger = logger;
    }

    public override string ApiType => "NovaPin";

    public override async Task<bool> HandleAsync(string messageType, string content)
    {
        try
        {
            _logger.LogInformation("Processing NovaPin API call for message type: {messageType}", messageType);

            switch (messageType)
            {
                case nameof(ServiceBonusInitiatedMessage):
                    return await HandleServiceBonusMessage(content);

                case nameof(InvoiceInitiatedMessage):
                    return await HandleInvoiceMessage(content);

                default:
                    _logger.LogWarning($"Unknown message type: {messageType}");
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling NovaPin API call: {exMessage}", ex.Message);
            LogProcessing(messageType, content, false, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 处理服务奖金消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <returns>是否处理成功</returns>
    private async Task<bool> HandleServiceBonusMessage(string content)
    {
        try
        {
            var message = JsonSerializer.DeserializeFromString<ServiceBonusInitiatedMessage>(content);

            // 构建NovaPin API请求
            var request = new InitiateServiceBonusRequest
            {
                ContractCode = message.ContractCode,
                AgentHrNo = message.AgentHrNo,
                OrderNumber = message.OrderNumber,
                IssueList = message.IssueList.Select(x => new Settlement.Infrastructure.Model.IssueItem
                {
                    HrNo = x.PersonId,
                    IssuedAmount = x.Amount,
                    Remark = x.Type
                }).ToList()
            };

            _logger.LogInformation("Calling NovaPin InitiateServiceBonus API for order: {OrderNumber}", message.OrderNumber);

            // 调用NovaPin API
            var result = await _novaPinApi.InitiateServiceBonus(request);

            if (result.Code == 0) // 假设0表示成功
            {
                _logger.LogInformation("NovaPin InitiateServiceBonus API call succeeded for order: {OrderNumber}", message.OrderNumber);
                LogProcessing(nameof(ServiceBonusInitiatedMessage), content, true);
                return true;
            }
            else
            {
                _logger.LogWarning("NovaPin InitiateServiceBonus API call failed for order: {OrderNumber}, Code: {resultCode}, Message: {resultMsg}", message.OrderNumber, result.Code, result.Msg);
                LogProcessing(nameof(ServiceBonusInitiatedMessage), content, false, $"Code: {result.Code}, Message: {result.Msg}");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing service bonus message: {exMessage}", ex.Message);
            LogProcessing(nameof(ServiceBonusInitiatedMessage), content, false, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 处理开票消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <returns>是否处理成功</returns>
    private async Task<bool> HandleInvoiceMessage(string content)
    {
        string logTip = "为凭证开具发票:content=" + content;
        try
        {
            var message = JsonSerializer.DeserializeFromString<InvoiceInitiatedMessage>(content);

            //todo:确认下是否需要增加开票类消息
            // 构建NovaPin API请求
            var request = new InitiateInvoiceForUmsReq
            {
                VoucherId = message.VoucherId
            };

            logTip = $"为凭证{message.VoucherId}开具发票:";
            _logger.LogInformation("{logTip}:开始", logTip);

            // 调用NovaPin API
            var result = await _invoiceService.CreateInvoiceForUms(request);

            if (result?.Code == 200) // 假设200表示成功
            {
                _logger.LogInformation("{logTip}:操作成功", logTip);
                LogProcessing(nameof(InvoiceInitiatedMessage), content, true);
                return true;
            }
            else
            {
                _logger.LogWarning("{logTip}:操作失败, Message: {resultMsg}", logTip, result?.Msg ?? "NULL");
                LogProcessing(nameof(InvoiceInitiatedMessage), content, false, $"Code: {result.Code}, Message: {result.Msg}");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{logTip}:引发异常 {exMessage}", logTip, ex.Message);
            LogProcessing(nameof(InvoiceInitiatedMessage), content, false, ex.Message);
            return false;
        }
    }
}
