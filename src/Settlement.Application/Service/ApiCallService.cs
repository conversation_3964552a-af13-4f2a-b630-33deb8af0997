using Entity.Nkp;
using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Settlement.Application.Service;

/// <summary>
/// API调用服务实现
/// </summary>
[Service(ServiceLifetime.Scoped)]
public class ApiCallService : IApiCallService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ApiCallService> _logger;
    private readonly Dictionary<string, Type> _apiHandlers;

    public ApiCallService(IServiceProvider serviceProvider, ILogger<ApiCallService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _apiHandlers = new Dictionary<string, Type>();

        // 注册API处理器
        RegisterApiHandlers();
    }

    /// <summary>
    /// 处理消息并调用对应的第三方API
    /// </summary>
    /// <param name="message">本地消息表消息</param>
    /// <returns>是否处理成功</returns>
    public async Task<bool> ProcessMessageAsync(OutboxMessage message)
    {
        try
        {
            var handler = GetHandler(message.ApiType);
            if (handler == null)
            {
                _logger.LogError($"No handler found for API type: {message.ApiType}");
                return false;
            }

            _logger.LogInformation($"Processing message {message.Id} with API type {message.ApiType}");

            var result = await handler.HandleAsync(message.MessageType, message.Content);

            if (result)
            {
                _logger.LogInformation($"Successfully processed message {message.Id}");
            }
            else
            {
                _logger.LogWarning($"Failed to process message {message.Id}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error processing message {message.Id}: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 根据API类型获取处理器
    /// </summary>
    /// <param name="apiType">API类型</param>
    /// <returns>API处理器</returns>
    public IApiHandler? GetHandler(string apiType)
    {
        if (!_apiHandlers.TryGetValue(apiType, out var handlerType))
        {
            return null;
        }

        return _serviceProvider.GetService(handlerType) as IApiHandler;
    }

    /// <summary>
    /// 注册API处理器
    /// </summary>
    private void RegisterApiHandlers()
    {
        // 手动注册API处理器
        _apiHandlers["NovaPin"] = typeof(Settlement.Application.ApiHandlers.NovaPinApiHandler);
        _apiHandlers["Chinaums"] = typeof(Settlement.Application.ApiHandlers.ChinaumsApiHandler);

        //_logger.LogInformation("Registered {HandlerCount} API handlers", _apiHandlers.Count);
    }
}
