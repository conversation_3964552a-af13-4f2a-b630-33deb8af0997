﻿using Config;
using Config.Enums.Nkp;
using Entity;
using Entity.Nkp;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Settlement.Domain;
using Settlement.Domain.Invoice;
using Settlement.Domain.Messages;
using Settlement.Domain.Voucher;
using Settlement.Infrastructure.Model;
using Settlement.Infrastructure.Proxy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Settlement.Application.Service
{
    [Service(ServiceLifetime.Scoped)]
    public class VoucherService : IVoucherService
    {
        private readonly IDbContextFactory<NkpContext> _contextFactory;
        private readonly OpenChinaumsApi _openChinaumsApi;
        private readonly ILogger<VoucherService> _logger;
        private readonly RequestContext _user;
        private readonly ConfigManager _config;
        private readonly NovaPinApi _novaPinApi;
        private readonly CommonUserService _commonUserService;
        IMessagePublishService _messagePublishService;
        public VoucherService(
            IDbContextFactory<NkpContext> contextFactory,
            OpenChinaumsApi openChinaumsApi,
            ILogger<VoucherService> logger,
            IOptionsSnapshot<ConfigManager> config,
            RequestContext user,
            NovaPinApi novaPinApi,
            CommonUserService commonUserService,
            IMessagePublishService messagePublishService)
        {
            _contextFactory = contextFactory;
            _openChinaumsApi = openChinaumsApi;
            _logger = logger;
            _user = user;
            _config = config.Value;
            _novaPinApi = novaPinApi;
            _commonUserService = commonUserService;
            _messagePublishService = messagePublishService;
        }

        /// <summary>
        /// 分页获取凭证记录
        /// </summary>
        /// <param name="req"></param>
        /// <param name="MerchantType">IN=取我给别人开的票，OUT=取别人给我开的票</param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public RespPageBase<VoucherListResp> GetList(VoucherGetList req, string MerchantType)
        {
            //验证参数
            ParamsValidation.Validate(req);

            //组装条件
            var predicate = PredicateBuilder.New<Voucher>(o => true);

            if (MerchantType.ToUpper() == "IN")
                predicate.And(x => x.InMerchantId == req.MerchantId);
            else
                predicate.And(x => x.OutMerchantId == req.MerchantId);

            if (req.BeginTime != null)
                predicate.And(p => p.VoucherTime >= req.BeginTime);
            if (req.EndTime != null)
                predicate.And(p => p.VoucherTime <= req.EndTime);
            if (!string.IsNullOrEmpty(req.Search))
                predicate.And(p => p.InMerchantName.Contains(req.Search) || p.OutMerchantName.Contains(req.Search) || p.Subject.Contains(req.Search));

            using var context = _contextFactory.CreateDbContext();
            int Total = context.Voucher.Count(predicate);
            var result = context.Voucher.Where(predicate)
                .OrderByDescending(x => x.CreatedTime).Skip((req.PageIndex - 1) * req.PageSize).Take(req.PageSize)
                .Select(x => new VoucherListResp(x))
                .ToList();
            return new RespPageBase<VoucherListResp>(result, Total, req.PageIndex, req.PageSize);
        }


    }
}
