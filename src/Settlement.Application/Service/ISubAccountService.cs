using Settlement.Domain.Transaction;

namespace Settlement.Application.Service;

/// <summary>
/// 子账户服务接口
/// </summary>
public interface ISubAccountService
{
    /// <summary>
    /// 开通子账户
    /// </summary>
    /// <param name="req">开通请求</param>
    /// <returns>开通结果</returns>
    Task<SubAccountResp> OpenSubAccountAsync(OpenSubAccountReq req);

    /// <summary>
    /// 更新子账户余额
    /// </summary>
    /// <param name="AccountNumber">账户号</param>
    /// <param name="BalanceAmt">余额，单位：分</param>
    void UpdateSubAccountAmount(string AccountNumber, string BalanceAmt);
    /// <summary>
    /// 获取子账户余额
    /// </summary>
    /// <param name="ProjectId">项目ID</param>
    Task<SubAccountBalanceResp> FindSubAccountAmount(string projectId);

    /// <summary>
    /// 开通子账户
    /// </summary>
    /// <param name="req"></param>
    Task OpenAccountNotify(OpenAccountNotifyReq req);
    
    /// <summary>
    /// 获取子账户状态
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    SubAccountStatusResp GetSubAccountStatus(SubAccountStatusReq req);
    
    /// <summary>
    /// 转账支付
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    Task<TransactionResp> Transaction(TransactionReq req);
    
    
    


}
