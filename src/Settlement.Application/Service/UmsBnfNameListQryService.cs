﻿using Entity;
using Entity.Nkp;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ServiceStack;
using Settlement.Domain.AutoReg;
using Settlement.Domain.Transaction;
using Settlement.Infrastructure.Proxy;

namespace Settlement.Application.Service;

/// <summary>
/// 控股股东/受益人姓名
/// </summary>
[Service(ServiceLifetime.Scoped)]
public class UmsBnfNameListQryService : IUmsBnfNameListQryService
{
    private readonly IDbContextFactory<NkpContext> _contextFactory;
    private readonly ChinaumsApi _ChinaumsApi;
    private readonly ILogger<UmsBnfNameListQryService> _logger;
    /// <summary>
    /// 简单流控使用key
    /// *0=社会统一信用代码
    /// </summary>
    private const string _LockKey = "ums:BnfNameListQry:{0}";

    public UmsBnfNameListQryService(
        IDbContextFactory<NkpContext> contextFactory,
        ChinaumsApi ChinaumsApi,
        ILogger<UmsBnfNameListQryService> logger)
    {
        _contextFactory = contextFactory;
        _ChinaumsApi = ChinaumsApi;
        _logger = logger;
    }

    /// <summary>
    /// 获取企业控股股东/受益人姓名
    /// </summary>
    /// <param name="req">统一社会信用代码</param>
    /// <returns>开通结果</returns>
    public async Task<Ums_BnfNameListQry> GetModel(BnfNameListQryReq req)
    {
        //简单流控
        if (!MyRedis.Client.SetNx(string.Format(_LockKey, req.ProtocolNo), DateTime.Now, TimeSpan.FromSeconds(5)))
            throw new BadRequestException($"操作太频繁了，请稍后重试");

        using var context = _contextFactory.CreateDbContext();

        try
        {
            // 1. 检查信息是否已查询过了
            var model = await context.Ums_BnfNameListQry.FirstOrDefaultAsync(x => x.ProtocolNo == req.ProtocolNo);
            if (model != null)
                return model;

            // 2. 调用第三方API
            var apiResult = await _ChinaumsApi.BnfNameListQry(req);
            if (apiResult.IsSuccess())
            {
                model = new Ums_BnfNameListQry
                {
                    ProtocolNo = req.ProtocolNo!,
                    BnfNameList = apiResult.BnfNameList ?? [],
                    ShareholderNameList = apiResult.ShareholderNameList ?? []
                };
                // 3. 保存到数据库
                context.Ums_BnfNameListQry.Add(model);
                await context.SaveChangesAsync();

                return model;
            }
            else
            {
                _logger.LogInformation("获取企业控股股东/受益人姓名：第三方API调用完成，错误码: {ErrorCode}", apiResult.ResCode);
                throw new BadRequestException($"获取企业控股股东/受益人失败，{apiResult.ResMsg}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取企业控股股东/受益人姓名：失败，社会统一信用代码: {ProtocolNo}", req.ProtocolNo);
            throw;
        }
    }
}
