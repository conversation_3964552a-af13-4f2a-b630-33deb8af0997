using Infrastructure.Common;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Settlement.Application.Service;

/// <summary>
/// 本地消息表后台处理服务
/// </summary>
public class OutboxBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<OutboxBackgroundService> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromSeconds(5); // 30秒处理一次
    private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(1); // 1小时清理一次
    private DateTime _lastCleanupTime = DateTime.MinValue;

    public OutboxBackgroundService(IServiceProvider serviceProvider, ILogger<OutboxBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Outbox Background Service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var lockerKey = $"OutboxBackgroundLock";
                using var locker = MyRedis.TryLock(lockerKey, 2000);
                if (locker == null)
                    return;
                using var scope = _serviceProvider.CreateScope();
                var messageProcessingService = scope.ServiceProvider.GetRequiredService<IMessageProcessingService>();

                // 处理待发送的消息
                //await messageProcessingService.ProcessPendingMessagesAsync();

                //定期清理已完成的消息
                if (DateTime.Now - _lastCleanupTime > _cleanupInterval)
                {
                    //await messageProcessingService.CleanupCompletedMessagesAsync(10000000);
                    _lastCleanupTime = DateTime.Now;
                }

                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // 正常停止
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in Outbox Background Service");
                
                // 发生错误时等待更长时间再重试
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("Outbox Background Service stopped");
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Outbox Background Service is stopping");
        await base.StopAsync(cancellationToken);
    }
}
