using Config.Enums.Nkp;
using Settlement.Domain.Transaction;
using Settlement.Infrastructure.Proxy;
using Entity;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Settlement.Application.Service;

/// <summary>
/// 企业资产服务实现类
/// </summary>
[Service(ServiceLifetime.Transient)]
public class EnterpriseAssetService : IEnterpriseAssetService
{
    private readonly OpenChinaumsApi _openChinaumsApi;
    private readonly NkpContext _nkpContext;
    private readonly IUmsCertService _umsCertService;

    public EnterpriseAssetService(OpenChinaumsApi openChinaumsApi, NkpContext nkpContext, IUmsCertService umsCertService)
    {
        _openChinaumsApi = openChinaumsApi;
        _nkpContext = nkpContext;
        _umsCertService = umsCertService;
    }

    /// <inheritdoc/>
    public async Task<EnterpriseAssetOverviewResp> GetEnterpriseAssetOverview(EnterpriseAssetOverviewReq request)
    {
        if (string.IsNullOrWhiteSpace(request.MerchantId))
        {
            throw new ArgumentException("商户ID不能为空");
        }

        // 根据商户ID从ums_complex_upload表中获取账户号
        var merchantAccount = await _nkpContext.Ums_Complex_Upload
            .Where(x => x.MerchantId == request.MerchantId)
            .Select(x => new { x.AcctNo })
            .FirstOrDefaultAsync();

        if (merchantAccount == null || string.IsNullOrWhiteSpace(merchantAccount.AcctNo))
        {
            throw new InvalidOperationException($"未找到商户ID为 {request.MerchantId} 的账户信息");
        }

        var accountNumber = merchantAccount.AcctNo;

        // 调用OpenChinaumsApi获取账户余额（可提现金额）
        var withdrawableAmount = await GetWithdrawableAmountAsync(accountNumber);

        // 查询已提现金额 - 从ums_callback_trans表中统计提现token模式的记录
        var withdrawnAmount = await GetWithdrawnAmountAsync(accountNumber);

        // 查询已发放绩效工资 - 从服务奖金发放记录表中统计
        var paidPerformanceSalary = await GetPaidPerformanceSalaryAsync(request.MerchantId);

        // 查询项目退款金额 - 从Ums_Callback_Trans表中统计子账户资金回收且TransNote为Refund的记录
        var projectRefundAmount = await GetProjectRefundAmountAsync(accountNumber);

        // 查询内部交付金额 - 从Ums_Callback_Trans表中统计子账户资金回收且TransNote不等于Refund的记录
        var internalDeliveryAmount = await GetInternalDeliveryAmountAsync(accountNumber);

        // 查询外部交付金额 - 从Ums_Callback_Trans表中统计子账户转账支付或支付账户转账支付的记录
        var externalDeliveryAmount = await GetExternalDeliveryAmountAsync(accountNumber);

        return new EnterpriseAssetOverviewResp
        {
            MerchantId = request.MerchantId,
            WithdrawableAmount = withdrawableAmount,
            WithdrawnAmount = withdrawnAmount,
            PaidPerformanceSalary = paidPerformanceSalary,
            ProjectRefundAmount = projectRefundAmount,
            InternalDeliveryAmount = internalDeliveryAmount,
            ExternalDeliveryAmount = externalDeliveryAmount,
            StatisticsTime = DateTime.Now
        };
    }

    /// <summary>
    /// 获取可提现金额
    /// </summary>
    /// <param name="accountNumber">账户号</param>
    /// <returns>可提现金额</returns>
    private async Task<decimal> GetWithdrawableAmountAsync(string accountNumber)
    {
        try
        {
            // 创建余额查询请求
            var balanceReq = new OpenAccountBalanceReq
            {
                AccountNumber = accountNumber,
                IsUseCert = true
            };

            // 获取证书
            var cert = _umsCertService.GetCertByANo(accountNumber);
            var balanceResult = await _openChinaumsApi.AccountBalance(new AccountBalanceReq(balanceReq), cert);

            if (balanceResult?.ErrorCode == "********")
            {
                // 将分转换为元
                return Convert.ToDecimal(balanceResult.BalanceAmt) / 100;
            }

            throw new InvalidOperationException($"查询账户余额失败: {balanceResult?.ErrorInfo ?? "未知错误"}");
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"获取可提现金额失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取已提现金额
    /// </summary>
    /// <param name="accountNumber">账户号</param>
    /// <returns>已提现金额</returns>
    private async Task<decimal> GetWithdrawnAmountAsync(string accountNumber)
    {
        if (string.IsNullOrWhiteSpace(accountNumber))
        {
            return 0m;
        }

        try
        {
            var transType = ((int)UmsTransType.提现token模式).ToString();
            
            // 先获取数据，然后在客户端进行聚合计算
            var transAmounts = await _nkpContext.Ums_Callback_Trans
                .Where(x => x.TransType == transType && 
                           x.OutAcctNo == accountNumber && 
                           !string.IsNullOrEmpty(x.TransAmt))
                .Select(x => x.TransAmt)
                .ToListAsync();
                
            var totalAmountInCents = transAmounts
                .Sum(x => decimal.TryParse(x, out var amount) ? amount : 0m);

            // 将分转换为元
            return totalAmountInCents / 100m;
        }
        catch (FormatException ex)
        {
            throw new InvalidOperationException($"交易金额格式错误，账户号: {accountNumber}", ex);
        }
        catch (OverflowException ex)
        {
            throw new InvalidOperationException($"交易金额超出范围，账户号: {accountNumber}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"获取已提现金额失败，账户号: {accountNumber}, 错误: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取已发放绩效工资金额
    /// </summary>
    /// <param name="merchantId">商户ID</param>
    /// <returns>已发放绩效工资金额（单位：元）</returns>
    private async Task<decimal> GetPaidPerformanceSalaryAsync(string merchantId)
    {
        if (string.IsNullOrWhiteSpace(merchantId))
        {
            return 0m;
        }

        try
        {
            // 查询已完成状态的服务奖金发放记录
            var issuedAmounts = await _nkpContext.Service_Bonus_Record
                .Where(x => x.MerchantId == merchantId && 
                           x.Status == Config.Enums.Nkp.ServiceBonusStatus.已完成 && 
                           !x.IsDeleted)
                .Select(x => x.IssuedAmount)
                .ToListAsync();
                
            var totalAmountInCents = issuedAmounts.Sum();

            // 将分转换为元
            return totalAmountInCents / 100m;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"获取已发放绩效工资金额失败，商户ID: {merchantId}, 错误: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取项目退款金额
    /// </summary>
    /// <param name="accountNumber">账户号</param>
    /// <returns>项目退款金额（单位：元）</returns>
    private async Task<decimal> GetProjectRefundAmountAsync(string accountNumber)
    {
        if (string.IsNullOrWhiteSpace(accountNumber))
        {
            return 0m;
        }

        try
        {
            var transType = ((int)UmsTransType.子账户资金回收).ToString();
            
            // 查询子账户资金回收且TransNote为Refund的记录
            var transAmounts = await _nkpContext.Ums_Callback_Trans
                .Where(x => x.TransType == transType && 
                           x.InAcctNo == accountNumber && // 入金账户为当前账户
                           x.TransNote == "Refund" &&
                           !string.IsNullOrEmpty(x.TransAmt))
                .Select(x => x.TransAmt)
                .ToListAsync();
                
            var totalAmountInCents = transAmounts
                .Sum(x => decimal.TryParse(x, out var amount) ? amount : 0m);

            // 将分转换为元
            return totalAmountInCents / 100m;
        }
        catch (FormatException ex)
        {
            throw new InvalidOperationException($"交易金额格式错误，账户号: {accountNumber}", ex);
        }
        catch (OverflowException ex)
        {
            throw new InvalidOperationException($"交易金额超出范围，账户号: {accountNumber}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"获取项目退款金额失败，账户号: {accountNumber}, 错误: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取内部交付金额
    /// </summary>
    /// <param name="accountNumber">账户号</param>
    /// <returns>内部交付金额（单位：元）</returns>
    private async Task<decimal> GetInternalDeliveryAmountAsync(string accountNumber)
    {
        if (string.IsNullOrWhiteSpace(accountNumber))
        {
            return 0m;
        }

        try
        {
            var transType = ((int)UmsTransType.子账户资金回收).ToString();
            
            // 查询子账户资金回收且TransNote不等于Refund的记录
            var transAmounts = await _nkpContext.Ums_Callback_Trans
                .Where(x => x.TransType == transType && 
                           x.InAcctNo == accountNumber && // 入金账户为当前账户
                           (x.TransNote != "Refund" || x.TransNote == null) &&
                           !string.IsNullOrEmpty(x.TransAmt))
                .Select(x => x.TransAmt)
                .ToListAsync();
                
            var totalAmountInCents = transAmounts
                .Sum(x => decimal.TryParse(x, out var amount) ? amount : 0m);

            // 将分转换为元
            return totalAmountInCents / 100m;
        }
        catch (FormatException ex)
        {
            throw new InvalidOperationException($"交易金额格式错误，账户号: {accountNumber}", ex);
        }
        catch (OverflowException ex)
        {
            throw new InvalidOperationException($"交易金额超出范围，账户号: {accountNumber}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"获取内部交付金额失败，账户号: {accountNumber}, 错误: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 获取外部交付金额
    /// </summary>
    /// <param name="accountNumber">账户号</param>
    /// <returns>外部交付金额（单位：元）</returns>
    private async Task<decimal> GetExternalDeliveryAmountAsync(string accountNumber)
    {
        if (string.IsNullOrWhiteSpace(accountNumber))
        {
            return 0m;
        }

        try
        {
            var transType1 = ((int)UmsTransType.子账户转账支付).ToString();
            var transType2 = ((int)UmsTransType.支付账户转账支付).ToString();
            
            // 查询子账户转账支付或支付账户转账支付的记录
            var transAmounts = await _nkpContext.Ums_Callback_Trans
                .Where(x => (x.TransType == transType1 || x.TransType == transType2) && 
                           x.InAcctNo == accountNumber && // 入金账户为当前账户
                           !string.IsNullOrEmpty(x.TransAmt))
                .Select(x => x.TransAmt)
                .ToListAsync();
                
            var totalAmountInCents = transAmounts
                .Sum(x => decimal.TryParse(x, out var amount) ? amount : 0m);

            // 将分转换为元
            return totalAmountInCents / 100m;
        }
        catch (FormatException ex)
        {
            throw new InvalidOperationException($"交易金额格式错误，账户号: {accountNumber}", ex);
        }
        catch (OverflowException ex)
        {
            throw new InvalidOperationException($"交易金额超出范围，账户号: {accountNumber}", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"获取外部交付金额失败，账户号: {accountNumber}, 错误: {ex.Message}", ex);
        }
    }

    /// <inheritdoc/>
    public async Task<MerchantWithdrawInfoResp> GetMerchantWithdrawInfo(MerchantWithdrawInfoReq request)
    {
        if (string.IsNullOrWhiteSpace(request.MerchantId))
        {
            throw new ArgumentException("商户ID不能为空");
        }

        // 根据商户ID从ums_complex_upload表中获取账户信息
        var merchantAccount = await _nkpContext.Ums_Complex_Upload
            .Where(x => x.MerchantId == request.MerchantId)
            .Select(x => new { x.AcctNo, x.BankAcctNo, x.ShopName })
            .FirstOrDefaultAsync();

        if (merchantAccount == null)
        {
            throw new InvalidOperationException($"未找到商户ID为 {request.MerchantId} 的账户信息");
        }

        if (string.IsNullOrWhiteSpace(merchantAccount.AcctNo))
        {
            throw new InvalidOperationException($"商户ID为 {request.MerchantId} 的账户号为空");
        }

        // 调用GetWithdrawableAmountAsync获取可提现金额
        var withdrawableAmount = await GetWithdrawableAmountAsync(merchantAccount.AcctNo);

        return new MerchantWithdrawInfoResp
        {
            AcctNo = merchantAccount.AcctNo,
            MerchantId = request.MerchantId,
            WithdrawableAmount = withdrawableAmount,
            BankAcctNo = merchantAccount.BankAcctNo ?? string.Empty,
            PayeeName = merchantAccount.ShopName ?? string.Empty
        };
    }
}