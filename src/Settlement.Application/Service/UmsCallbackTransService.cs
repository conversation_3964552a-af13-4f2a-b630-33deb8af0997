﻿using Entity;
using Entity.Nkp;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Settlement.Infrastructure.Proxy;

namespace Settlement.Application.Service;

[Service(ServiceLifetime.Scoped)]
public class UmsCallbackTransService : IUmsCallbackTransService
{
    private readonly IDbContextFactory<NkpContext> _contextFactory;
    private readonly OpenChinaumsApi _openChinaumsApi;
    private readonly ILogger<UmsCallbackTransService> _logger;

    public UmsCallbackTransService(
        IDbContextFactory<NkpContext> contextFactory,
        OpenChinaumsApi openChinaumsApi,
        ILogger<UmsCallbackTransService> logger)
    {
        _contextFactory = contextFactory;
        _openChinaumsApi = openChinaumsApi;
        _logger = logger;
    }

    /// <summary>
    /// 添加动账记录
    /// </summary>
    /// <param name="msg"></param>
    /// <returns></returns>
    public async Task<Ums_Callback_Trans> Add(string msg)
    {
        using var context = _contextFactory.CreateDbContext();
        Ums_Callback_Trans msgModel;
        try
        {
            msgModel = ServiceStack.Text.JsonSerializer.DeserializeFromString<Ums_Callback_Trans>(msg);
            context.Add(msgModel);
            await context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加动账记录，记录: {msg}", msg);
            throw;
        }
        return msgModel;
    }
}
