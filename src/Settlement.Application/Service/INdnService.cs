﻿using Config.CommonModel;
using Settlement.Infrastructure.Proxy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Settlement.Application.Service
{
    /// <summary>
    /// 数字诺亚回调处理
    /// </summary>
    public interface INdnService
    {
        /// <summary>
        /// 数字诺亚回调处理
        /// </summary>
        EmptyResponse NdnNotify(NdnNotify req);
    }
}
