using Config.CommonModel;
using Settlement.Domain.Project;
using Settlement.Domain.Transaction;

namespace Settlement.Application.Service;

public interface IProjectService
{
    ProjectRefundInfoResp GetProjectRefundInfo(string projectId);
    

    /// <summary>
    /// 项目直接退款（直接调用API，用于紧急情况）
    /// </summary>
    /// <param name="req">退款请求</param>
    /// <returns>第三方API响应</returns>
    Task<EmptyResponse> CancelProjectRefundDirect(ProjectRefundReq req);
    
    /// <summary>
    /// 获取项目收支统计
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>项目收支统计响应</returns>
    ProjectTransactionSummaryResp GetProjectTransactionSummary(string projectId);
    
    /// <summary>
    /// 获取项目收支明细分页列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>项目收支明细分页响应</returns>
    ProjectTransactionDetailPagedResp GetProjectTransactionDetailPaged(ProjectTransactionDetailReq req);

    /// <summary>
    /// 获取项目对公汇款记录分页列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>项目对公汇款记录分页响应</returns>
    ProjectRemittanceRecordPagedResp GetProjectRemittanceRecordPaged(ProjectRemittanceRecordReq req);
    
    /// <summary>
    /// 获取项目概览分页列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>项目概览分页响应</returns>
    ProjectOverviewPagedResp GetProjectOverviewPaged(ProjectOverviewPagedReq req);

    /// <summary>
    /// 获取项目概览导出数据
    /// </summary>
    /// <param name="req">导出请求</param>
    /// <returns>项目概览列表</returns>
    List<ProjectOverviewItem> GetProjectOverviewForExport(ProjectOverviewDownloadReq req);
}
