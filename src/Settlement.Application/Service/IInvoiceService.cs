﻿using Config.Enums.Nkp;
using Entity.Nkp;
using Infrastructure.Exceptions;
using LinqKit;
using Microsoft.EntityFrameworkCore.Internal;
using Settlement.Domain;
using Settlement.Domain.Invoice;
using Settlement.Infrastructure.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Settlement.Application.Service
{
    public interface IInvoiceService
    {
        /// <summary>
        /// 发起开票(项目发票)
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        Task CreateInvoiceForPro(InitiateInvoiceForProReq req);

        /// <summary>
        /// 发起开票(服务奖金)
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        Task<InitiateInvoiceResponse?> CreateInvoiceForUms(InitiateInvoiceForUmsReq req);


        /// <summary>
        /// 发起凭证开票流程
        /// </summary>
        /// <param name="cancel"></param>
        /// <param name="count">本次开几个凭证的发票</param>
        /// <returns></returns>
        Task CreateInvoiceForVoucher(CancellationToken cancel, int count = 10);

        /// <summary>
        /// 获取【项目】上次开发票填写信息接口开发
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        GetProLastInvoiceResp GetLastInvoiceForPro(GetProLastInvoiceReq req);

        /// <summary>
        /// 获取【项目】发票列表接口
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        RespPageBase<GetProInvoicesResp> GetInvoicesForPro(GetProInvoicesReq req);

        /// <summary>
        /// 获取【服务奖金】发票列表接口
        /// </summary>
        /// <param name="req"></param>
        /// <param name="MerchantType">IN=取我给别人开的票，OUT=取别人给我开的票</param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        RespPageBase<GetUmsInvoicesResp> GetInvoicesForUms(GetUmsInvoicesReq req, string MerchantType);

        /// <summary>
        /// 获取发票详情接口开发
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        List<Invoice_Detail> GetInvoicesDetail(GetInvoicesDetailReq req);

        /// <summary>
        /// 根据项目获取充值/退款动账记录
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        List<Ums_Callback_Trans> GetTransForPro(GetTransForProReq req);

        /// <summary>
        /// 根据商户获取服务奖金相关的 充值/退款动账记录
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        List<Ums_Callback_Trans> GetTransForUms(GetTransForUmsReq req);

        /// <summary>
        /// 获取【项目】可开票金额
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        GetInvoiceStatisticsResp GetInvoiceStatisticsForPro(GetStatisticsForProReq req);

        /// <summary>
        /// 获取【服务奖金】可开票金额
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        GetInvoiceStatisticsResp GetInvoiceStatisticsForUms(GetStatisticsForUmsReq req);
    }
}
