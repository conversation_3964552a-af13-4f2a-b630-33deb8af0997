﻿using Entity.Nkp;
using Infrastructure.Exceptions;
using LinqKit;
using Microsoft.EntityFrameworkCore.Internal;
using Settlement.Domain.Voucher;
using Settlement.Domain;
using Settlement.Infrastructure.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Settlement.Application.Service
{
    public interface IVoucherService
    {
        /// <summary>
        /// 分页获取凭证记录
        /// </summary>
        /// <param name="req"></param>
        /// <param name="MerchantType">IN=取我给别人开的票，OUT=取别人给我开的票</param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        RespPageBase<VoucherListResp> GetList(VoucherGetList req, string MerchantType);
    }
}
