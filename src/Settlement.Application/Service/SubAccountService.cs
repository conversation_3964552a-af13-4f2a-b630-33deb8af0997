using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Config;
using Config.Enums.Nkp;
using Entity;
using Entity.Nkp;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nest;
using Org.BouncyCastle.Ocsp;
using Settlement.Domain.Messages;
using Settlement.Domain.Transaction;
using Settlement.Infrastructure.Common;
using TypeEnum = Settlement.Domain.Transaction.TypeEnum;
using Settlement.Infrastructure.Model;
using Settlement.Infrastructure.Proxy;
using IssueItem = Settlement.Domain.Messages.IssueItem;

namespace Settlement.Application.Service;

/// <summary>
/// 子账户服务实现
/// </summary>
[Service(ServiceLifetime.Scoped)]
public class SubAccountService : ISubAccountService
{
    private readonly IDbContextFactory<NkpContext> _contextFactory;
    private readonly OpenChinaumsApi _openChinaumsApi;
    private readonly ILogger<SubAccountService> _logger;
    private readonly IMessagePublishService _messagePublishService;
    private readonly ConfigManager _config;
    private readonly NovaPinApi _novaPinApi;
    
    private const string OnePayEnumString = "OnePay";
        
    private const string TransferPayString = "TransferPay";
    
    private const string TransferPay2String = "TransferPay2";
     

    public SubAccountService(
        IDbContextFactory<NkpContext> contextFactory,
        OpenChinaumsApi openChinaumsApi,
        ILogger<SubAccountService> logger,IMessagePublishService messagePublishService,IOptionsSnapshot<ConfigManager> config,NovaPinApi novaPinApi)
    {
        _contextFactory = contextFactory;
        _openChinaumsApi = openChinaumsApi;
        _logger = logger;
        _messagePublishService = messagePublishService;
        _config = config.Value;
        _novaPinApi = novaPinApi;
    }

    /// <summary>
    /// 开通子账户
    /// </summary>
    /// <param name="req">开通请求</param>
    /// <returns>开通结果</returns>
    public async Task<SubAccountResp> OpenSubAccountAsync(OpenSubAccountReq req)
    {
        using var context = _contextFactory.CreateDbContext();

        try
        {
            _logger.LogInformation("开始处理子账户开通请求，项目ID: {ProjectId}", req.ProjectId);

            //  检查子账户是否已存在
            var existingSubAccount = await context.SubAccount
                .FirstOrDefaultAsync(x => x.ExternalUserNo == req.ProjectId);

            if (existingSubAccount != null)
            {
                throw new BadRequestException("该项目资金账户已经开通");
            }
            // 根据项目查询项目帐套信息，并且查询是否入网，如果没有入网不允许创建子项目
            var paymentAccount = context.Project
                .Where(p => p.ProjectId == req.ProjectId)
                .Join(context.Ndn_Books, 
                      p => p.BookCode, 
                      b => b.BookCode, 
                      (p, b) => b)
                .Join(context.Ums_Complex_Upload,
                      b => b.BookName,
                      u => u.ShopName,
                      (b, u) => u)
                .FirstOrDefault(u => u.Status == StatusEnum.上传成功 && 
                                   u.ApplyStatus == ApplyStatusEnum.入网成功 && 
                                   u.OpenStatus == SubAccountOpenStatus.成功);

            if (paymentAccount == null)
            {
                throw new BadRequestException("该项目所属帐套未完成支付账户入网开户");
            }

            if (string.IsNullOrEmpty(paymentAccount.AcctNo))
            {
                throw new BadRequestException("该项目所属帐套支付账户号为空");
            }
            // 查询当前子账户所属项目是否有签约合同
            var projecContract  = context.Project_Contract.FirstOrDefault(x => x.ProjectId == req.ProjectId && x.IsDefault == 1);
            if (projecContract == null)
            {
                throw new BadRequestException("当前项目未签约合同，请先签约合同");
            }
            // 创建子账户记录（初始状态）
            var subAccount = new SubAccount
            {
                ExternalUserNo = req.ProjectId,
                UserType = req.UserType ?? "03",
                OpenStatus = SubAccountOpenStatus.审核中,
                CreatedTime = DateTime.Now,
                UpdatedTime = DateTime.Now,
                // 这些字段将在API调用成功后设置
                ChannelNo = "",
                MerchantNo = "",
                AppSerialNumber = EntityTools.SnowflakeId(),
                ParentAccountNo = paymentAccount.AcctNo
            };

            // 保存到数据库
            context.SubAccount.Add(subAccount);
            await context.SaveChangesAsync();

            _logger.LogInformation("子账户记录已创建，ID: {SubAccountId}", subAccount.Id);

            // 构建第三方API请求
            var subAccountReq = new SubAccountReq
            {
                ExternalUserNo = req.ProjectId,
                CorporateCertificateType = req.CorporateCertificateType,
                UserType = req.UserType ?? "03",
                MacCode = req.MacCode,
                ParentAccountNo = paymentAccount.AcctNo,
                SubAccountType = "07",
                SubProductCode = _config.OpenChinaumsSubProdCode
            };

            // 调用第三方API
            var apiResult = await _openChinaumsApi.OpenSubAccount(subAccountReq);

            _logger.LogInformation("OpenSubAccount第三方API调用完成，错误码: {ErrorCode}", apiResult.ErrorCode);

            //  根据API调用结果更新数据库
            await UpdateSubAccountFromApiResult(context, subAccount, apiResult);

            return apiResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "子账户开通失败，项目ID: {ProjectId}, 失败原因: {ErrorMessage}", 
                req.ProjectId ?? "未知", 
                ex.Message ?? "未知错误");
            throw;
        }
    }

    /// <summary>
    /// 根据API调用结果更新子账户信息
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="subAccount">子账户实体</param>
    /// <param name="apiResult">API调用结果</param>
    private async Task UpdateSubAccountFromApiResult(NkpContext context, SubAccount subAccount, SubAccountResp apiResult)
    {
        try
        {
            // 更新基本信息
            subAccount.ErrorCode = apiResult.ErrorCode;
            subAccount.ErrorInfo = apiResult.ErrorInfo;
            subAccount.UpdatedTime = DateTime.Now;

            if (apiResult.ErrorCode == "0000" || apiResult.ErrorCode == "********")
            {
                // 将字符串 OpenStatus 解析为枚举，默认成功
                if (!string.IsNullOrEmpty(apiResult.OpenStatus) &&
                    Enum.TryParse<SubAccountOpenStatus>(apiResult.OpenStatus, out var parsedStatus))
                {
                    subAccount.OpenStatus = parsedStatus;
                }
                else
                {
                    // 无法解析时，按失败处理
                    subAccount.OpenStatus = SubAccountOpenStatus.失败;
                }
                subAccount.AccountNumber = apiResult.AccountNumber;
                subAccount.FailReason = null;

                if (!string.IsNullOrEmpty(apiResult.UserType))
                {
                    subAccount.UserType = apiResult.UserType;
                }

                _logger.LogInformation("子账户开通成功，账户号: {AccountNumber}", apiResult.AccountNumber);
            }
            else
            {
                // API调用失败，更新失败信息
                subAccount.OpenStatus = SubAccountOpenStatus.失败;
                subAccount.FailReason = apiResult.ErrorInfo;

                _logger.LogWarning("子账户开通失败，错误码: {ErrorCode}, 错误信息: {ErrorInfo}",
                    apiResult.ErrorCode, apiResult.ErrorInfo);
            }

            await context.SaveChangesAsync();

            _logger.LogInformation("子账户状态已更新，状态: {Status}", subAccount.OpenStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新子账户状态失败");
            throw;
        }
    }

    /// <summary>
    /// 更新子账户余额
    /// </summary>
    /// <param name="AccountNumber">账户号</param>
    /// <param name="BalanceAmt">余额，单位：分</param>
    public void UpdateSubAccountAmount(string AccountNumber, string BalanceAmt)
    {
        using var context = _contextFactory.CreateDbContext();
        try
        {
            var sub = context.SubAccount.FirstOrDefault(x => x.AccountNumber == AccountNumber);
            if (sub != null)
            {
                sub.Amount = long.Parse(BalanceAmt);
                context.SaveChanges();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新子账户余额失败，账户号: {AccountNumber}，余额：{BalanceAmt}", AccountNumber, BalanceAmt);
            throw;
        }
    }
    /// <summary>
    /// 获取子账户余额
    /// </summary>
    /// <param name="ProjectId">项目ID</param>
    public async Task<SubAccountBalanceResp> FindSubAccountAmount(string projectId)
    {
        using var context = _contextFactory.CreateDbContext();
        try
        {
            var sub = context.SubAccount.FirstOrDefault(x => x.ExternalUserNo == projectId);
            if (sub == null)
                throw new BadRequestException("子账户不存在");
            var accType = EnumExtensions.FromDescription<AcctType>(sub.UserType);
            if (accType == null)
                throw new BadRequestException("子账户账号类型不正确，请联系管理员");
            if(string.IsNullOrEmpty(sub.AccountNumber))
                throw new BadRequestException("子账户账号为空，请联系管理员");

            //获取子账户余额
            var balanse = await _openChinaumsApi.SubAccountBalance(new SubAccountBalanceReq(sub.AccountNumber, accType.Value));
            if (balanse == null)
                throw new BadRequestException("获取子账户余额请求失败，请联系管理员");

            return balanse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取并更新子账户余额失败，项目: {projectId}", projectId);
            throw;
        }
    }
    
    public async Task OpenAccountNotify(OpenAccountNotifyReq req)
    {
        var operationId = Guid.NewGuid().ToString();
        _logger.LogInformation("开始处理账户通知 [OperationId: {OperationId}], 通知类型: {NoticeType}, AppSsn: {AppSsn}", 
            operationId, req.NoticeType, req.Msg?.AppSsn);
            
        using var context = _contextFactory.CreateDbContext();
        try
        {
            if (req.Msg != null && "subAcctOpen".Equals(req.NoticeType))
            {
                _logger.LogInformation("处理子账户开通通知 [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}, 开通状态: {OpenStatus}", 
                    operationId, req.Msg.ExternalUserNo, req.Msg.OpenStatus);
                    
                var firstOrDefault = context.SubAccount.FirstOrDefault(x => x.ExternalUserNo == req.Msg.ExternalUserNo);
                if (firstOrDefault == null)
                {
                    _logger.LogWarning("子账户不存在 [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}", 
                        operationId, req.Msg.ExternalUserNo);
                    throw new Exception("子账户不存在");
                }
                
                // 记录子账户状态变更前的信息
                _logger.LogInformation("子账户状态变更 [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}, 原状态: {OldStatus}, 新状态: {NewStatus}, 账户号: {AccountNumber}", 
                    operationId, req.Msg.ExternalUserNo, firstOrDefault.OpenStatus, req.Msg.OpenStatus, req.Msg.AcctNo);
                    
                if (!string.IsNullOrEmpty(req.Msg.OpenStatus) &&
                    Enum.TryParse<SubAccountOpenStatus>(req.Msg.OpenStatus, out var parsedStatus))
                {
                    firstOrDefault.AccountNumber = req.Msg.AcctNo;
                    firstOrDefault.OpenStatus = parsedStatus;
                    firstOrDefault.FailReason = req.Msg.FailReason;
                    
                    _logger.LogInformation("子账户状态已更新为 {Status} [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}, 账户号: {AccountNumber}", 
                        parsedStatus, operationId, req.Msg.ExternalUserNo, req.Msg.AcctNo);
                }
                else
                {
                    firstOrDefault.OpenStatus = SubAccountOpenStatus.失败;
                    firstOrDefault.FailReason = req.Msg.FailReason;
                    
                    _logger.LogWarning("子账户状态解析失败，已设置为失败状态 [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}, 失败原因: {FailReason}", 
                        operationId, req.Msg.ExternalUserNo, req.Msg.FailReason);
                }
            }

            if (req.Msg != null && "acctOpen".Equals(req.NoticeType))
            {
                _logger.LogInformation("处理支付账户开通通知 [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}, 开通状态: {OpenStatus}", 
                    operationId, req.Msg.ExternalUserNo, req.Msg.OpenStatus);
                    
                var firstOrDefault = context.Ums_Complex_Upload.FirstOrDefault(x => x.MerchantId == req.Msg.ExternalUserNo);
                if (firstOrDefault == null)
                {
                    _logger.LogWarning("支付账户不存在 [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}", 
                        operationId, req.Msg.ExternalUserNo);
                    throw new Exception("支付账户不存在");
                }
                
                // 记录支付账户状态变更前的信息
                _logger.LogInformation("支付账户状态变更 [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}, 原状态: {OldStatus}, 新状态: {NewStatus}, 账户号: {AccountNumber}", 
                    operationId, req.Msg.ExternalUserNo, firstOrDefault.OpenStatus, req.Msg.OpenStatus, req.Msg.AcctNo);
                    
                if (!string.IsNullOrEmpty(req.Msg.OpenStatus) &&
                    Enum.TryParse<SubAccountOpenStatus>(req.Msg.OpenStatus, out var parsedStatus))
                {
                    firstOrDefault.AcctNo = req.Msg.AcctNo;
                    firstOrDefault.OpenStatus = parsedStatus;
                    firstOrDefault.FailReason = req.Msg.FailReason;
                    
                    _logger.LogInformation("支付账户状态已更新为 {Status} [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}, 账户号: {AccountNumber}", 
                        parsedStatus, operationId, req.Msg.ExternalUserNo, req.Msg.AcctNo);
                }
                else
                {
                    firstOrDefault.AcctNo = req.Msg.AcctNo;
                    firstOrDefault.OpenStatus = SubAccountOpenStatus.失败;
                    firstOrDefault.FailReason = req.Msg.FailReason;
                    
                    _logger.LogWarning("支付账户状态解析失败，已设置为失败状态 [OperationId: {OperationId}], 外部用户号: {ExternalUserNo}, 失败原因: {FailReason}", 
                        operationId, req.Msg.ExternalUserNo, req.Msg.FailReason);
                }
            }
            if (req.Msg != null && "tran".Equals(req.NoticeType))
            {
                _logger.LogInformation("处理交易通知 [OperationId: {OperationId}], 交易类型: {TransType}, AppSsn: {AppSsn}, 交易金额: {Amount}分", 
                    operationId, req.Msg.TransType, req.Msg.AppSsn, req.Msg.TransAmt);
                    
                if (((int)UmsTransType.子账户充值上账).ToString().Equals(req.Msg.TransType))
                {
                    // 幂等性检查：检查AppSsn是否已存在
                    var existingCallbackTrans = context.Ums_Callback_Trans.FirstOrDefault(x => x.AppSsn == req.Msg.AppSsn);
                    if (existingCallbackTrans != null)
                    {
                        _logger.LogInformation("子账户充值上账回调已处理过 [OperationId: {OperationId}], AppSsn: {AppSsn}", 
                            operationId, req.Msg.AppSsn);
                        return; // 直接返回，避免重复处理
                    }
                    
                    _logger.LogInformation("开始处理子账户充值上账 [OperationId: {OperationId}], 入金账户: {InAcctNo}, 交易金额: {Amount}分", 
                        operationId, req.Msg.InAcctNo, req.Msg.TransAmt);
                        
                    // 保存回调数据到 Ums_Callback_Trans 表
                    var callbackTrans = new Ums_Callback_Trans
                    {
                        AppSsn = req.Msg.AppSsn,
                        AppAcctDate = req.Msg.AppAcctDate,
                        ReqDate = req.Msg.ReqDate,
                        ReqTime = req.Msg.ReqTime,
                        TransSsn = req.Msg.TransSsn,
                        AcctDate = req.Msg.AcctDate,
                        TransDate = req.Msg.TransDate,
                        TransTime = req.Msg.TransTime,
                        OutAcctNo = req.Msg.OutAcctNo ?? "",
                        InAcctNo = req.Msg.InAcctNo ?? "",
                        TransAmt = req.Msg.TransAmt,
                        PltfmFee = req.Msg.PltfmFee ?? "0",
                        InstFee = req.Msg.InstFee ?? "0",
                        Fee = req.Msg.Fee ?? "0",
                        TransNote = req.Msg.TransNote,
                        TransType = req.Msg.TransType
                    };
                    
                    context.Ums_Callback_Trans.Add(callbackTrans);
                    _logger.LogInformation("回调数据已保存到Ums_Callback_Trans表 [OperationId: {OperationId}], AppSsn: {AppSsn}", 
                        operationId, req.Msg.AppSsn);
                    
                    // 更新子账户余额 - 使用原子性操作
                    if (long.TryParse(req.Msg.TransAmt, out var transAmt))
                    {
                        // 使用数据库级别的原子更新操作，避免并发问题
                        var affectedRows = await context.SubAccount
                            .Where(x => x.AccountNumber == req.Msg.InAcctNo)
                            .ExecuteUpdateAsync(setters => 
                                setters.SetProperty(x => x.Amount, x => x.Amount + transAmt)
                                       .SetProperty(x => x.UpdatedTime, DateTime.Now));
                        
                        if (affectedRows == 0)
                        {
                            _logger.LogError("子账户充值上账处理失败 [OperationId: {OperationId}], 未找到对应子账户或更新失败: {AccountNumber}", 
                                operationId, req.Msg.InAcctNo);
                            throw new InvalidOperationException($"子账户充值上账失败，无法更新子账户余额，账户号: {req.Msg.InAcctNo}");
                        }
                        
                        // 重新查询更新后的子账户信息用于日志记录和后续处理
                        var subAccount = await context.SubAccount
                            .FirstOrDefaultAsync(x => x.AccountNumber == req.Msg.InAcctNo);
                        
                        if (subAccount != null)
                        {
                            _logger.LogInformation("找到对应子账户 [OperationId: {OperationId}], 账户号: {AccountNumber}, 项目ID: {ProjectId}", 
                                operationId, subAccount.AccountNumber, subAccount.ExternalUserNo);
                            
                            var newBalance = subAccount.Amount;
                            var oldBalance = newBalance - transAmt;
                            
                            _logger.LogInformation("子账户余额已原子性更新 [OperationId: {OperationId}], 账户号: {AccountNumber}, 原余额: {OldBalance}分, 充值金额: {Amount}分, 新余额: {NewBalance}分", 
                                operationId, subAccount.AccountNumber, oldBalance, transAmt, newBalance);
                                
                            //查询子账户上级账户
                            var payAccount = context.Ums_Complex_Upload.FirstOrDefault(x => x.AcctNo == subAccount.ParentAccountNo);
                            if (payAccount == null)
                            {
                                _logger.LogWarning("未找到子账户对应的上级支付账户 [OperationId: {OperationId}], 上级账户号: {ParentAccountNo}", 
                                    operationId, subAccount.ParentAccountNo);
                            }
                            
                            // 保存充值记录到项目交易记录表
                            var projectTransactionRecord = new Project_Transaction_Record
                            {
                                ProjectId = subAccount.ExternalUserNo, // 使用子账户关联的项目ID
                                FlowNo = req.Msg.TransSsn ?? req.Msg.AppSsn, // 使用交易流水号或应用流水号
                                TransactionTime = DateTime.ParseExact($"{req.Msg.TransDate}{req.Msg.TransTime}", "yyyyMMddHHmmss", null),
                                TransactionType = ProjectTransactionType.收入, // 充值记录为收入
                                Amount = transAmt, // 金额（分）
                                Balance = (long)newBalance!, // 余额（分）
                                Remark = $"子账户充值上账 - {req.Msg.TransNote ?? ""}",
                                TransactionNo = req.Msg.AppSsn,
                                CounterpartyAccount = req.Msg.OutAcctName ?? payAccount?.ShopName,
                                RemitterName = req.Msg.OutAcctName, // 汇款人名称
                                RemitterBankAccount = req.Msg.OutAcctNo, // 汇款银行账号
                                RemittanceArrivalTime = DateTime.ParseExact($"{req.Msg.TransDate}{req.Msg.TransTime}", "yyyyMMddHHmmss", null), // 汇款到账时间
                                RechargeArrivalTime = DateTime.Now, // 充值到账时间（当前处理时间）
                                CreatedTime = DateTime.Now,
                                UpdatedTime = DateTime.Now,
                                IsDeleted = false
                            };
                            
                            context.Project_Transaction_Record.Add(projectTransactionRecord);
                            
                            _logger.LogInformation("子账户充值上账交易记录已保存 [OperationId: {OperationId}], 项目ID: {ProjectId}, 流水号: {FlowNo}, 交易时间: {TransactionTime}", 
                                operationId, projectTransactionRecord.ProjectId, projectTransactionRecord.FlowNo, projectTransactionRecord.TransactionTime);
                                
                            _logger.LogInformation("子账户充值上账成功 [OperationId: {OperationId}], 账户号: {AccountNumber}, 充值金额: {TransAmt}分, 原余额: {OldBalance}分, 新余额: {NewBalance}分, 项目ID: {ProjectId}", 
                                operationId, req.Msg.InAcctNo, req.Msg.TransAmt, oldBalance, newBalance, subAccount.ExternalUserNo);
                        }
                        else
                        {
                            _logger.LogWarning("子账户充值上账处理失败 [OperationId: {OperationId}], 无法解析交易金额: {TransAmt}", 
                                operationId, req.Msg.TransAmt);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("子账户充值上账处理失败 [OperationId: {OperationId}], 未找到对应子账户: {AccountNumber}", 
                            operationId, req.Msg.InAcctNo);
                    }
                }
                
                if (((int)UmsTransType.子账户转账支付).ToString().Equals(req.Msg.TransType))
                {
                    // 幂等性检查：检查AppSsn是否已存在
                    var existingCallbackTrans = context.Ums_Callback_Trans.FirstOrDefault(x => x.AppSsn == req.Msg.AppSsn);
                    if (existingCallbackTrans != null)
                    {
                        _logger.LogInformation("子账户转账支付回调已处理过 [OperationId: {OperationId}], AppSsn: {AppSsn}", 
                            operationId, req.Msg.AppSsn);
                        return; // 直接返回，避免重复处理
                    }
                    
                    _logger.LogInformation("开始处理子账户转账支付 [OperationId: {OperationId}], 出金账户: {OutAcctNo}, 入金账户: {InAcctNo}, 交易金额: {Amount}分", 
                        operationId, req.Msg.OutAcctNo, req.Msg.InAcctNo, req.Msg.TransAmt);
                        
                    var subAccount = context.SubAccount.FirstOrDefault(x => x.AccountNumber == req.Msg.OutAcctNo);
                    if (subAccount == null)
                    {
                        _logger.LogWarning("子账户转账支付处理失败 [OperationId: {OperationId}], 未找到出金子账户: {OutAcctNo}", 
                            operationId, req.Msg.OutAcctNo);
                    }
                    
                    // 保存回调数据到 Ums_Callback_Trans 表
                    var callbackTrans = new Ums_Callback_Trans
                    {
                        AppSsn = req.Msg.AppSsn,
                        AppAcctDate = req.Msg.AppAcctDate,
                        ReqDate = req.Msg.ReqDate,
                        ReqTime = req.Msg.ReqTime,
                        TransSsn = req.Msg.TransSsn,
                        AcctDate = req.Msg.AcctDate,
                        TransDate = req.Msg.TransDate,
                        TransTime = req.Msg.TransTime,
                        OutAcctNo = req.Msg.OutAcctNo ?? "",
                        InAcctNo = req.Msg.InAcctNo ?? "",
                        TransAmt = req.Msg.TransAmt,
                        PltfmFee = req.Msg.PltfmFee ?? "0",
                        InstFee = req.Msg.InstFee ?? "0",
                        Fee = req.Msg.Fee ?? "0",
                        TransNote = req.Msg.TransNote,
                        TransType = req.Msg.TransType
                    };
                    
                    context.Ums_Callback_Trans.Add(callbackTrans);
                    _logger.LogInformation("回调数据已保存到Ums_Callback_Trans表 [OperationId: {OperationId}], AppSsn: {AppSsn}", 
                        operationId, req.Msg.AppSsn);
                    
                    // 处理子账户支出记录到项目交易记录表
                    if (subAccount != null)
                    {
                        _logger.LogInformation("找到对应子账户 [OperationId: {OperationId}], 账户号: {AccountNumber}, 项目ID: {ProjectId}", 
                            operationId, subAccount.AccountNumber, subAccount.ExternalUserNo);
                            
                        if (long.TryParse(req.Msg.TransAmt, out var transAmt))
                        {
                            // 获取子账户的准确余额
                            long actualBalance = subAccount.Amount ?? 0;
                            _logger.LogInformation("开始获取子账户实时余额 [OperationId: {OperationId}], 账户号: {AccountNumber}, 当前数据库余额: {Balance}分", 
                                operationId, subAccount.AccountNumber, actualBalance);
                                
                            try
                            {
                                var accType = EnumExtensions.FromDescription<AcctType>(subAccount.UserType);
                                if (accType != null && !string.IsNullOrEmpty(subAccount.AccountNumber))
                                {
                                    // 调用银联API获取实时余额
                                    _logger.LogInformation("调用银联API获取实时余额 [OperationId: {OperationId}], 账户号: {AccountNumber}, 账户类型: {AccountType}", 
                                        operationId, subAccount.AccountNumber, accType.Value);
                                        
                                    var balanceResp = await _openChinaumsApi.SubAccountBalance(
                                        new SubAccountBalanceReq(subAccount.AccountNumber, accType.Value));
                                    
                                    if (balanceResp != null && !string.IsNullOrEmpty(balanceResp.BalanceAmt))
                                    {
                                        if (long.TryParse(balanceResp.BalanceAmt, out var apiBalance))
                                        {
                                            actualBalance = apiBalance;
                                            _logger.LogInformation("获取子账户实时余额成功 [OperationId: {OperationId}], 账户号: {AccountNumber}, API返回余额: {Balance}分", 
                                                operationId, subAccount.AccountNumber, actualBalance);
                                        }
                                        else
                                        {
                                            _logger.LogWarning("无法解析API返回的余额 [OperationId: {OperationId}], 账户号: {AccountNumber}, API返回值: {BalanceAmt}", 
                                                operationId, subAccount.AccountNumber, balanceResp.BalanceAmt);
                                        }
                                    }
                                    else
                                    {
                                        _logger.LogWarning("API返回的余额为空 [OperationId: {OperationId}], 账户号: {AccountNumber}", 
                                            operationId, subAccount.AccountNumber);
                                    }
                                }
                                else
                                {
                                    _logger.LogWarning("无法获取账户类型或账户号为空 [OperationId: {OperationId}], 账户号: {AccountNumber}, 账户类型: {AccountType}", 
                                        operationId, subAccount.AccountNumber, subAccount.UserType);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "获取子账户实时余额失败，使用数据库余额 [OperationId: {OperationId}], 账户号: {AccountNumber}, 异常信息: {Message}", 
                                    operationId, subAccount.AccountNumber, ex.Message);
                            }
                            
                            // 查询入金账户的支付账户信息作为对方账户
                            var payAccount = context.Ums_Complex_Upload.FirstOrDefault(x => x.AcctNo == req.Msg.InAcctNo);
                            var counterpartyAccount = payAccount?.ShopName;
                            
                            if (payAccount != null)
                            {
                                _logger.LogInformation("找到入金账户信息 [OperationId: {OperationId}], 账户号: {AccountNumber}, 商户名称: {ShopName}", 
                                    operationId, payAccount.AcctNo, payAccount.ShopName);
                            }
                            else
                            {
                                _logger.LogWarning("未找到入金账户信息 [OperationId: {OperationId}], 入金账户号: {InAcctNo}", 
                                    operationId, req.Msg.InAcctNo);
                            }
                            
                            // 保存支出记录到项目交易记录表
                            var transactionTime = DateTime.ParseExact($"{req.Msg.TransDate}{req.Msg.TransTime}", "yyyyMMddHHmmss", null);
                            _logger.LogInformation("准备创建项目交易记录 [OperationId: {OperationId}], 项目ID: {ProjectId}, 交易时间: {TransactionTime}, 金额: {Amount}分", 
                                operationId, subAccount.ExternalUserNo, transactionTime, transAmt);
                                
                            var projectTransactionRecord = new Project_Transaction_Record
                            {
                                ProjectId = subAccount.ExternalUserNo, // 使用子账户关联的项目ID
                                FlowNo = req.Msg.TransSsn ?? req.Msg.AppSsn, // 使用交易流水号或应用流水号
                                TransactionTime = transactionTime,
                                TransactionType = ProjectTransactionType.支出, // 转账支付记录为支出
                                Amount = transAmt, // 金额（分）
                                Balance = actualBalance, // 使用实时查询的准确余额
                                Remark = "子账户转账支付",
                                TransactionNo = req.Msg.AppSsn,
                                CounterpartyAccount = counterpartyAccount,
                                OperatorId = "System",
                                OperatorName = "系统自动",
                                CreatedTime = DateTime.Now,
                                UpdatedTime = DateTime.Now,
                                IsDeleted = false
                            };
                            
                            context.Project_Transaction_Record.Add(projectTransactionRecord);
                            
                            _logger.LogInformation("子账户转账支付记录已保存 [OperationId: {OperationId}], 出金账户: {OutAcctNo}, 入金账户: {InAcctNo}, 转账金额: {TransAmt}分, 项目ID: {ProjectId}, 对方账户: {CounterpartyAccount}, 实时余额: {Balance}分", 
                                operationId, req.Msg.OutAcctNo, req.Msg.InAcctNo, req.Msg.TransAmt, subAccount.ExternalUserNo, counterpartyAccount, actualBalance);
                        }
                        else
                        {
                            _logger.LogWarning("子账户转账支付处理失败 [OperationId: {OperationId}], 无法解析交易金额: {TransAmt}", 
                                operationId, req.Msg.TransAmt);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("子账户转账支付处理失败 [OperationId: {OperationId}], 未找到出金子账户: {OutAcctNo}", 
                            operationId, req.Msg.OutAcctNo);
                    }
                }

                if (((int)UmsTransType.支付账户转账支付).ToString().Equals(req.Msg.TransType))
                {
                    // 幂等性检查：检查AppSsn是否已存在
                    var existingCallbackTrans = context.Ums_Callback_Trans.FirstOrDefault(x => x.AppSsn == req.Msg.AppSsn);
                    if (existingCallbackTrans != null)
                    {
                        _logger.LogInformation("支付账户转账支付回调已处理过 [OperationId: {OperationId}], AppSsn: {AppSsn}", 
                            operationId, req.Msg.AppSsn);
                        return; // 直接返回，避免重复处理
                    }
                    
                    _logger.LogInformation("开始处理支付账户转账支付 [OperationId: {OperationId}], 出金账户: {OutAcctNo}, 入金账户: {InAcctNo}, 交易金额: {Amount}分", 
                        operationId, req.Msg.OutAcctNo, req.Msg.InAcctNo, req.Msg.TransAmt);
                    
                    // 保存回调数据到 Ums_Callback_Trans 表
                    var callbackTrans = new Ums_Callback_Trans
                    {
                        AppSsn = req.Msg.AppSsn,
                        AppAcctDate = req.Msg.AppAcctDate,
                        ReqDate = req.Msg.ReqDate,
                        ReqTime = req.Msg.ReqTime,
                        TransSsn = req.Msg.TransSsn,
                        AcctDate = req.Msg.AcctDate,
                        TransDate = req.Msg.TransDate,
                        TransTime = req.Msg.TransTime,
                        OutAcctNo = req.Msg.OutAcctNo ?? "",
                        InAcctNo = req.Msg.InAcctNo ?? "",
                        TransAmt = req.Msg.TransAmt,
                        PltfmFee = req.Msg.PltfmFee ?? "0",
                        InstFee = req.Msg.InstFee ?? "0",
                        Fee = req.Msg.Fee ?? "0",
                        TransNote = req.Msg.TransNote,
                        TransType = req.Msg.TransType
                    };
                    
                    context.Ums_Callback_Trans.Add(callbackTrans);
                    
                    _logger.LogInformation("支付账户转账支付动账记录已保存 [OperationId: {OperationId}], 出金账户: {OutAcctNo}, 入金账户: {InAcctNo}, 转账金额: {Amount}分, AppSsn: {AppSsn}, 交易时间: {TransactionTime}", 
                        operationId, req.Msg.OutAcctNo, req.Msg.InAcctNo, req.Msg.TransAmt, req.Msg.AppSsn, 
                        $"{req.Msg.TransDate} {req.Msg.TransTime}");
                }

                if (((int)UmsTransType.子账户资金回收).ToString().Equals(req.Msg.TransType) || "201745".Equals(req.Msg.TransType))
                {
                    // 幂等性检查：检查AppSsn是否已存在
                    var existingCallbackTrans = context.Ums_Callback_Trans.FirstOrDefault(x => x.AppSsn == req.Msg.AppSsn);
                    if (existingCallbackTrans != null)
                    {
                        _logger.LogInformation("子账户资金回收回调已处理过 [OperationId: {OperationId}], AppSsn: {AppSsn}", 
                            operationId, req.Msg.AppSsn);
                        return; // 直接返回，避免重复处理
                    }
                    
                    _logger.LogInformation("开始处理子账户资金回收 [OperationId: {OperationId}], 出金账户: {OutAcctNo}, 入金账户: {InAcctNo}, 交易金额: {Amount}分", 
                        operationId, req.Msg.OutAcctNo, req.Msg.InAcctNo, req.Msg.TransAmt);
                        
                    var subAccount = context.SubAccount.FirstOrDefault(x => x.AccountNumber == req.Msg.OutAcctNo);
                    if (subAccount == null)
                    {
                        _logger.LogWarning("子账户资金回收处理失败 [OperationId: {OperationId}], 未找到出金子账户: {OutAcctNo}", 
                            operationId, req.Msg.OutAcctNo);
                    }
                    
                    // 保存回调数据到 Ums_Callback_Trans 表
                    var callbackTrans = new Ums_Callback_Trans
                    {
                        AppSsn = req.Msg.AppSsn,
                        AppAcctDate = req.Msg.AppAcctDate,
                        ReqDate = req.Msg.ReqDate,
                        ReqTime = req.Msg.ReqTime,
                        TransSsn = req.Msg.TransSsn,
                        AcctDate = req.Msg.AcctDate,
                        TransDate = req.Msg.TransDate,
                        TransTime = req.Msg.TransTime,
                        OutAcctNo = req.Msg.OutAcctNo ?? "",
                        InAcctNo = req.Msg.InAcctNo ?? "",
                        TransAmt = req.Msg.TransAmt,
                        PltfmFee = req.Msg.PltfmFee ?? "0",
                        InstFee = req.Msg.InstFee ?? "0",
                        Fee = req.Msg.Fee ?? "0",
                        TransNote = req.Msg.TransNote,
                        TransType = req.Msg.TransType
                    };
                    
                    context.Ums_Callback_Trans.Add(callbackTrans);
                    _logger.LogInformation("回调数据已保存到Ums_Callback_Trans表 [OperationId: {OperationId}], AppSsn: {AppSsn}", 
                        operationId, req.Msg.AppSsn);
                    
                    // 处理子账户退款记录到项目交易记录表
                    if (subAccount != null)
                    {
                        _logger.LogInformation("找到对应子账户 [OperationId: {OperationId}], 账户号: {AccountNumber}, 项目ID: {ProjectId}", 
                            operationId, subAccount.AccountNumber, subAccount.ExternalUserNo);
                            
                        if (long.TryParse(req.Msg.TransAmt, out var transAmt))
                        {
                            // 获取子账户的准确余额
                            long actualBalance = subAccount.Amount ?? 0;
                            _logger.LogInformation("开始获取子账户实时余额 [OperationId: {OperationId}], 账户号: {AccountNumber}, 当前数据库余额: {Balance}分", 
                                operationId, subAccount.AccountNumber, actualBalance);
                                
                            try
                            {
                                var accType = EnumExtensions.FromDescription<AcctType>(subAccount.UserType);
                                if (accType != null && !string.IsNullOrEmpty(subAccount.AccountNumber))
                                {
                                    // 调用银联API获取实时余额
                                    _logger.LogInformation("调用银联API获取实时余额 [OperationId: {OperationId}], 账户号: {AccountNumber}, 账户类型: {AccountType}", 
                                        operationId, subAccount.AccountNumber, accType.Value);
                                        
                                    var balanceResp = await _openChinaumsApi.SubAccountBalance(
                                        new SubAccountBalanceReq(subAccount.AccountNumber, accType.Value));
                                    
                                    if (balanceResp != null && !string.IsNullOrEmpty(balanceResp.BalanceAmt))
                                    {
                                        if (long.TryParse(balanceResp.BalanceAmt, out var apiBalance))
                                        {
                                            actualBalance = apiBalance;
                                            _logger.LogInformation("获取子账户实时余额成功 [OperationId: {OperationId}], 账户号: {AccountNumber}, API返回余额: {Balance}分", 
                                                operationId, subAccount.AccountNumber, actualBalance);
                                        }
                                        else
                                        {
                                            _logger.LogWarning("无法解析API返回的余额 [OperationId: {OperationId}], 账户号: {AccountNumber}, API返回值: {BalanceAmt}", 
                                                operationId, subAccount.AccountNumber, balanceResp.BalanceAmt);
                                        }
                                    }
                                    else
                                    {
                                        _logger.LogWarning("API返回的余额为空 [OperationId: {OperationId}], 账户号: {AccountNumber}", 
                                            operationId, subAccount.AccountNumber);
                                    }
                                }
                                else
                                {
                                    _logger.LogWarning("无法获取账户类型或账户号为空 [OperationId: {OperationId}], 账户号: {AccountNumber}, 账户类型: {AccountType}", 
                                        operationId, subAccount.AccountNumber, subAccount.UserType);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "获取子账户实时余额失败，使用数据库余额 [OperationId: {OperationId}], 账户号: {AccountNumber}, 异常信息: {Message}", 
                                    operationId, subAccount.AccountNumber, ex.Message);
                            }
                            
                            // 查询入金账户的支付账户信息作为对方账户
                            var payAccount = context.Ums_Complex_Upload.FirstOrDefault(x => x.AcctNo == req.Msg.InAcctNo);
                            var counterpartyAccount = payAccount?.ShopName;
                            
                            if (payAccount != null)
                            {
                                _logger.LogInformation("找到入金账户信息 [OperationId: {OperationId}], 账户号: {AccountNumber}, 商户名称: {ShopName}", 
                                    operationId, payAccount.AcctNo, payAccount.ShopName);
                            }
                            else
                            {
                                _logger.LogWarning("未找到入金账户信息 [OperationId: {OperationId}], 入金账户号: {InAcctNo}", 
                                    operationId, req.Msg.InAcctNo);
                            }
                            
                            // 保存退款记录到项目交易记录表
                            var transactionTime = DateTime.ParseExact($"{req.Msg.TransDate}{req.Msg.TransTime}", "yyyyMMddHHmmss", null);
                            _logger.LogInformation("准备创建项目交易记录 [OperationId: {OperationId}], 项目ID: {ProjectId}, 交易时间: {TransactionTime}, 金额: {Amount}分", 
                                operationId, subAccount.ExternalUserNo, transactionTime, transAmt);
                                
                            var projectTransactionRecord = new Project_Transaction_Record
                            {
                                ProjectId = subAccount.ExternalUserNo, // 使用子账户关联的项目ID
                                FlowNo = req.Msg.TransSsn ?? req.Msg.AppSsn, // 使用交易流水号或应用流水号
                                TransactionTime = transactionTime,
                                TransactionType = ProjectTransactionType.支出, // 资金回收记录为支出
                                Amount = transAmt, // 金额（分）
                                Balance = actualBalance, // 使用实时查询的准确余额
                                Remark = "子账户资金回收-退款",
                                TransactionNo = req.Msg.AppSsn,
                                CounterpartyAccount = counterpartyAccount,
                                OperatorId = "System",
                                OperatorName = "系统自动",
                                CreatedTime = DateTime.Now,
                                UpdatedTime = DateTime.Now,
                                IsDeleted = false
                            };
                            
                            context.Project_Transaction_Record.Add(projectTransactionRecord);
                            
                            _logger.LogInformation("子账户资金回收(退款)记录已保存 [OperationId: {OperationId}], 出金账户: {OutAcctNo}, 入金账户: {InAcctNo}, 回收金额: {Amount}分, 项目ID: {ProjectId}, 对方账户: {CounterpartyAccount}, 实时余额: {Balance}分", 
                                operationId, req.Msg.OutAcctNo, req.Msg.InAcctNo, req.Msg.TransAmt, subAccount.ExternalUserNo, counterpartyAccount, actualBalance);
                        }
                        else
                        {
                            _logger.LogWarning("子账户资金回收处理失败 [OperationId: {OperationId}], 无法解析交易金额: {TransAmt}", 
                                operationId, req.Msg.TransAmt);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("子账户资金回收处理失败 [OperationId: {OperationId}], 未找到出金子账户: {OutAcctNo}", 
                            operationId, req.Msg.OutAcctNo);
                    }
                }

                if (((int)UmsTransType.提现token模式).ToString().Equals(req.Msg.TransType))
                {
                    // 幂等性检查：检查AppSsn是否已存在
                    var existingCallbackTrans = context.Ums_Callback_Trans.FirstOrDefault(x => x.AppSsn == req.Msg.AppSsn);
                    if (existingCallbackTrans != null)
                    {
                        _logger.LogInformation("提现token模式回调已处理过，AppSsn: {AppSsn}", req.Msg.AppSsn);
                        return; // 直接返回，避免重复处理
                    }
                    
                    // 保存回调数据到 Ums_Callback_Trans 表
                    var callbackTrans = new Ums_Callback_Trans
                    {
                        AppSsn = req.Msg.AppSsn,
                        AppAcctDate = req.Msg.AppAcctDate,
                        ReqDate = req.Msg.ReqDate,
                        ReqTime = req.Msg.ReqTime,
                        TransSsn = req.Msg.TransSsn,
                        AcctDate = req.Msg.AcctDate,
                        TransDate = req.Msg.TransDate,
                        TransTime = req.Msg.TransTime,
                        OutAcctNo = req.Msg.OutAcctNo ?? "",
                        InAcctNo = req.Msg.InAcctNo ?? "",
                        TransAmt = req.Msg.TransAmt,
                        PltfmFee = req.Msg.PltfmFee ?? "0",
                        InstFee = req.Msg.InstFee ?? "0",
                        Fee = req.Msg.Fee ?? "0",
                        TransNote = req.Msg.TransNote,
                        TransType = req.Msg.TransType
                    };
                    
                    context.Ums_Callback_Trans.Add(callbackTrans);
                    
                    _logger.LogInformation("提现token模式动账记录已保存，出金账户: {OutAcctNo}, 入金账户: {InAcctNo}, 交易金额: {TransAmt}, AppSsn: {AppSsn}", 
                        req.Msg.OutAcctNo, req.Msg.InAcctNo, req.Msg.TransAmt, req.Msg.AppSsn);
                }

            }
            await context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OpenAccountNotify: {Message}",ex.Message);
            throw;
        }

    }

    public SubAccountStatusResp GetSubAccountStatus(SubAccountStatusReq req)
    {
        var result = new SubAccountStatusResp();
        using var context = _contextFactory.CreateDbContext();
        try
        {
            var subAccount = context.SubAccount.FirstOrDefault(x => x.ExternalUserNo == req.ProjectId);
            if (subAccount == null)
            {
                result.Status = "0";
                return result;
            }
            var account = context.Ums_Complex_Upload.FirstOrDefault(x => x.AcctNo == subAccount.ParentAccountNo);
            result.Status = "1";
            result.Amount = subAccount.Amount.ToString();
            result.AcctNo = subAccount.AccountNumber;
            result.PayeeName = account?.ShopName;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GetSubAccountStatus: {Message}",ex.Message);
            throw;
        }
    }

    public async Task<TransactionResp> Transaction(TransactionReq req)
    {
        var result = new TransactionResp
        {
            OrderNo = req.OrderNo,
            TransStatus = "0" // 默认失败状态
        };

        try
        {
            // 参数验证
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(req);
            bool isValid = Validator.TryValidateObject(req, validationContext, validationResults, true);
            if (!isValid)
            {
                result.TransNote = validationResults.FirstOrDefault()?.ErrorMessage ?? "参数验证失败";
                return result;
            }

            //验证当前支付账户是否完成进件入网成功/开户成功
            //验证当前项目子账户是否完成开户
            //验证当前项目子账户金额是否充足
            //todo 个人代征则从配置中获取代征公司entid

            using var context = _contextFactory.CreateDbContext();

            var paymentAccount = context.Ums_Complex_Upload.FirstOrDefault(x => x.MerchantId == req.InEntId);
            if (paymentAccount == null)
            {
                result.TransNote = "支付账户不存在";
                return result;
            }
            if (paymentAccount.ApplyStatus != ApplyStatusEnum.入网成功)
            {
                result.TransNote = "支付账户未入网成功";
                return result;
            }
            if (paymentAccount.OpenStatus != SubAccountOpenStatus.成功)
            {
                result.TransNote = "支付账户未开户激活成功";
                return result;
            }
            if (string.IsNullOrWhiteSpace(paymentAccount.AcctNo))
            {
                result.TransNote = "入金公司基本户账户号不存在";
                return result;
            }

            var subAccount = context.SubAccount.FirstOrDefault(x => x.ExternalUserNo == req.ProjectId);
            if (subAccount == null)
            {
                result.TransNote = "子账户不存在";
                return result;
            }
            if (subAccount.OpenStatus != SubAccountOpenStatus.成功)
            {
                result.TransNote = "子账户未开户成功";
                return result;
            }
            if (subAccount.Amount < req.TransAmt)
            {
                result.TransNote = "子账户余额不足";
                return result;
            }
            if (string.IsNullOrWhiteSpace(subAccount.AccountNumber) || string.IsNullOrWhiteSpace(subAccount.ParentAccountNo))
            {
                result.TransNote = "子账户账户号不存在";
                return result;
            }
            
            // 查询入金单位是否是上级单位
            var flag = !string.IsNullOrWhiteSpace(paymentAccount.AcctNo) && paymentAccount.AcctNo.Equals(subAccount.ParentAccountNo);
            
            // 查询诺聘支付账户证书是否存在
            if (string.IsNullOrWhiteSpace(_config.NuoPinAccountNo))
            {
                throw new BadRequestException("诺聘平台的支付账户账户号未配置");
            }
            var nuoPinUmsCert = context.Ums_Cert.FirstOrDefault(x => x.AccountNo == _config.NuoPinAccountNo && x.EndTime > DateTime.Now);
            if (nuoPinUmsCert == null)
            {
                throw new BadRequestException("诺聘平台的支付账户证书不存在");
            }
            
            // 查询子账户上级支付账户的证书是否存在
            var parentUmsCert = context.Ums_Cert.FirstOrDefault(x => x.AccountNo == subAccount.ParentAccountNo && x.EndTime > DateTime.Now);
            if (parentUmsCert == null)
            {
                throw new BadRequestException("当前项目单位支付账户证书不存在");
            }
            // 查询订单是否存在
            var orderInfo = context.OutboxMessage.FirstOrDefault(x =>
                x.OrderNo == OnePayEnumString + req.OrderNo || x.OrderNo == TransferPayString + req.OrderNo);
            if (orderInfo !=null)
            {
                throw new BadRequestException("当前订单号已存在，不允许重复");
            }
            // 直接扣减子账户金额,后期通过本地消息表保证一致性
            //subAccount.Amount -= req.TransAmt;
            var affectedRows = await context.SubAccount
                .Where(x => x.ExternalUserNo == req.ProjectId)
                .ExecuteUpdateAsync(setters =>
                    setters.SetProperty(x => x.Amount, x => x.Amount - req.TransAmt)
                        .SetProperty(x => x.UpdatedTime, DateTime.Now)
                );
            if (affectedRows == 0)
                throw new BadRequestException("扣减子账户金额失败");
            // 准备所有消息并保存到本地消息表
            var now = DateTime.Now;
            SubAccountRetrieveReq subAccountRetrieveMessage = null;
            WithholdPayReq withholdPayMessage = null;
            PayAccountPayReq payAccountPayMessage = null;
            InitiateServiceBonusRequest serviceBonusMessage = null;

            string subAccountRetrieveMessageId = "";
            string withholdPayMessageId = "";
            string payAccountPayMessageId = "";
            string serviceBonusMessageId = "";
            
            // 根据转账路径准备不同的消息
            if (flag)
            {
                var appSsn = OnePayEnumString + req.OrderNo;
                subAccountRetrieveMessage = new SubAccountRetrieveReq
                {
                    ChannelNo = _config.OpenChinaumsChannelNo,
                    AppSsn = appSsn,
                    AppAcctDate = now.ToString("yyyyMMdd"),
                    ReqDate = now.ToString("yyyyMMdd"),
                    ReqTime = now.ToString("HHmmss"),
                    AcctNo = subAccount.AccountNumber,
                    TransAmt = req.TransAmt.ToString(),
                    MchntOrderNo = req.OrderNo,
                    TransNote = string.IsNullOrWhiteSpace(req.Remark) ? null : req.Remark,
                    
                };
                subAccountRetrieveMessageId =  await _messagePublishService.PublishAsync(subAccountRetrieveMessage, ApiTypes.OpenChinaums, subAccountRetrieveMessage.AppSsn);
                
            }
            else
            {
                // 通过诺聘平台中转 - 准备第一步消息
                var certSign = CertificateUtil.Sign(parentUmsCert.Dn+now.ToString("yyyyMMddHHmmss")+req.OrderNo+req.TransAmt+subAccount.AccountNumber,
                    CertificateUtil.GetPrivateKey(parentUmsCert.PfxByte, "", parentUmsCert.PassWord)!);
                
                withholdPayMessage = new WithholdPayReq
                {
                    ChannelNo = _config.OpenChinaumsChannelNo,
                    AppSsn = TransferPayString+req.OrderNo,
                    AppAcctDate = now.ToString("yyyyMMdd"),
                    ReqDate = now.ToString("yyyyMMdd"),
                    ReqTime = now.ToString("HHmmss"),
                    AcctNo = subAccount.AccountNumber,
                    InAcctNo = _config.NuoPinAccountNo,
                    TransAmt = req.TransAmt.ToString(),
                    PltfmFee = req.PltfmFee.ToString(),
                    InstFee = req.InstFee.ToString(),
                    MchntOrderNo = req.OrderNo,
                    Dn = parentUmsCert.Dn,
                    RandomCode = now.ToString("yyyyMMddHHmmss"),
                    CertSign = certSign,
                    TransNote = string.IsNullOrWhiteSpace(req.Remark) ? null : req.Remark,
                    //GoodsInfo = "",
                    //RiskInfoEnc = "",
                };
                
                // 新增：仅当目标账户不是诺聘平台账户时才构造第二步转账消息
                if (!paymentAccount.AcctNo.Equals(_config.NuoPinAccountNo))
                {
                    var nuoPinCertSign = CertificateUtil.Sign(nuoPinUmsCert.Dn+now.ToString("yyyyMMddHHmmss")+req.OrderNo+req.TransAmt,
                        CertificateUtil.GetPrivateKey(nuoPinUmsCert.PfxByte, "", nuoPinUmsCert.PassWord)!);
                    
                    payAccountPayMessage = new PayAccountPayReq()
                    {
                        ChannelNo = _config.OpenChinaumsChannelNo,
                        AppSsn = TransferPay2String+req.OrderNo,
                        AppAcctDate = now.ToString("yyyyMMdd"),
                        ReqDate = now.ToString("yyyyMMdd"),
                        ReqTime = now.ToString("HHmmss"),
                        AcctNo = _config.NuoPinAccountNo,
                        InAcctNo = paymentAccount.AcctNo,
                        TransAmt = req.TransAmt.ToString(),
                        PltfmFee = req.PltfmFee.ToString(),
                        InstFee = req.InstFee.ToString(),
                        MchntOrderNo = req.OrderNo,
                        Dn = nuoPinUmsCert.Dn,
                        RandomCode = now.ToString("yyyyMMddHHmmss"),
                        CertSign = nuoPinCertSign,
                        TransNote = string.IsNullOrWhiteSpace(req.Remark) ? null : req.Remark,
                        //GoodsInfo = "",
                        //RiskInfoEnc = "",
                    };
                }
                
                // 保存消息到本地消息表
                withholdPayMessageId = await _messagePublishService.PublishAsync(withholdPayMessage, ApiTypes.OpenChinaums, withholdPayMessage.AppSsn);
                if (payAccountPayMessage != null)
                {
                    payAccountPayMessageId = await _messagePublishService.PublishAsync(payAccountPayMessage, ApiTypes.OpenChinaums, payAccountPayMessage.AppSsn);
                }
            }
            
            // 如果是内部类型请求，准备并保存服务奖金消息 需要扣除税点
            if (req.Type == TypeEnum.内部)
            {
                var baseAmount = req.TransAmt / 100m; // 转换为元
                var issuedAmount = baseAmount;
                
                // 如果启用了税点扣除且配置存在
                if (_config.ServiceBonusTax?.EnableTaxDeduction == true)
                {
                    var taxDeductionRate = _config.ServiceBonusTax.TaxDeductionRate;
                    var deductedAmount = baseAmount * (1 - taxDeductionRate);
                    
                    // 检查最小和最大扣除金额限制
                    var deductionAmount = baseAmount - deductedAmount;
                    var deductionAmountInCents = (long)(deductionAmount * 100);
                    
                    if (deductionAmountInCents >= _config.ServiceBonusTax.MinTaxDeductionAmount)
                    {
                        if (_config.ServiceBonusTax.MaxTaxDeductionAmount == 0 || 
                            deductionAmountInCents <= _config.ServiceBonusTax.MaxTaxDeductionAmount)
                        {
                            issuedAmount = deductedAmount;
                        }
                    }
                }
                
                // 保留两位小数，其他全部舍弃（不四舍五入）
                issuedAmount = Math.Truncate(issuedAmount * 100) / 100;
                
                serviceBonusMessage = new InitiateServiceBonusRequest
                {
                    ContractCode = req.ContractCode,
                    AgentHrNo = req.AgentHrNo,
                    OrderNumber = req.OrderNo,
                    IssueList = new() { new()
                    {
                        HrNo = req.HrNo,
                        IssuedAmount = issuedAmount,
                        Remark = req.Remark,
                    } }
                };
                
                serviceBonusMessageId = await _messagePublishService.PublishAsync(serviceBonusMessage, ApiTypes.NovaPin, req.OrderNo);
            }
            
            // 保存所有消息到数据库
            await context.SaveChangesAsync();
            
            // 执行API调用
            if (flag)
            {
                // 直接转账 - 调用API
                var onePayResult = await OnePay(context, result, subAccountRetrieveMessageId, subAccountRetrieveMessage!,
                    req, subAccount.AccountNumber!, subAccount.ParentAccountNo!);
                if (onePayResult.TransStatus != "1")
                {
                    return onePayResult; // 如果失败，直接返回错误结果
                }

            }
            else
            {
                // 通过诺聘平台中转 - 调用API
                var transferPayResult = await TransferPay(context, result,
                    withholdPayMessageId, payAccountPayMessageId, withholdPayMessage!, payAccountPayMessage,
                    req, subAccount.AccountNumber!, paymentAccount.AcctNo);
                if (transferPayResult.TransStatus != "1")
                {
                    return transferPayResult; // 如果失败，直接返回错误结果
                }
            }
            
            // 如果是内部类型请求，调用服务奖金API
            if (req.Type == TypeEnum.内部 && serviceBonusMessage != null)
            {
                // 创建服务奖金发放记录
                var serviceBonusRecord = new Service_Bonus_Record
                {
                    OrderNumber = req.OrderNo,
                    MerchantId = req.InEntId,
                    ContractCode = req.ContractCode,
                    AgentHrNo = req.AgentHrNo,
                    BeneficiaryHrNo = req.HrNo,
                    IssuedAmount = (long)(serviceBonusMessage.IssueList.First().IssuedAmount * 100), // 转换为分
                    Remark = req.Remark,
                    Status = ServiceBonusStatus.待发起,
                    CreatedTime = DateTime.Now,
                    UpdatedTime = DateTime.Now
                };
                
                // 保存记录到数据库
                context.Service_Bonus_Record.Add(serviceBonusRecord);
                await context.SaveChangesAsync();

                var serviceBonus = await _novaPinApi.InitiateServiceBonus(serviceBonusMessage);
                
                // 更新服务奖金发放记录
                serviceBonusRecord.InitiatedTime = DateTime.Now;
                serviceBonusRecord.ResponseCode = serviceBonus.Code;
                serviceBonusRecord.ResponseMessage = serviceBonus.Msg;
                serviceBonusRecord.ResponseData = serviceBonus.Data?.ToString();
                serviceBonusRecord.UpdatedTime = DateTime.Now;
                
                var serviceBonusMessageResult = context.OutboxMessage.FirstOrDefault(x => x.Id == serviceBonusMessageId);
                if (!200.Equals(serviceBonus.Code))
                {
                    // 更新记录为失败状态
                    serviceBonusRecord.Status = ServiceBonusStatus.发起失败;
                    serviceBonusRecord.LastErrorMessage = $"错误码: {serviceBonus.Code}, 错误信息: {serviceBonus.Msg}";
                    
                    _logger.LogError("服务奖金发起失败，错误码: {ErrorCode}, 错误信息: {ErrorInfo}",
                        serviceBonus.Code, serviceBonus.Msg);
                    if (serviceBonusMessageResult != null)
                    {
                        serviceBonusMessageResult.Status = OutboxMessageStatus.Failed;
                        serviceBonusMessageResult.ErrorMessage = $"错误码: {serviceBonus.Code}, 错误信息: {serviceBonus.Msg}";
                    }
                    
                    await context.SaveChangesAsync();
                    
                    // 虽然转账成功，但服务奖金发放失败，返回错误状态
                    result.TransStatus = "0";
                    result.TransNote = $"转账成功，但服务奖金发放失败: {serviceBonus.Msg}";
                    return result;
                }
                
                // 更新记录为成功状态
                serviceBonusRecord.Status = ServiceBonusStatus.发起成功;
                serviceBonusRecord.CompletedTime = DateTime.Now;
                
                if (serviceBonusMessageResult != null)
                {
                    serviceBonusMessageResult.Status = OutboxMessageStatus.Completed;
                }
                
                await context.SaveChangesAsync();
            }
            // 所有操作都成功
            result.TransStatus = "1";
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Transaction: {Message}", ex.Message);
            result.TransStatus = "0";
            result.TransNote = $"交易处理失败: {ex.Message}";
            return result;
        }
    }

    private async Task<TransactionResp> OnePay(
        NkpContext context,
        TransactionResp result,
        string subAccountRetrieveMessageId,
        SubAccountRetrieveReq subAccountRetrieveMessage,
        TransactionReq originalReq,
        string fromAccountNo,
        string toAccountNo)
    {
        var operationId = Guid.NewGuid().ToString();
        _logger.LogInformation("开始执行OnePay直接转账 [OperationId: {OperationId}], 订单号: {OrderNo}, 消息ID: {MessageId}, AppSsn: {AppSsn}, 金额: {Amount}分", 
            operationId, originalReq.OrderNo, subAccountRetrieveMessageId, subAccountRetrieveMessage.AppSsn, originalReq.TransAmt);

        // 获取已保存的消息
        var subAccountRetrieveMessageResult = context.OutboxMessage.FirstOrDefault(x => x.Id == subAccountRetrieveMessageId);
        if (subAccountRetrieveMessageResult == null)
        {
            _logger.LogWarning("OnePay执行失败 [OperationId: {OperationId}], 未找到对应的消息记录, 消息ID: {MessageId}", 
                operationId, subAccountRetrieveMessageId);
        }

        try
        {
            // 调用API前记录
            _logger.LogInformation("准备调用SubAccountRetrieve API [OperationId: {OperationId}], AppSsn: {AppSsn}, 出金账户: {FromAccount}, 入金账户: {ToAccount}", 
                operationId, subAccountRetrieveMessage.AppSsn, fromAccountNo, toAccountNo);
                
            // 调用API，使用已创建的消息对象
            //如果是子账户转账到上级账户使用资金归集接口
            var subAccountRetrieveResult = await _openChinaumsApi.SubAccountRetrieve(subAccountRetrieveMessage);
            
            // API调用后记录结果
            _logger.LogInformation("SubAccountRetrieve API调用完成 [OperationId: {OperationId}], AppSsn: {AppSsn}, 状态: {Status}, 错误信息: {ErrorInfo}", 
                operationId, subAccountRetrieveMessage.AppSsn, subAccountRetrieveResult.TransStatus, subAccountRetrieveResult.FailReason ?? "无");
                
            // 更新消息状态
            if (subAccountRetrieveResult.TransStatus != "1")
            {
                _logger.LogError("子账户转账支付失败 [OperationId: {OperationId}], 错误码: {ErrorCode}, 错误信息: {ErrorInfo}, AppSsn: {AppSsn}, 出金账户: {FromAccount}, 入金账户: {ToAccount}", 
                    operationId, subAccountRetrieveResult.TransStatus, subAccountRetrieveResult.FailReason, 
                    subAccountRetrieveMessage.AppSsn, fromAccountNo, toAccountNo);

                if (subAccountRetrieveMessageResult != null)
                {
                    _logger.LogInformation("更新消息状态为失败 [OperationId: {OperationId}], 消息ID: {MessageId}", 
                        operationId, subAccountRetrieveMessageId);
                    subAccountRetrieveMessageResult.Status = OutboxMessageStatus.Failed;
                    subAccountRetrieveMessageResult.ErrorMessage = subAccountRetrieveResult.FailReason;
                    await context.SaveChangesAsync();
                }

                result.TransStatus = subAccountRetrieveResult.TransStatus;
                result.TransNote = subAccountRetrieveResult.FailReason;
                return result;
            }

            // 只有成功时才创建和保存动账记录
            _logger.LogInformation("创建交易记录 [OperationId: {OperationId}], AppSsn: {AppSsn}, 交易流水号: {TransSsn}", 
                operationId, subAccountRetrieveMessage.AppSsn, subAccountRetrieveResult.TransSsn ?? "未返回");
                
            var transactionRecord = CreateTransactionRecord(
                originalReq,
                fromAccountNo,
                toAccountNo,
                "OnePay",
                "SubAccountRetrieve",
                subAccountRetrieveMessage.AppSsn ?? "",
                subAccountRetrieveResult);

            context.TransactionRecord.Add(transactionRecord);

            if (subAccountRetrieveMessageResult != null)
            {
                _logger.LogInformation("更新消息状态为完成 [OperationId: {OperationId}], 消息ID: {MessageId}", 
                    operationId, subAccountRetrieveMessageId);
                subAccountRetrieveMessageResult.Status = OutboxMessageStatus.Completed;
            }

            await context.SaveChangesAsync();
            
            _logger.LogInformation("OnePay直接转账处理成功 [OperationId: {OperationId}], 订单号: {OrderNo}, AppSsn: {AppSsn}", 
                operationId, originalReq.OrderNo, subAccountRetrieveMessage.AppSsn);

            result.TransStatus = "1";
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OnePay API调用异常 [OperationId: {OperationId}], 订单号: {OrderNo}, AppSsn: {AppSsn}, 异常信息: {Message}", 
                operationId, originalReq.OrderNo, subAccountRetrieveMessage.AppSsn, ex.Message);

            result.TransStatus = "0";
            result.TransNote = $"转账API调用失败: {ex.Message}";
            return result;
        }
    }

    private async Task<TransactionResp> TransferPay(
        NkpContext context,
        TransactionResp result,
        string withholdPayMessageId,
        string payAccountPayMessageId,
        WithholdPayReq withholdPayMessage,
        PayAccountPayReq? payAccountPayMessage,
        TransactionReq originalReq,
        string fromAccountNo,
        string toAccountNo)
    {
        var operationId = Guid.NewGuid().ToString();
        _logger.LogInformation("开始执行TransferPay中转转账 [OperationId: {OperationId}], 订单号: {OrderNo}, 第一步消息ID: {Step1MessageId}, 第二步消息ID: {Step2MessageId}", 
            operationId, originalReq.OrderNo, withholdPayMessageId, payAccountPayMessageId);
            
        // 获取已保存的第一个消息
        var withholdPayMessageResult = context.OutboxMessage.FirstOrDefault(x => x.Id == withholdPayMessageId);
        if (withholdPayMessageResult == null)
        {
            _logger.LogWarning("TransferPay执行失败 [OperationId: {OperationId}], 未找到第一步消息记录, 消息ID: {MessageId}", 
                operationId, withholdPayMessageId);
        }
        
        try
        {
            // 调用第一个API前记录
            _logger.LogInformation("准备调用WithholdPay API [OperationId: {OperationId}], AppSsn: {AppSsn}, 源账户: {FromAccount}, 中间账户: {MidAccount}, 金额: {Amount}分", 
                operationId, withholdPayMessage.AppSsn, fromAccountNo, _config.NuoPinAccountNo, originalReq.TransAmt);
                
            // 调用第一个API，使用已创建的消息对象
            var withholdPayResult = await _openChinaumsApi.WithholdPay(withholdPayMessage);
            
            // API调用后记录结果
            _logger.LogInformation("WithholdPay API调用完成 [OperationId: {OperationId}], AppSsn: {AppSsn}, 状态: {Status}, 错误信息: {ErrorInfo}", 
                operationId, withholdPayMessage.AppSsn, withholdPayResult.TransStatus, withholdPayResult.FailReason ?? "无");

            // 更新第一个消息状态
            if (withholdPayResult.TransStatus != "1")
            {
                _logger.LogError("子账户转账支付失败 [OperationId: {OperationId}], 错误码: {ErrorCode}, 错误信息: {ErrorInfo}, AppSsn: {AppSsn}, 源账户: {FromAccount}, 中间账户: {MidAccount}", 
                    operationId, withholdPayResult.TransStatus, withholdPayResult.FailReason, 
                    withholdPayMessage.AppSsn, fromAccountNo, _config.NuoPinAccountNo);

                if (withholdPayMessageResult != null)
                {
                    _logger.LogInformation("更新第一步消息状态为失败 [OperationId: {OperationId}], 消息ID: {MessageId}", 
                        operationId, withholdPayMessageId);
                    withholdPayMessageResult.Status = OutboxMessageStatus.Failed;
                    withholdPayMessageResult.ErrorMessage = withholdPayResult.FailReason;
                    await context.SaveChangesAsync();
                }

                result.TransStatus = withholdPayResult.TransStatus;
                result.TransNote = withholdPayResult.FailReason;
                return result;
            }

            // 第一步成功，创建第一条动账记录：从子账户到诺聘平台中间账户
            _logger.LogInformation("第一步转账成功，创建第一步交易记录 [OperationId: {OperationId}], AppSsn: {AppSsn}, 交易流水号: {TransSsn}", 
                operationId, withholdPayMessage.AppSsn, withholdPayResult.TransSsn ?? "未返回");
                
            var firstTransactionRecord = CreateTransactionRecord(
                originalReq,
                fromAccountNo,
                _config.NuoPinAccountNo, // 中间账户
                "TransferPay_Step1",
                "WithholdPay",
                withholdPayMessage.AppSsn ?? "",
                withholdPayResult);

            context.TransactionRecord.Add(firstTransactionRecord);

            if (withholdPayMessageResult != null)
            {
                _logger.LogInformation("更新第一步消息状态为完成 [OperationId: {OperationId}], 消息ID: {MessageId}", 
                    operationId, withholdPayMessageId);
                withholdPayMessageResult.Status = OutboxMessageStatus.Completed;
            }

            // 新增：如果没有第二步转账消息，直接返回成功
            if (payAccountPayMessage == null || string.IsNullOrWhiteSpace(payAccountPayMessageId))
            {
                _logger.LogInformation("无需执行第二步转账 [OperationId: {OperationId}], 目标账户即为中间账户, 中转转账完成", 
                    operationId);
                await context.SaveChangesAsync();
                result.TransStatus = "1";
                return result;
            }

            // 获取已保存的第二个消息
            var payAccountPayMessageResult = context.OutboxMessage.FirstOrDefault(x => x.Id == payAccountPayMessageId);
            if (payAccountPayMessageResult == null)
            {
                _logger.LogWarning("第二步消息记录未找到 [OperationId: {OperationId}], 消息ID: {MessageId}, 但将继续执行API调用", 
                    operationId, payAccountPayMessageId);
            }

            // 调用第二个API前记录
            _logger.LogInformation("准备调用PayAccountPay API [OperationId: {OperationId}], AppSsn: {AppSsn}, 中间账户: {MidAccount}, 目标账户: {ToAccount}, 金额: {Amount}分", 
                operationId, payAccountPayMessage!.AppSsn, _config.NuoPinAccountNo, toAccountNo, originalReq.TransAmt);
                
            // 调用第二个API，使用已创建的消息对象
            var payAccountPayResult = await _openChinaumsApi.PayAccountPay(payAccountPayMessage!);
            
            // API调用后记录结果
            _logger.LogInformation("PayAccountPay API调用完成 [OperationId: {OperationId}], AppSsn: {AppSsn}, 状态: {Status}, 错误信息: {ErrorInfo}", 
                operationId, payAccountPayMessage.AppSsn, payAccountPayResult.TransStatus, payAccountPayResult.FailReason ?? "无");

            // 更新第二个消息状态
            if (payAccountPayResult.TransStatus != "1")
            {
                _logger.LogError("单位支付账户转账支付失败 [OperationId: {OperationId}], 错误码: {ErrorCode}, 错误信息: {ErrorInfo}, AppSsn: {AppSsn}, 中间账户: {MidAccount}, 目标账户: {ToAccount}", 
                    operationId, payAccountPayResult.TransStatus, payAccountPayResult.FailReason, 
                    payAccountPayMessage.AppSsn, _config.NuoPinAccountNo, toAccountNo);

                if (payAccountPayMessageResult != null)
                {
                    _logger.LogInformation("更新第二步消息状态为失败 [OperationId: {OperationId}], 消息ID: {MessageId}", 
                        operationId, payAccountPayMessageId);
                    payAccountPayMessageResult.Status = OutboxMessageStatus.Failed;
                    payAccountPayMessageResult.ErrorMessage = payAccountPayResult.FailReason;
                    await context.SaveChangesAsync();
                }

                // 第一步已成功，但第二步失败，记录详细错误信息
                _logger.LogWarning("中转转账部分成功 [OperationId: {OperationId}], 第一步成功但第二步失败, 资金已到达中间账户 {MidAccount}, 需要手动处理", 
                    operationId, _config.NuoPinAccountNo);
                result.TransStatus = payAccountPayResult.TransStatus;
                result.TransNote = $"中转支付第二步失败: {payAccountPayResult.FailReason}，资金已到达中间账户，请联系管理员处理";
                return result;
            }

            // 第二步成功，创建第二条动账记录：从诺聘平台中间账户到最终目标账户
            _logger.LogInformation("第二步转账成功，创建第二步交易记录 [OperationId: {OperationId}], AppSsn: {AppSsn}, 交易流水号: {TransSsn}", 
                operationId, payAccountPayMessage.AppSsn, payAccountPayResult.TransSsn ?? "未返回");
                
            var secondTransactionRecord = CreateTransactionRecord(
                originalReq,
                _config.NuoPinAccountNo, // 中间账户
                toAccountNo, // 最终目标账户
                "TransferPay_Step2",
                "PayAccountPay",
                payAccountPayMessage.AppSsn ?? "",
                payAccountPayResult);

            context.TransactionRecord.Add(secondTransactionRecord);

            if (payAccountPayMessageResult != null)
            {
                _logger.LogInformation("更新第二步消息状态为完成 [OperationId: {OperationId}], 消息ID: {MessageId}", 
                    operationId, payAccountPayMessageId);
                payAccountPayMessageResult.Status = OutboxMessageStatus.Completed;
            }

            await context.SaveChangesAsync();
            
            _logger.LogInformation("TransferPay中转转账处理成功完成 [OperationId: {OperationId}], 订单号: {OrderNo}, 源账户: {FromAccount}, 中间账户: {MidAccount}, 目标账户: {ToAccount}", 
                operationId, originalReq.OrderNo, fromAccountNo, _config.NuoPinAccountNo, toAccountNo);

            result.TransStatus = "1";
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "TransferPay API调用异常 [OperationId: {OperationId}], 订单号: {OrderNo}, 异常信息: {Message}", 
                operationId, originalReq.OrderNo, ex.Message);

            result.TransStatus = "0";
            result.TransNote = $"转账API调用失败: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 创建动账记录（成功状态）
    /// </summary>
    private static TransactionRecord CreateTransactionRecord(
        TransactionReq req,
        string fromAccountNo,
        string toAccountNo,
        string transMethod,
        string apiType,
        string appSsn,
        object apiResponse)
    {
        var record = new TransactionRecord
        {
            OrderNo = req.OrderNo,
            AppSsn = appSsn,
            FromAccountNo = fromAccountNo,
            ToAccountNo = toAccountNo,
            TransAmt = req.TransAmt,
            PltfmFee = req.PltfmFee,
            InstFee = req.InstFee,
            TransType = (int)req.Type,
            TransStatus = "1", // 成功状态
            TransMethod = transMethod,
            ApiType = apiType,
            TransNote = req.Remark,
            InEntId = req.InEntId,
            InEntName = req.InEntName,
            ApiResponse = JsonSerializer.Serialize(apiResponse),
            CreatedTime = DateTime.Now,
            UpdatedTime = DateTime.Now
        };

        // 根据不同的API响应类型设置字段
        switch (apiResponse)
        {
            case SubAccountRetrieveResp subResp:
                record.TransSsn = subResp.TransSsn;
                record.AppAcctDate = subResp.AppAcctDate;
                record.AcctDate = subResp.AcctDate;
                record.TransDate = subResp.TransDate;
                record.TransTime = subResp.TransTime;
                record.BalanceAmt = subResp.BalanceAmt;
                record.ErrorCode = subResp.ErrorCode;
                record.ErrorInfo = subResp.ErrorInfo;
                break;
            case WithholdPayResp withholdResp:
                record.TransSsn = withholdResp.TransSsn;
                record.AppAcctDate = withholdResp.AppAcctDate;
                record.AcctDate = withholdResp.AcctDate;
                record.TransDate = withholdResp.TransDate;
                record.TransTime = withholdResp.TransTime;  
                record.BalanceAmt = withholdResp.BalanceAmt;
                record.ErrorCode = withholdResp.ErrorCode;
                record.ErrorInfo = withholdResp.ErrorInfo;
                break;
            case PayAccountPayResp payResp:
                record.TransSsn = payResp.TransSsn;
                record.AppAcctDate = payResp.AppAcctDate;
                record.AcctDate = payResp.AcctDate;
                record.TransDate = payResp.TransDate;
                record.TransTime = payResp.TransTime;
                record.BalanceAmt = payResp.BalanceAmt;
                record.ErrorCode = payResp.ErrorCode;
                record.ErrorInfo = payResp.ErrorInfo;
                break;
        }

        return record;
    }


}
