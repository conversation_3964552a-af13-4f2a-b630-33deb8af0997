using Entity.Nkp;

namespace Settlement.Application.Service;

/// <summary>
/// API调用服务接口
/// </summary>
public interface IApiCallService
{
    /// <summary>
    /// 处理消息并调用对应的第三方API
    /// </summary>
    /// <param name="message">本地消息表消息</param>
    /// <returns>是否处理成功</returns>
    Task<bool> ProcessMessageAsync(OutboxMessage message);

    /// <summary>
    /// 根据API类型获取处理器
    /// </summary>
    /// <param name="apiType">API类型</param>
    /// <returns>API处理器</returns>
    IApiHandler? GetHandler(string apiType);
}

/// <summary>
/// API处理器接口
/// </summary>
public interface IApiHandler
{
    /// <summary>
    /// API类型
    /// </summary>
    string ApiType { get; }

    /// <summary>
    /// 处理消息
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="content">消息内容</param>
    /// <returns>是否处理成功</returns>
    Task<bool> HandleAsync(string messageType, string content);
}

/// <summary>
/// API处理器基类
/// </summary>
public abstract class ApiHandlerBase : IApiHandler
{
    public abstract string ApiType { get; }

    public abstract Task<bool> HandleAsync(string messageType, string content);

    /// <summary>
    /// 记录处理日志
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="content">消息内容</param>
    /// <param name="success">是否成功</param>
    /// <param name="error">错误信息</param>
    protected virtual void LogProcessing(string messageType, string content, bool success, string? error = null)
    {
        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ApiType: {ApiType}, MessageType: {messageType}, Success: {success}, Error: {error}");
    }
}
