﻿using Config;
using Entity;
using Entity.Nkp;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ServiceStack;
using Settlement.Domain.Transaction;
using Settlement.Infrastructure.Common;
using Settlement.Infrastructure.Model;
using Settlement.Infrastructure.Proxy;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Security.Cryptography;

namespace Settlement.Application.Service
{
    /// <summary>
    /// 企业支付账户证书表
    /// </summary>
    [Service(ServiceLifetime.Scoped)]
    public class UmsCertService : IUmsCertService
    {
        private readonly IDbContextFactory<NkpContext> _contextFactory;
        private readonly OpenChinaumsApi _openChinaumsApi;
        private readonly ILogger<UmsCertService> _logger;
        private readonly RequestContext _user;
        private readonly ConfigManager _config;

        public UmsCertService(
            IDbContextFactory<NkpContext> contextFactory,
            OpenChinaumsApi openChinaumsApi,
            ILogger<UmsCertService> logger,
            IOptionsSnapshot<ConfigManager> config,
            RequestContext user)
        {
            _contextFactory = contextFactory;
            _openChinaumsApi = openChinaumsApi;
            _logger = logger;
            _user = user;
            _config = config.Value;
        }

        /// <summary>
        /// 保存证书
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public async Task<Ums_Cert> Save(UmsCerReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);
            req.EndTime = req.EndTime!.Value.Date.AddDays(1).AddSeconds(-1);
            if (!req.PublicKeyString!.Contains("-----BEGIN CERTIFICATE-----"))
                throw new BadRequestException($"公钥错误，应为*.cer文件中复制的以----BEGIN CERTIFICATE---开头的字符串");

            //简单流控
            if (!MyRedis.Client.SetNx($"ums:Saveumscer:{_user.Id}", DateTime.Now, TimeSpan.FromSeconds(5)))
                throw new BadRequestException($"操作太频繁了，请稍后重试");

            using var context = _contextFactory.CreateDbContext();
            Ums_Cert? model;
            try
            {
                var complex = context.Ums_Complex_Upload.FirstOrDefault(x => x.MerchantId == req.MerchantId);
                if (complex == null)
                    throw new BadRequestException("企业尚无银联入网信息，请待企业入网且成功后再操作");
                else if (complex.ApplyStatus != ApplyStatusEnum.入网成功)
                    throw new BadRequestException("企业尚未入网成功，请待企业入网成功后再操作");
                else if (string.IsNullOrEmpty(complex.AcctNo))
                    throw new BadRequestException("企业入网成功后尚未创建账号，请耐心等待1~2分钟后重试");

                model = context.Ums_Cert.FirstOrDefault(x => x.MerchantId == complex.MerchantId);

                //仅在新增时必须传此参数
                if (model == null && (req.PfxByte == null || req.PfxByte.Length == 0))
                    throw new SecurityException(".pfx文件不能为空");

                //将上传的文件转为字节数组
                byte[]? PfxByte = null;
                string PfxFileName = string.Empty;
                if (req.PfxByte != null && req.PfxByte.Length > 0)
                {
                    PfxByte = CertificateUtil.GetCertBytes(req.PfxByte!.OpenReadStream());
                    PfxFileName = ((Microsoft.AspNetCore.Http.FormFile)req.PfxByte).FileName;//contentType=application/x-pkcs12
                }
                _logger.LogInformation("用户【{userid}】上传证书：{req},证书Base64字符串:{PfxByte}", _user.Id, req.ToJson(), (PfxByte == null ? string.Empty : Convert.ToBase64String(PfxByte)));

                RSA? privateRSA = null;
                //判断证书是否变更，变更时验证能否获取到私钥
                if (model == null || (PfxByte != null && !model.PfxByte.SequenceEqual(PfxByte)) || model.PassWord != req.PassWord)
                {
                    try
                    {
                        privateRSA = CertificateUtil.GetPrivateKey(PfxByte!, "", req.PassWord);
                    }
                    catch
                    {
                    }
                    if (privateRSA == null)
                        throw new SecurityException("证书或密码错误");
                }
                //判断公钥是否变更，变更时验证公钥是否正确
                if (model == null || model.PublicKeyString != req.PublicKeyString
                    || privateRSA != null)//privateRSA!=null说明私钥变更了
                {
                    RSA publicRSA;
                    //判断能否获取公钥
                    try
                    {
                        publicRSA = CertificateUtil.GeneratePublicKey(req.PublicKeyString!);
                    }
                    catch
                    {
                        throw new BadRequestException($"公钥错误，应为*.cer文件中的内容");
                    }
                    //根据私钥生成签名
                    if (privateRSA == null)
                        privateRSA = CertificateUtil.GetPrivateKey(PfxByte!, "", req.PassWord);
                    const string tempStr = "123456";
                    var tempSign = CertificateUtil.Sign(tempStr, privateRSA!);

                    //验证公钥能否验签成功 
                    var vr = CertificateUtil.VerifyRsa(tempSign, publicRSA, tempStr);
                    if (!vr)
                        throw new BadRequestException($"公钥验证失败，请检查是否为*.cer文件中的内容");
                }

                if (model == null)
                {
                    model = new Ums_Cert
                    {
                        UserId = _user.Id,
                        AccountNo = complex.AcctNo ?? string.Empty,
                        MerchantId = complex.MerchantId,
                        UmsRegId = complex.UmsRegId,

                        Dn = req.Dn ?? string.Empty,
                        PassWord = req.PassWord ?? string.Empty,
                        PfxByte = PfxByte!,
                        PfxFileName = PfxFileName,
                        PublicKeyString = req.PublicKeyString ?? string.Empty,
                        EndTime = req.EndTime!.Value
                    };
                    context.Add(model);
                }
                else
                {
                    model.Dn = req.Dn ?? string.Empty;
                    model.PassWord = req.PassWord ?? string.Empty;
                    model.PublicKeyString = req.PublicKeyString ?? string.Empty;
                    model.EndTime = req.EndTime!.Value;
                    model.UpdateTime = DateTime.Now;

                    if (PfxByte != null)
                    {
                        model.PfxByte = PfxByte;
                        model.PfxFileName = PfxFileName;
                    }
                }
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存证书，用户:{userId},记录: {req}", _user.Id, req.ToJson());
                throw;
            }
            return model;
        }

        /// <summary>
        /// 获取证书
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public Ums_Cert? GetCert(GetUmsCerReq req)
        {
            using var context = _contextFactory.CreateDbContext();
            Ums_Cert? model = context.Ums_Cert.FirstOrDefault(x => x.MerchantId == req.MerchantId);
            return model;
        }

        /// <summary>
        /// 查询单位账户支付账户的证书是否存在
        /// </summary>
        /// <param name="AccountNo"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public UmsCerRespWithKey GetCertByANo(string AccountNo)
        {
            using var context = _contextFactory.CreateDbContext();
            // 查询子账户上级支付账户的证书是否存在
            var parentUmsCert = context.Ums_Cert.FirstOrDefault(x => x.AccountNo == AccountNo && x.EndTime > DateTime.Now);
            if (parentUmsCert == null)
            {
                throw new BadRequestException("当前项目单位支付账户证书不存在");
            }

            return GetUmsCerRespWithKey(parentUmsCert!)!;
        }

        /// <summary>
        /// 查询子账户上级支付账户的证书是否存在
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public UmsCerRespWithKey? GetCertByPro(string ProjectId)
        {
            using var context = _contextFactory.CreateDbContext();
            var subAccount = context.SubAccount.FirstOrDefault(x => x.ExternalUserNo == ProjectId);
            if (subAccount == null)
            {
                throw new BadRequestException("子账户不存在");
            }
            if (subAccount.OpenStatus != SubAccountOpenStatus.成功)
            {
                throw new BadRequestException("子账户未开户成功");
            }
            if (string.IsNullOrWhiteSpace(subAccount.AccountNumber) || string.IsNullOrWhiteSpace(subAccount.ParentAccountNo))
            {
                throw new BadRequestException("子账户账户号不存在");
            }
            // 查询子账户上级支付账户的证书是否存在
            var parentUmsCert = context.Ums_Cert.FirstOrDefault(x => x.AccountNo == subAccount.ParentAccountNo && x.EndTime > DateTime.Now);
            if (parentUmsCert == null)
            {
                throw new BadRequestException("当前项目单位支付账户证书不存在");
            }

            return GetUmsCerRespWithKey(parentUmsCert);
        }

        /// <summary>
        /// 查询诺聘支付账户证书是否存在
        /// </summary>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public UmsCerRespWithKey? GetCertByNuoPin()
        {
            // 查询诺聘支付账户证书是否存在
            if (string.IsNullOrWhiteSpace(_config.NuoPinAccountNo))
            {
                throw new BadRequestException("诺聘平台的支付账户账户号未配置");
            }
            using var context = _contextFactory.CreateDbContext();
            var nuoPinUmsCert = context.Ums_Cert.FirstOrDefault(x => x.AccountNo == _config.NuoPinAccountNo && x.EndTime > DateTime.Now);
            if (nuoPinUmsCert == null)
            {
                throw new BadRequestException("诺聘平台的支付账户证书不存在");
            }
            return GetUmsCerRespWithKey(nuoPinUmsCert);
        }

        private UmsCerRespWithKey? GetUmsCerRespWithKey(Ums_Cert? Model)
        {
            if (Model == null)
                return null;

            var privateKey = CertificateUtil.GetPrivateKey(Model.PfxByte, "", Model.PassWord);

            return new UmsCerRespWithKey(Model, privateKey);
        }
    }
}
