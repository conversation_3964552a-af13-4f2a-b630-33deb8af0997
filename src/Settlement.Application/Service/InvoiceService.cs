﻿using Config;
using Config.Enums.Nkp;
using Config.Extend;
using Entity;
using Entity.Nkp;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Infrastructure.Extend;
using LinqKit;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Nest;
using Org.BouncyCastle.Ocsp;
using ServiceStack;
using Settlement.Domain;
using Settlement.Domain.Invoice;
using Settlement.Infrastructure.Model;
using Settlement.Infrastructure.Proxy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Nest.MachineLearningUsage;

namespace Settlement.Application.Service
{
    /// <summary>
    /// 发票记录表
    /// </summary>
    [Service(ServiceLifetime.Scoped)]
    public class InvoiceService : IInvoiceService
    {
        private readonly IDbContextFactory<NkpContext> _contextFactory;
        private readonly OpenChinaumsApi _openChinaumsApi;
        private readonly ILogger<InvoiceService> _logger;
        private readonly RequestContext _user;
        private readonly ConfigManager _config;
        private readonly NovaPinApi _novaPinApi;
        private readonly CommonUserService _commonUserService;
        public InvoiceService(
            IDbContextFactory<NkpContext> contextFactory,
            OpenChinaumsApi openChinaumsApi,
            ILogger<InvoiceService> logger,
            IOptionsSnapshot<ConfigManager> config,
            RequestContext user,
            NovaPinApi novaPinApi,
            CommonUserService commonUserService)
        {
            _contextFactory = contextFactory;
            _openChinaumsApi = openChinaumsApi;
            _logger = logger;
            _user = user;
            _config = config.Value;
            _novaPinApi = novaPinApi;
            _commonUserService = commonUserService;
        }


        /// <summary>
        /// 发起开票(项目发票)
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public async Task CreateInvoiceForPro(InitiateInvoiceForProReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);

            using var context = _contextFactory.CreateDbContext();
            //根据项目ID获取合同
            var contract = GetContractByPro(context, req.ProjectId!);

            //获取商品信息
            var product = GetProduct(context);

            //发起开票
            await CreateInvoice(new InitiateInvoiceReq(req, contract.ContractNo!, contract.Initiator ?? string.Empty, product), InvoiceRecordUseScence.项目发票, req.ProjectId, null, contract.Participant);
        }

        /// <summary>
        /// 发起凭证开票流程
        /// </summary>
        /// <param name="cancel"></param>
        /// <param name="count">本次开几个凭证的发票</param>
        /// <returns></returns>
        public async Task CreateInvoiceForVoucher(CancellationToken cancel, int count = 10)
        {
            try
            {
                if (cancel.IsCancellationRequested)
                    return;

                if (count < 0)
                    count = 10;
                using var context = _contextFactory.CreateDbContext();
                var vouchers = context.Voucher.Where(x => x.InvoiceStatus == VoucherInvoiceStatus.尚未开票)
                    .OrderBy(x => x.CreatedTime).Take(count).ToList();
                string vid = string.Empty;
                try
                {
                    foreach (var item in vouchers)
                    {
                        //防止多个服务同时创建发票
                        if (!(MyRedis.Client.SetNx($"InvoiceForUms:{item.Id}", DateTime.Now, 30)))
                            continue;

                        vid = item.Id;
                        try
                        {
                            await CreateInvoiceForUms(new InitiateInvoiceForUmsReq
                            {
                                VoucherId = item.Id
                            });
                        }
                        catch (BadRequestException e)
                        {
                            continue;
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(e, "发起凭证开票流程:引发异常，凭证ID={vid}", item.Id);
                            continue;
                        }
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "发起凭证开票流程:引发异常，凭证ID={vid}", vid);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "发起凭证开票流程:引发异常");
            }
        }

        /// <summary>
        /// 发起开票(服务奖金)
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public async Task<InitiateInvoiceResponse?> CreateInvoiceForUms(InitiateInvoiceForUmsReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);

            using var context = _contextFactory.CreateDbContext();
            //获取凭证信息
            var voucher = context.Voucher.FirstOrDefault(x => x.Id == req.VoucherId) ?? throw new BadRequestException($"服务奖金开发票：未获取到凭证{req.VoucherId}，生成发票失败");

            //判断凭证是否已开发票
            if (context.Invoice_Record.Any(x => x.VoucherId == voucher.Id && (x.Status == InvoiceRecordStatus.已申请 || x.Status == InvoiceRecordStatus.处理完成)))
            {
                if (voucher.InvoiceStatus == VoucherInvoiceStatus.尚未开票)
                {
                    voucher.InvoiceStatus = VoucherInvoiceStatus.申请开票失败;
                    voucher.InvoiceRemark = "该凭证已申请发票";
                    context.SaveChanges();
                }

                _logger.LogError($"发起凭证开票流程:申请开票失败，凭证ID={voucher.Id}，原因:该凭证已申请发票，请勿重复操作");
                throw new BadRequestException($"服务奖金开发票：该凭证已申请发票，请勿重复操作");
            }

            //判断出入金是否同账户
            if (voucher.InMerchantId == voucher.OutMerchantId)
            {
                voucher.InvoiceStatus = VoucherInvoiceStatus.出入金同账户不开票;
                voucher.InvoiceRemark = VoucherInvoiceStatus.出入金同账户不开票.ToString();
                context.SaveChanges();

                throw new BadRequestException($"服务奖金开发票：出入金同账户不开票");
            }

            try
            {
                //根据商户ID获取合同
                var contract = GetContractByMerchantId(context, voucher, true);
                if (string.IsNullOrEmpty(contract.ContractNo))
                    throw new BadRequestException($"服务奖金开发票：商户尚未关联合同，请关联后重试");

                //获取商品信息
                var product = GetProduct(context);

                //发起开票
                var result = await CreateInvoice(new InitiateInvoiceReq(voucher, contract.ContractNo, contract.InvoiceInfo!, product), InvoiceRecordUseScence.服务奖金, null, voucher, voucher.InMerchantName, contract.InvoiceInfo);
                if (result?.IsSuccess() == true)
                    voucher.InvoiceStatus = VoucherInvoiceStatus.已申请开票;
                else
                {
                    voucher.InvoiceStatus = VoucherInvoiceStatus.申请开票失败;
                    _logger.LogError($"发起凭证开票流程:申请开票失败，凭证ID={voucher.Id}，申请返回结果:{result.ToJson()}");
                }
                context.SaveChanges();
                return result;
            }
            catch (Exception e)
            {
                voucher.InvoiceRemark = (e.Message?.Length > 200 ? e.Message.Substring(0, 200) : e.Message) ?? string.Empty;
                voucher.InvoiceStatus = VoucherInvoiceStatus.申请开票失败;
                context.SaveChanges();
                _logger.LogError($"发起凭证开票流程:申请开票失败，凭证ID={voucher.Id}，原因:{e.ToJson()}");
                throw;
            }
        }

        /// <summary>
        /// 获取【项目】上次开发票填写信息接口开发
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public GetProLastInvoiceResp GetLastInvoiceForPro(GetProLastInvoiceReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);
            GetProLastInvoiceResp result;
            using var context = _contextFactory.CreateDbContext();
            var rescord = context.Invoice_Record.Where(x => x.ProjectId == req.ProjectId).OrderByDescending(x => x.CreateTime).FirstOrDefault();
            if (rescord == null)
            {
                var contract = GetContractByPro(context, req.ProjectId!);
                var pro = GetProduct(context);
                rescord = new Invoice_Record
                {
                    IssuingCompany = contract.Participant ?? string.Empty,
                    BuyerName = contract.Initiator ?? string.Empty,
                    VatRate = pro.TaxRate.ToString(),
                };
                result = new(contract.ContractNo, contract.Participant, contract.Initiator, pro);
            }
            else
                result = new(rescord);
            return result;
        }

        /// <summary>
        /// 获取【项目】发票列表接口
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public RespPageBase<GetProInvoicesResp> GetInvoicesForPro(GetProInvoicesReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);
            var predicate = PredicateBuilder.New<Invoice_Record>(x => x.ProjectId == req.ProjectId && x.UseScence == InvoiceRecordUseScence.项目发票);
            using var context = _contextFactory.CreateDbContext();
            int Total = context.Invoice_Record.Count(predicate);
            var rows = context.Invoice_Record.Where(predicate)
                .OrderByDescending(x => x.CreateTime).Skip((req.PageIndex - 1) * req.PageSize).Take(req.PageSize)
                .Select(x => new GetProInvoicesResp(x))
                .ToList();
            return new RespPageBase<GetProInvoicesResp>(rows, Total, req.PageIndex, req.PageSize);
        }

        /// <summary>
        /// 获取【服务奖金】发票列表接口
        /// </summary>
        /// <param name="req"></param>
        /// <param name="MerchantType">IN=取我给别人开的票，OUT=取别人给我开的票</param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public RespPageBase<GetUmsInvoicesResp> GetInvoicesForUms(GetUmsInvoicesReq req, string MerchantType)
        {
            //验证参数
            ParamsValidation.Validate(req);

            using var context = _contextFactory.CreateDbContext();

            var predicate = PredicateBuilder.New<Voucher>(x => x.InMerchantId != x.OutMerchantId);

            if (MerchantType.ToUpper() == "IN")
                predicate.And(x => x.InMerchantId == req.MerchantId);
            else
                predicate.And(x => x.OutMerchantId == req.MerchantId);

            //组装条件
            if (req.BeginTime != null)
                predicate.And(x => x.VoucherTime >= req.BeginTime);
            if (req.EndTime != null)
                predicate.And(x => x.VoucherTime <= req.EndTime);
            if (!string.IsNullOrEmpty(req.Search))
                predicate.And(x => x.InMerchantName.Contains(req.Search) || x.OutMerchantName.Contains(req.Search));

            int Total = context.Voucher.Count(predicate);
            var linq = from v in context.Voucher.Include(x => x.OutMerchant).Where(predicate)
                       join i in context.Invoice_Record.Where(x => x.UseScence == InvoiceRecordUseScence.服务奖金) on v.Id equals i.VoucherId into tvi
                       from vi in tvi
                                   //当一个凭证有多个发票时，只取最后创建的发票
                                   //.GroupBy(x => x.VoucherId)
                                   //.Select(g => g.OrderByDescending(x => x.CreateTime).FirstOrDefault())
                                   .DefaultIfEmpty()
                       orderby v.CreatedTime descending
                       select new GetUmsInvoicesResp(vi, v);
            var rows = linq
                .Skip((req.PageIndex - 1) * req.PageSize).Take(req.PageSize)
                .ToList();

            return new RespPageBase<GetUmsInvoicesResp>(rows, Total, req.PageIndex, req.PageSize);
        }
        public RespPageBase<GetUmsInvoicesResp> GetInvoicesForUms_OLD(GetUmsInvoicesReq req, string MerchantType)
        {
            //验证参数
            ParamsValidation.Validate(req);

            using var context = _contextFactory.CreateDbContext();

            var predicate = PredicateBuilder.New<Invoice_Record>(o => o.UseScence == InvoiceRecordUseScence.服务奖金);

            if (MerchantType.ToUpper() == "IN")
                predicate.And(x => x.MerchantId == req.MerchantId);
            else
                predicate.And(x => x.BuyerMerchantId == req.MerchantId);

            //组装条件
            if (req.BeginTime != null)
                predicate.And(x => x.Voucher!.VoucherTime >= req.BeginTime);
            if (req.EndTime != null)
                predicate.And(x => x.Voucher!.VoucherTime <= req.EndTime);
            if (!string.IsNullOrEmpty(req.Search))
                predicate.And(x => x.BuyerName!.Contains(req.Search) || x.IssuingCompany.Contains(req.Search));

            int Total = context.Invoice_Record.Count(predicate);
            var rows = context.Invoice_Record.Where(predicate)
                .OrderByDescending(x => x.CreateTime).Skip((req.PageIndex - 1) * req.PageSize).Take(req.PageSize)
                .Select(x => new GetUmsInvoicesResp(x, x.Voucher!.VoucherTime))
                .ToList();
            return new RespPageBase<GetUmsInvoicesResp>(rows, Total, req.PageIndex, req.PageSize);
        }

        /// <summary>
        /// 获取发票详情接口开发
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public List<Invoice_Detail> GetInvoicesDetail(GetInvoicesDetailReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);

            using var context = _contextFactory.CreateDbContext();
            return context.Invoice_Detail.Where(x => x.RecordId == req.RecordId).ToList();
        }

        /// <summary>
        /// 根据项目获取充值/退款动账记录
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public List<Ums_Callback_Trans> GetTransForPro(GetTransForProReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);

            //获取动账记录
            using var context = _contextFactory.CreateDbContext();
            var (predicate, subAcc) = GetTransPredicate_Pro(new GetStatisticsForProReq(req), context);
            return context.Ums_Callback_Trans.Where(predicate)
                .Skip((req.PageIndex - 1) * req.PageSize)
                .Take(req.PageSize)
                .ToList();
        }

        /// <summary>
        /// 根据商户获取服务奖金相关的 充值/退款动账记录
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        public List<Ums_Callback_Trans> GetTransForUms(GetTransForUmsReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);

            using var context = _contextFactory.CreateDbContext();

            //获取筛选条件
            var predicate = GetTransPredicate_Ums(context, new GetStatisticsForUmsReq(req));
            //获取动账记录
            return context.Ums_Callback_Trans.Where(predicate)
                .Skip((req.PageIndex - 1) * req.PageSize)
                .Take(req.PageSize)
                .ToList();
        }

        /// <summary>
        /// 获取【项目】可开票金额
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public GetInvoiceStatisticsResp GetInvoiceStatisticsForPro(GetStatisticsForProReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);

            using var context = _contextFactory.CreateDbContext();

            //获取筛选条件
            var (predicate, subAcc) = GetTransPredicate_Pro(req, context);
            //todo：获取充值总额(充值金额-退款金额)
            var TransAmt = context.Ums_Callback_Trans.Where(predicate)
                    .Select(x => new { x.TransAmt, x.InAcctNo })
                    .ToList()
                    .Sum(x => decimal.Parse(x.TransAmt) * (x.InAcctNo == subAcc ? 1 : -1))
                    .RMB_FenToYuan();


            //获取发票总额
            var InvoicesAmt = context.Invoice_Record.Where(x => x.ProjectId == req.ProjectId && x.UseScence == InvoiceRecordUseScence.项目发票 && (x.Status == InvoiceRecordStatus.已申请 || x.Status == InvoiceRecordStatus.处理完成)).Sum(x => x.Amount);

            return InvoiceStatisticsCompute(TransAmt, InvoicesAmt);
        }

        /// <summary>
        /// 获取【服务奖金】可开票金额
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public GetInvoiceStatisticsResp GetInvoiceStatisticsForUms(GetStatisticsForUmsReq req)
        {
            //验证参数
            ParamsValidation.Validate(req);

            using var context = _contextFactory.CreateDbContext();

            //获取筛选条件
            var predicate = GetTransPredicate_Ums(context, req);
            //todo：获取充值总额(充值金额-退款金额)
            var TransAmt = context.Ums_Callback_Trans.Where(predicate).Sum(x => decimal.Parse(x.TransAmt)).RMB_FenToYuan();
            //获取发票总额
            var InvoicesAmt = context.Invoice_Record.Where(x => x.MerchantId == req.MerchantId && x.BuyerMerchantId == req.BuyerMerchantId && x.UseScence == InvoiceRecordUseScence.服务奖金).Sum(x => x.Amount);

            return InvoiceStatisticsCompute(TransAmt, InvoicesAmt);
        }

        /// <summary>
        /// 根据动账金额、已开票金额，生成返回结果
        /// </summary>
        /// <param name="TransAmt"></param>
        /// <param name="InvoicesAmt"></param>
        /// <returns></returns>
        private GetInvoiceStatisticsResp InvoiceStatisticsCompute(decimal TransAmt, decimal InvoicesAmt)
        {
            var temp = TransAmt - InvoicesAmt;
            return new GetInvoiceStatisticsResp()
            {
                TransAmt = TransAmt,
                InvoicesAmt = InvoicesAmt,
                Invoicable = temp > 0 ? temp : 0,
                OutInvoice = temp > 0 ? 0 : -1 * temp
            };
        }

        /// <summary>
        /// 获取【项目】充值/退款动账记录的筛选条件
        /// </summary>
        /// <param name="req"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        private (ExpressionStarter<Ums_Callback_Trans> predicate, string subAcc) GetTransPredicate_Pro(GetStatisticsForProReq req, NkpContext context)
        {
            var subModel = context.SubAccount.FirstOrDefault(x => x.ExternalUserNo == req.ProjectId);
            string subAcc = subModel?.AccountNumber ?? string.Empty,
                parentAcc = subModel?.ParentAccountNo ?? string.Empty;

            //todo:怎么将项目与动账记录关联呢
            var predicate = PredicateBuilder.New<Ums_Callback_Trans>(x => (x.InAcctNo != x.OutAcctNo)
            && (
                (x.InAcctNo == subAcc && "201741".Equals(x.TransType))//充值
                || (x.OutAcctNo == subAcc && "201745".Equals(x.TransType) && "Refund".Equals(x.TransNote))//退款到基本户
                )
            );
            return (predicate, subAcc);
        }

        /// <summary>
        /// 根据商户获取服务奖金相关的 充值/退款动账记录的筛选条件
        /// </summary>
        /// <param name="context"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        private ExpressionStarter<Ums_Callback_Trans> GetTransPredicate_Ums(NkpContext context, GetStatisticsForUmsReq req)
        {
            //todo:怎么获取服务奖金的动账呢

            //获取开票商户
            var AcctNo = GetAccnoByMerchantId(context, req.MerchantId!);

            //获取购方商户
            var buyreAcctNo = GetAccnoByMerchantId(context, req.BuyerMerchantId!, false)
                ?? throw new BadRequestException("购方商户不存在");

            var predicate = PredicateBuilder.New<Ums_Callback_Trans>(x => x.InAcctNo == AcctNo && x.OutAcctNo == buyreAcctNo && "201667".Equals(x.TransType));
            return predicate;
        }

        /// <summary>
        /// 发起开票
        /// </summary>
        /// <param name="req"></param>
        /// <param name="UseScence">应用场景</param>
        /// <param name="ProjectId"></param>
        /// <param name="voucher"></param>
        /// <param name="IssuingCompany">本方公司名</param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        private async Task<InitiateInvoiceResponse?> CreateInvoice(InitiateInvoiceReq req, InvoiceRecordUseScence UseScence, string? ProjectId, Entity.Nkp.Voucher? voucher, string? IssuingCompany, NdnInvoiceInfo? BuyerInvoiceInfo = null)
        {
            //验证参数
            ParamsValidation.Validate(req);
            if (string.IsNullOrEmpty(req.Email) &&
                (req.PushMode == Config.Enums.Nkp.NovaPinInvoicePushMode.邮箱 || req.PushMode == Config.Enums.Nkp.NovaPinInvoicePushMode.邮箱和手机))
                throw new BadRequestException($"推送邮箱为空，请检查输入，或修改 推送方式");
            if (string.IsNullOrEmpty(req.BuyerPhone) &&
                (req.PushMode == Config.Enums.Nkp.NovaPinInvoicePushMode.手机 || req.PushMode == Config.Enums.Nkp.NovaPinInvoicePushMode.邮箱和手机))
                throw new BadRequestException($"推送手机为空，请检查输入，或修改 推送方式");
            if ((req.ShowBankAccountType == Config.Enums.Nkp.NovaPinInvoiceShowBankAccountType.备注仅显示购方开户行及账号 || req.ShowBankAccountType == Config.Enums.Nkp.NovaPinInvoiceShowBankAccountType.购销方开户行及账号都显示))
            {
                if (string.IsNullOrEmpty(req.BuyerAccount) && string.IsNullOrEmpty(req.BuyerAccountName))
                    throw new BadRequestException($"开户行和账号为空，请检查输入，或修改 显示开户行及账号方式");
            }
            if ((req.ShowAddressTelType == Config.Enums.Nkp.NovaPinInvoiceShowAddressTelType.备注仅显示购方地址及电话 || req.ShowAddressTelType == Config.Enums.Nkp.NovaPinInvoiceShowAddressTelType.购销方地址及电话都显示))
            {
                if (string.IsNullOrEmpty(req.BuyerAddress) && string.IsNullOrEmpty(req.BuyerTel))
                    throw new BadRequestException($"地址和电话为空，请检查输入，或修改 显示地址及电话方式");
            }

            //验证交易流水是否存在，是否已开票
            CheckTrans(req.TransIDs!);

            //验证开票金额
            GetInvoiceStatisticsResp? statistics = null;
            if (UseScence == InvoiceRecordUseScence.项目发票)
                statistics = GetInvoiceStatisticsForPro(new GetStatisticsForProReq() { ProjectId = ProjectId! });

            if (statistics != null)
            {
                if (statistics.Invoicable == 0)
                    throw new BadRequestException($"可开票金额为0，无需开票");
                else if (req.Amount > statistics.Invoicable)
                    throw new BadRequestException($"开票金额不能大于可开票金额({statistics.Invoicable})");
            }

            //补充信息
            //1.当前操作人的员工工号
            (string? jobNo, string? Name) = UseScence == InvoiceRecordUseScence.项目发票 ? _commonUserService.GetUserNo(_user.Id) : (BuyerInvoiceInfo!.InvoiceOperatorNo, BuyerInvoiceInfo.InvoiceOperatorName);
            if (string.IsNullOrEmpty(jobNo))
                throw new BadRequestException($"未检测到当前用户绑定的工号，请绑定工号后重试");
            //2.申请开票记录的ID
            Invoice_Record model = new()
            {
                UserId = _user.Id,
                AgentHrName = Name ?? string.Empty,
                ProjectId = ProjectId ?? string.Empty,
                VoucherId = voucher?.Id ?? string.Empty,
                MerchantId = voucher?.InMerchantId ?? string.Empty,
                BuyerMerchantId = voucher?.OutMerchantId ?? string.Empty,
                ContractCode = req.ContractCode,
                AgentHrNo = jobNo,
                Amount = req.Amount,
                Unit = req.Unit,
                InvoiceType = req.InvoiceType,
                BuyerTaxnum = req.BuyerTaxnum,
                BuyerName = req.BuyerName,
                BuyerAddress = req.BuyerAddress,
                BuyerTel = req.BuyerTel,
                BuyerAccountName = req.BuyerAccountName,
                BuyerAccount = req.BuyerAccount,
                InvoiceName = req.InvoiceName,
                GoodsCode = req.GoodsCode,
                VatRate = req.VatRate.ToString(),
                WithTaxFlag = req.WithTaxFlag,
                FavouredpolicyFlag = req.FavouredpolicyFlag,
                SpecType = req.SpecType,
                PushMode = req.PushMode,
                BuyerPhone = req.BuyerPhone,
                Email = req.Email,
                ShowBankAccountType = req.ShowBankAccountType,
                ShowAddressTelType = req.ShowAddressTelType,
                ShowCheckerType = req.ShowCheckerType,
                Remark = req.Remark,
                UseScence = UseScence,
                IssuingCompany = IssuingCompany ?? string.Empty
            };

            //请求数字诺亚
            InitiateInvoiceResponse? novaResult = null;
            try
            {
                novaResult = await _novaPinApi.InitiateInvoice(new InitiateInvoiceRequest(req, model.Id, jobNo));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"发起开票，请求数字诺亚引发异常，参数: {req.ToJson()}");
                throw new BadRequestException($"发起开票请求异常，请联系管理员");
            }

            if (novaResult == null)
                throw new BadRequestException($"发起开票请求失败，请联系管理员");
            else if (novaResult.Code != 200)
                throw new BadRequestException($"发起开票请求失败，原因：{(novaResult.Msg.Contains("合同") ? (req.ContractCode + novaResult.Msg) : novaResult.Msg)}");

            //记录请求结果
            using var context = _contextFactory.CreateDbContext();
            context.Invoice_Record.Add(model);

            //记录发票明细
            if (req.TransIDs != null && req.TransIDs.Count > 0)
                context.Invoice_Detail.AddRange(req.TransIDs!.Select(x => new Invoice_Detail
                {
                    RecordId = model.Id,
                    UmsTransId = x
                }));

            await context.SaveChangesAsync();

            return novaResult;
        }

        /// <summary>
        /// 验证交易流水是否存在，是否已开发票
        /// </summary>
        /// <param name="transIds"></param>
        private void CheckTrans(List<string>? transIds)
        {
            if (transIds == null || transIds.Count == 0)
                return;

            using var context = _contextFactory.CreateDbContext();
            var trans = context.Ums_Callback_Trans.Where(x => transIds.Contains(x.AppSsn)).ToList();
            //验证交易流水是否存在
            if (trans.Count != transIds.Count)
                throw new BadRequestException($"部分交易流水不存在，请刷新页面后重试");

            //验证交易流水是否已开发票
            if (context.Invoice_Record.Any(x => (x.Status == InvoiceRecordStatus.已申请 || x.Status == InvoiceRecordStatus.处理完成)
                 && x.Invoice_Detail.Any(d => transIds.Contains(d.UmsTransId))))
                throw new BadRequestException($"部分交易流水已开发票，请刷新页面后重试");
        }

        /// <summary>
        /// 根据MerchantId获取AcctNo
        /// </summary>
        /// <param name="context"></param>
        /// <param name="MerchantId"></param>
        /// <param name="IsThrowNullEx"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        private string? GetAccnoByMerchantId(NkpContext context, string MerchantId, bool IsThrowNullEx = true)
        {
            var model = context.Ums_Complex_Upload.Where(x => x.MerchantId == MerchantId).FirstOrDefault();
            if (model == null && IsThrowNullEx)
                throw new BadRequestException("企业尚无银联入网信息");

            return model?.BankAcctNo;
        }

        /// <summary>
        /// 获取项目的默认合同标识
        /// </summary>
        /// <param name="context"></param>
        /// <param name="ProjectId">项目ID</param>
        /// <returns>合同号</returns>
        private (string? ContractNo, string? Participant, string? Initiator) GetContractByPro(NkpContext context, string ProjectId)
        {
            var con = context.Project_Contract.Where(x => x.ProjectId == ProjectId && x.IsDefault == 1).OrderByDescending(x => x.EndTime).FirstOrDefault();
            if (con == null)
                throw new BadRequestException($"项目尚未关联合同或合同未设置默认，请关联后重试");
            return (con?.ContractNo, con?.Participant, con?.Initiator);
        }

        /// <summary>
        /// 获取MerchantId关联的合同标识
        /// </summary>
        /// <param name="context"></param>
        /// <param name="voucher">凭证Model</param>
        /// <param name="IsThrowNullEx">商户未入网时，是否报错提醒</param>
        /// <returns>合同号</returns>
        private (string? ContractNo, NdnInvoiceInfo? InvoiceInfo) GetContractByMerchantId(NkpContext context, Voucher voucher, bool IsThrowNullEx)
        {
            var NuoPinMerchant = CheckIsNPMerchant(voucher);

            EnterpriseMerchant? merchant;
            switch (NuoPinMerchant)
            {
                case VoucherNuoPinMerchant.OUT://诺聘是出金方
                    merchant = context.EnterpriseMerchant.Where(x => x.Id == voucher.InMerchantId!).FirstOrDefault();
                    if (merchant == null && IsThrowNullEx)
                        throw new BadRequestException("企业尚未配置发票信息");
                    var npMerchant = context.EnterpriseMerchant.Where(x => x.Id == voucher.OutMerchantId!).FirstOrDefault();
                    if (npMerchant == null && IsThrowNullEx)
                        throw new BadRequestException("诺聘平台尚未配置发票信息");
                    return (merchant?.PartyBContractNo, npMerchant?.InvoiceInfo);

                case VoucherNuoPinMerchant.IN:
                case VoucherNuoPinMerchant.NONE:
                default:
                    merchant = context.EnterpriseMerchant.Where(x => x.Id == voucher.OutMerchantId!).FirstOrDefault();
                    if (merchant == null && IsThrowNullEx)
                        throw new BadRequestException("企业尚未配置发票信息");
                    return (merchant?.PartyAContractNo, merchant?.InvoiceInfo);
            }
        }

        /// <summary>
        /// 验证哪一方不是诺聘平台的商户
        /// </summary>
        /// <param name="voucher"></param>
        /// <returns>返回不是诺聘平台商户的ID</returns>、
        private VoucherNuoPinMerchant CheckIsNPMerchant(Voucher voucher)
        {
            if (voucher.InMerchantId == "290688983965442309" || voucher.InMerchantName == "河北诺聘网络科技有限公司")
                return VoucherNuoPinMerchant.IN;
            else if (voucher.OutMerchantId == "290688983965442309" || voucher.OutMerchantName == "河北诺聘网络科技有限公司")
                return VoucherNuoPinMerchant.OUT;
            return VoucherNuoPinMerchant.NONE;
        }

        /// <summary>
        /// 获取发票产品
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        private Invoice_Product GetProduct(NkpContext context)
        {
            return context.Invoice_Product.FirstOrDefault() ?? throw new BadRequestException($"未获取到发票关联商品信息，请关联后重试");
        }
    }
}
