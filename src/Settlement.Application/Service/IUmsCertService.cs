﻿using Entity.Nkp;
using Infrastructure.Common;
using Infrastructure.Exceptions;
using Microsoft.EntityFrameworkCore.Internal;
using Settlement.Domain.Transaction;
using Settlement.Infrastructure.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Settlement.Application.Service
{
    /// <summary>
    /// 企业支付账户证书表
    /// </summary>
    public interface IUmsCertService
    {
        /// <summary>
        /// 保存证书
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        Task<Ums_Cert> Save(UmsCerReq req);

        /// <summary>
        /// 获取证书
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        Ums_Cert? GetCert(GetUmsCerReq req);

        /// <summary>
        /// 查询单位账户支付账户的证书是否存在
        /// </summary>
        /// <param name="AccountNo"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        UmsCerRespWithKey GetCertByANo(string AccountNo);

        /// <summary>
        /// 查询子账户上级支付账户的证书是否存在
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        UmsCerRespWithKey? GetCertByPro(string ProjectId);

        /// <summary>
        /// 查询诺聘支付账户证书是否存在
        /// </summary>
        /// <returns></returns>
        /// <exception cref="BadRequestException"></exception>
        UmsCerRespWithKey? GetCertByNuoPin();
    }
}
