﻿using Config;
using Entity;
using Infrastructure.Common;
using Infrastructure.Extend;
using Infrastructure.Proxy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

namespace Settlement.Application.Service;

[Service(ServiceLifetime.Transient)]
public class CommonUserService
{
    private IDbContextFactory<NkpContext> _staffingContextFactory;
    private ConfigManager _config;
    private readonly IHostEnvironment _hostingEnvironment;
    private readonly LogManager _log;
    private readonly NuoPinApi _nuoPinApi;
    private RequestContext _user;

    public CommonUserService(IDbContextFactory<NkpContext> staffingContextFactory,
        IOptionsSnapshot<ConfigManager> config,
        IHostEnvironment hostingEnvironment, 
        LogManager log, 
        NuoPinApi nuoPinApi,
        RequestContext user)
    {
        _staffingContextFactory = staffingContextFactory;
        _config = config.Value;
        _hostingEnvironment = hostingEnvironment;
        _log = log;
        _nuoPinApi = nuoPinApi;
        _user = user;
    }

    /// <summary>
    /// 查询用户工号
    /// </summary>
    /// <param name="hrId"></param>
    public (string? jobNo, string? Name) GetUserNo(string hrId)
    {
        using var context = _staffingContextFactory.CreateDbContext();
        var jobNo = context.User_DingDing.Where(x => x.UserId == hrId).Select(s => new { JobNumber = s.DingJobNo, s.DingName }).FirstOrDefault();
        if (string.IsNullOrWhiteSpace(jobNo?.JobNumber))
            jobNo = context.User.Where(x => x.UserId == hrId).Select(s => new { JobNumber = s.Dd_User.JobNumber, DingName = s.Dd_User.Name }).FirstOrDefault();

        return (jobNo?.JobNumber, jobNo?.DingName);
    }
}
