using Infrastructure.Extend;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Settlement.Domain.OutboxService;

namespace Settlement.Application.Service;

/// <summary>
/// 消息处理服务
/// </summary>
[Service(ServiceLifetime.Scoped)]
public class MessageProcessingService : IMessageProcessingService
{
    private readonly IOutboxService _outboxService;
    private readonly IApiCallService _apiCallService;
    private readonly ILogger<MessageProcessingService> _logger;

    public MessageProcessingService(
        IOutboxService outboxService,
        IApiCallService apiCallService,
        ILogger<MessageProcessingService> logger)
    {
        _outboxService = outboxService;
        _apiCallService = apiCallService;
        _logger = logger;
    }

    /// <summary>
    /// 处理待发送的消息（调用第三方API）
    /// </summary>
    /// <returns></returns>
    public async Task ProcessPendingMessagesAsync()
    {
        try
        {
            // 获取待处理的消息
            var pendingMessages = await _outboxService.GetPendingMessagesAsync(10);

            foreach (var message in pendingMessages)
            {
                try
                {
                    // 标记为处理中
                    await _outboxService.MarkAsProcessingAsync(message.Id);

                    // 调用第三方API
                    var success = await _apiCallService.ProcessMessageAsync(message);

                    if (success)
                    {
                        // 标记为已完成
                        await _outboxService.MarkAsCompletedAsync(message.Id);
                        _logger.LogInformation("Successfully processed message {MessageId} of type {MessageType}", message.Id, message.MessageType);
                    }
                    else
                    {
                        // 标记为失败
                        await _outboxService.MarkAsFailedAsync(message.Id, "API call failed");
                        _logger.LogWarning("Failed to process message {MessageId} of type {MessageType}: API call failed", message.Id, message.MessageType);
                    }
                }
                catch (Exception ex)
                {
                    // 标记为失败
                    await _outboxService.MarkAsFailedAsync(message.Id, ex.Message);
                    _logger.LogError(ex, "Failed to process message {MessageId} of type {MessageType}", message.Id, message.MessageType);
                }
            }

            // 处理重试消息
            await ProcessRetryMessagesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing pending messages");
        }
    }

    /// <summary>
    /// 处理重试消息
    /// </summary>
    /// <returns></returns>
    public async Task ProcessRetryMessagesAsync()
    {
        try
        {
            var retryMessages = await _outboxService.GetRetryMessagesAsync(5);

            foreach (var message in retryMessages)
            {
                try
                {
                    // 标记为处理中
                    await _outboxService.MarkAsProcessingAsync(message.Id);

                    // 重新调用第三方API
                    var success = await _apiCallService.ProcessMessageAsync(message);

                    if (success)
                    {
                        // 标记为已完成
                        await _outboxService.MarkAsCompletedAsync(message.Id);
                        _logger.LogInformation("Successfully retried message {MessageId} of type {MessageType}", message.Id, message.MessageType);
                    }
                    else
                    {
                        // 标记为失败
                        await _outboxService.MarkAsFailedAsync(message.Id, "API call failed on retry");
                        _logger.LogWarning("Failed to retry message {MessageId} of type {MessageType}: API call failed", message.Id, message.MessageType);
                    }
                }
                catch (Exception ex)
                {
                    // 标记为失败
                    await _outboxService.MarkAsFailedAsync(message.Id, ex.Message);
                    _logger.LogError(ex, "Failed to retry message {MessageId} of type {MessageType}", message.Id, message.MessageType);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing retry messages");
        }
    }



    /// <summary>
    /// 清理已完成的消息
    /// </summary>
    /// <param name="olderThanDays">保留天数</param>
    /// <returns></returns>
    public async Task<int> CleanupCompletedMessagesAsync(int olderThanDays = 100000)
    {
        try
        {
            //var cleanedCount = await _outboxService.CleanupCompletedMessagesAsync(olderThanDays);
            _logger.LogInformation("Cleaned up {CleanedCount} completed messages older than {OlderThanDays} days", 1, olderThanDays);
            return 1;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while cleaning up completed messages");
            return 0;
        }
    }
}

/// <summary>
/// 消息处理服务接口
/// </summary>
public interface IMessageProcessingService
{
    /// <summary>
    /// 处理待发送的消息
    /// </summary>
    /// <returns></returns>
    Task ProcessPendingMessagesAsync();

    /// <summary>
    /// 处理重试消息
    /// </summary>
    /// <returns></returns>
    Task ProcessRetryMessagesAsync();

    /// <summary>
    /// 清理已完成的消息
    /// </summary>
    /// <param name="olderThanDays">保留天数</param>
    /// <returns></returns>
    Task<int> CleanupCompletedMessagesAsync(int olderThanDays = 10000000);
}


