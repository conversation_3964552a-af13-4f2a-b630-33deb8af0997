﻿using Config;
using Config.CommonModel;
using Entity;
using Infrastructure.Extend;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Core;
using ServiceStack;
using Settlement.Infrastructure.Proxy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Config.Enums.Nkp;

namespace Settlement.Application.Service
{
    /// <summary>
    /// 数字诺亚回调处理
    /// </summary>
    [Service(ServiceLifetime.Scoped)]
    public class NdnService : INdnService
    {
        private readonly IDbContextFactory<NkpContext> _contextFactory;
        private readonly OpenChinaumsApi _openChinaumsApi;
        private readonly ILogger<NdnService> _logger;
        private readonly RequestContext _user;
        private readonly ConfigManager _config;
        private readonly NovaPinApi _novaPinApi;
        private readonly CommonUserService _commonUserService;
        public NdnService(
            IDbContextFactory<NkpContext> contextFactory,
            OpenChinaumsApi openChinaumsApi,
            ILogger<NdnService> logger,
            IOptionsSnapshot<ConfigManager> config,
            RequestContext user,
            NovaPinApi novaPinApi,
            CommonUserService commonUserService)
        {
            _contextFactory = contextFactory;
            _openChinaumsApi = openChinaumsApi;
            _logger = logger;
            _user = user;
            _config = config.Value;
            _novaPinApi = novaPinApi;
            _commonUserService = commonUserService;
        }

        public EmptyResponse NdnNotify(NdnNotify req)
        {
            _logger.LogInformation("数字诺亚通知:通知内容如下：{req}", req.ToJson());
            var result = new EmptyResponse();

            try
            {
                Config.Enums.Nkp.InvoiceRecordStatus ndnAuditStatus;
                switch (req.Status)
                {
                    case NdnNotifyStatus.成功:
                        ndnAuditStatus = Config.Enums.Nkp.InvoiceRecordStatus.处理完成;
                        break;
                    case NdnNotifyStatus.失败:
                        ndnAuditStatus = Config.Enums.Nkp.InvoiceRecordStatus.处理失败;
                        break;
                    default:
                        ndnAuditStatus = Config.Enums.Nkp.InvoiceRecordStatus.处理失败;
                        string temp = "Status=" + req.Status.ToString();
                        if (string.IsNullOrEmpty(req.Remark))
                            req.Remark = temp;
                        else
                            req.Remark += ":" + temp;
                        break;
                };

                using var context = _contextFactory.CreateDbContext();
                switch (req.Type)
                {
                    case NdnTodoType.开发票:
                        HandleInvoiceIssuance(req, ndnAuditStatus, context);
                        break;
                    case NdnTodoType.发放服务奖金:
                        HandleServiceBonus(req, ndnAuditStatus, context);
                        break;
                }

                context.SaveChanges();
            }
            catch (Exception ee)
            {
                _logger.LogError(ee, "数字诺亚通知:通知内容如下：{req}，引发异常", req.ToJson());
            }
            return result;
        }

        /// <summary>
        /// 申请开票回调处理
        /// </summary>
        /// <param name="model"></param>
        /// <param name="Status"></param>
        /// <param name="context"></param>
        /// <exception cref="Exception"></exception>
        private void HandleInvoiceIssuance(NdnNotify model, Config.Enums.Nkp.InvoiceRecordStatus Status, NkpContext context)
        {
            var invoice = context.Invoice_Record.FirstOrDefault(x => x.Id == model.OrderId);
            if (invoice == null)
            {
                _logger.LogError($"数字诺亚通知：申请开票记录不存在，发票记录ID{model.OrderId!}，推送过来的数据:{model.ToJson()}");
                throw new Exception($"数字诺亚通知：申请开票记录不存在");
            }

            if (invoice.Status == Config.Enums.Nkp.InvoiceRecordStatus.处理完成)
                return;

            invoice.Status = Status;
            invoice.UpdateTime = DateTime.Now;
            invoice.InvoicePDFUrl = model.InvoiceUrl.ToJson();
            invoice.CallBackRemark = model.Remark ?? string.Empty;

            //更新凭证表的开票状态
            if(invoice.UseScence== InvoiceRecordUseScence.服务奖金)
            {
                var voucher= context.Voucher.FirstOrDefault(x=>x.Id==invoice.VoucherId);
                if (voucher != null)
                {
                    voucher.InvoiceStatus = Status == InvoiceRecordStatus.处理完成 ? VoucherInvoiceStatus.开票成功 : VoucherInvoiceStatus.开票失败;
                    if (Status != InvoiceRecordStatus.处理完成)
                        voucher.InvoiceRemark = (model.Remark?.Length > 200 ? model.Remark.Substring(0, 200) : model.Remark) ?? string.Empty;
                }
            }
        }


        private void HandleServiceBonus(NdnNotify model, Config.Enums.Nkp.InvoiceRecordStatus status, NkpContext context)
        {
            var transfer = context.Post_Settlement_Detail.FirstOrDefault(x => x.Id == model.OrderId);
            if (transfer == null)
            {
                _logger.LogError($"数字诺亚通知：服务奖金不存在，OrderId{model.OrderId!}，推送过来的数据:{model.ToJson()}");
                throw new Exception($"数字诺亚通知：服务奖金不存在");
            }
            else
            {
                if (status == InvoiceRecordStatus.处理失败)
                {
                    var serviceBonusRecord = context.Service_Bonus_Record.FirstOrDefault(x => x.OrderNumber == model.OrderId);
                    if (serviceBonusRecord != null)
                    {
                        serviceBonusRecord.Status = ServiceBonusStatus.被驳回;
                    }

                    context.SaveChanges();
                    _logger.LogError($"数字诺亚通知：服务奖金处理失败，OrderId{model.OrderId!}，原因:{model.Remark}");
                    throw new Exception($"数字诺亚通知：服务奖金处理失败");

                }
                else
                {
                    var serviceBonusRecord = context.Service_Bonus_Record.FirstOrDefault(x => x.OrderNumber == model.OrderId);
                    if (serviceBonusRecord != null)
                    {
                        serviceBonusRecord.Status = ServiceBonusStatus.已完成;
                    }
                    context.SaveChanges();
                    
                }

            }
        }



    }
}
