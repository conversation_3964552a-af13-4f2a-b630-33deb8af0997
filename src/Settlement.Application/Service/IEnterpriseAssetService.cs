using Settlement.Domain.Transaction;

namespace Settlement.Application.Service;

/// <summary>
/// 企业资产服务接口
/// </summary>
public interface IEnterpriseAssetService
{
    /// <summary>
    /// 获取企业资产概览
    /// </summary>
    /// <param name="request">企业资产概览请求</param>
    /// <returns>企业资产概览响应</returns>
    Task<EnterpriseAssetOverviewResp> GetEnterpriseAssetOverview(EnterpriseAssetOverviewReq request);

    /// <summary>
    /// 获取商户提现信息
    /// </summary>
    /// <param name="request">商户提现信息请求</param>
    /// <returns>商户提现信息响应</returns>
    Task<MerchantWithdrawInfoResp> GetMerchantWithdrawInfo(MerchantWithdrawInfoReq request);
}